"""
文件缓存实现

基于文件系统的持久化缓存实现。
"""

import asyncio
import hashlib
import json
import os
import pickle
import time
from pathlib import Path
from typing import Any, Dict, List, Optional
import threading
import fnmatch

from .interface import CacheInterface


class FileCache(CacheInterface):
    """文件缓存实现"""
    
    def __init__(
        self, 
        cache_dir: str = "data/cache", 
        default_ttl: int = 86400,
        max_size: int = 1000,
        cleanup_interval: int = 3600
    ):
        """
        初始化文件缓存
        
        Args:
            cache_dir: 缓存目录
            default_ttl: 默认TTL(秒)
            max_size: 最大缓存文件数
            cleanup_interval: 清理间隔(秒)
        """
        self.cache_dir = Path(cache_dir)
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.cleanup_interval = cleanup_interval
        
        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0,
            'expired': 0,
            'errors': 0
        }
        
        # 启动清理任务
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动定期清理任务"""
        async def cleanup_expired():
            while True:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired()
        
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(cleanup_expired())
        except RuntimeError:
            # 如果没有事件循环，跳过清理任务
            pass
    
    def _get_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名冲突和长度问题
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def _get_meta_path(self, key: str) -> Path:
        """获取元数据文件路径"""
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.meta"
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            with self._lock:
                file_path = self._get_file_path(key)
                meta_path = self._get_meta_path(key)
                
                if not file_path.exists() or not meta_path.exists():
                    self._stats['misses'] += 1
                    return None
                
                # 读取元数据
                with open(meta_path, 'r') as f:
                    meta = json.load(f)
                
                # 检查是否过期
                if self._is_expired(meta):
                    self._delete_files(file_path, meta_path)
                    self._stats['expired'] += 1
                    self._stats['misses'] += 1
                    return None
                
                # 读取缓存值
                with open(file_path, 'rb') as f:
                    value = pickle.load(f)
                
                # 更新访问时间
                meta['last_access'] = time.time()
                with open(meta_path, 'w') as f:
                    json.dump(meta, f)
                
                self._stats['hits'] += 1
                return value
                
        except Exception as e:
            self._stats['errors'] += 1
            self._stats['misses'] += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        try:
            with self._lock:
                if ttl is None:
                    ttl = self.default_ttl
                
                file_path = self._get_file_path(key)
                meta_path = self._get_meta_path(key)
                
                # 检查是否需要驱逐
                if not file_path.exists():
                    await self._check_and_evict()
                
                # 创建元数据
                meta = {
                    'key': key,
                    'expire_time': time.time() + ttl if ttl > 0 else None,
                    'created_time': time.time(),
                    'last_access': time.time(),
                    'size': 0
                }
                
                # 写入缓存值
                with open(file_path, 'wb') as f:
                    pickle.dump(value, f)
                
                # 获取文件大小
                meta['size'] = file_path.stat().st_size
                
                # 写入元数据
                with open(meta_path, 'w') as f:
                    json.dump(meta, f)
                
                self._stats['sets'] += 1
                return True
                
        except Exception as e:
            self._stats['errors'] += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            with self._lock:
                file_path = self._get_file_path(key)
                meta_path = self._get_meta_path(key)
                
                if file_path.exists() or meta_path.exists():
                    self._delete_files(file_path, meta_path)
                    self._stats['deletes'] += 1
                    return True
                return False
                
        except Exception as e:
            self._stats['errors'] += 1
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            with self._lock:
                file_path = self._get_file_path(key)
                meta_path = self._get_meta_path(key)
                
                if not file_path.exists() or not meta_path.exists():
                    return False
                
                # 读取元数据检查过期
                with open(meta_path, 'r') as f:
                    meta = json.load(f)
                
                if self._is_expired(meta):
                    self._delete_files(file_path, meta_path)
                    self._stats['expired'] += 1
                    return False
                
                return True
                
        except Exception as e:
            self._stats['errors'] += 1
            return False
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        try:
            with self._lock:
                for file_path in self.cache_dir.glob("*.cache"):
                    file_path.unlink(missing_ok=True)
                
                for meta_path in self.cache_dir.glob("*.meta"):
                    meta_path.unlink(missing_ok=True)
                
                return True
                
        except Exception as e:
            self._stats['errors'] += 1
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            # 计算缓存文件数量和总大小
            cache_files = list(self.cache_dir.glob("*.cache"))
            total_size = sum(f.stat().st_size for f in cache_files if f.exists())
            
            return {
                'backend': 'file',
                'cache_dir': str(self.cache_dir),
                'size': len(cache_files),
                'max_size': self.max_size,
                'total_size_bytes': total_size,
                'hit_rate': round(hit_rate, 4),
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'sets': self._stats['sets'],
                'deletes': self._stats['deletes'],
                'evictions': self._stats['evictions'],
                'expired': self._stats['expired'],
                'errors': self._stats['errors']
            }
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的所有键"""
        try:
            with self._lock:
                keys = []
                for meta_path in self.cache_dir.glob("*.meta"):
                    try:
                        with open(meta_path, 'r') as f:
                            meta = json.load(f)
                        
                        key = meta.get('key', '')
                        if pattern == "*" or fnmatch.fnmatch(key, pattern):
                            # 检查是否过期
                            if not self._is_expired(meta):
                                keys.append(key)
                            else:
                                # 删除过期文件
                                file_path = self._get_file_path(key)
                                self._delete_files(file_path, meta_path)
                                self._stats['expired'] += 1
                    except:
                        continue
                
                return keys
                
        except Exception as e:
            self._stats['errors'] += 1
            return []
    
    async def ttl(self, key: str) -> Optional[int]:
        """获取缓存剩余过期时间"""
        try:
            with self._lock:
                meta_path = self._get_meta_path(key)
                
                if not meta_path.exists():
                    return None
                
                with open(meta_path, 'r') as f:
                    meta = json.load(f)
                
                if meta.get('expire_time') is None:
                    return -1  # 永不过期
                
                remaining = meta['expire_time'] - time.time()
                return max(0, int(remaining))
                
        except Exception as e:
            self._stats['errors'] += 1
            return None
    
    def _is_expired(self, meta: Dict[str, Any]) -> bool:
        """检查缓存是否过期"""
        expire_time = meta.get('expire_time')
        if expire_time is None:
            return False
        return time.time() > expire_time
    
    def _delete_files(self, file_path: Path, meta_path: Path):
        """删除缓存文件和元数据文件"""
        try:
            file_path.unlink(missing_ok=True)
            meta_path.unlink(missing_ok=True)
        except:
            pass
    
    async def _check_and_evict(self):
        """检查并驱逐旧缓存"""
        cache_files = list(self.cache_dir.glob("*.cache"))
        
        if len(cache_files) >= self.max_size:
            # 获取所有文件的访问时间
            file_times = []
            for cache_file in cache_files:
                meta_path = cache_file.with_suffix('.meta')
                try:
                    if meta_path.exists():
                        with open(meta_path, 'r') as f:
                            meta = json.load(f)
                        file_times.append((
                            meta.get('last_access', 0),
                            cache_file,
                            meta_path
                        ))
                except:
                    continue
            
            # 按访问时间排序，删除最旧的文件
            file_times.sort(key=lambda x: x[0])
            
            # 删除最旧的文件直到低于最大数量
            evict_count = len(file_times) - self.max_size + 1
            for i in range(min(evict_count, len(file_times))):
                _, cache_file, meta_path = file_times[i]
                self._delete_files(cache_file, meta_path)
                self._stats['evictions'] += 1
    
    async def _cleanup_expired(self):
        """清理过期的缓存文件"""
        try:
            with self._lock:
                current_time = time.time()
                
                for meta_path in self.cache_dir.glob("*.meta"):
                    try:
                        with open(meta_path, 'r') as f:
                            meta = json.load(f)
                        
                        if self._is_expired(meta):
                            key = meta.get('key', '')
                            file_path = self._get_file_path(key)
                            self._delete_files(file_path, meta_path)
                            self._stats['expired'] += 1
                    except:
                        continue
                        
        except Exception as e:
            self._stats['errors'] += 1
    
    def __del__(self):
        """析构函数，取消清理任务"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
