"""
页面管理器模块
统一管理所有17个页面的路由和错误处理
"""

import time
import traceback
from typing import Callable, Dict, Optional

import requests
import streamlit as st

from .error_handler import E<PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorLogger


class PageManager:
    """页面管理和路由类"""
    
    def __init__(self):
        """初始化页面管理器"""
        self.page_functions = self._initialize_page_functions()
        self.current_page = None
    
    def _initialize_page_functions(self) -> Dict[str, Callable]:
        """
        初始化页面函数映射
        
        Returns:
            页面名称到函数的映射字典
        """
        return {
            # 首页概览类
            "📈 数据概览": self._show_data_overview,
            "🎲 最新开奖": self._show_latest_draw,
            "📊 系统状态": self._show_system_status,

            # 智能预测类
            "🤖 智能融合预测": self._show_intelligent_fusion,
            "📈 趋势分析预测": self._show_trend_analysis,
            "🎯 预测分析": self._show_prediction_analysis,
            "🏛️ 模型库管理": self._show_model_library,

            # 数据分析类
            "🔍 数据查询": self._show_data_query,
            "📊 频率分析": self._show_frequency_analysis,
            "💰 销售分析": self._show_sales_analysis,
            "📈 和值分布": self._show_sum_distribution,

            # 系统管理类
            "🔄 数据更新": self._show_data_update,
            "📊 性能监控": self._show_real_time_monitoring,
            "💡 优化建议": self._show_optimization_suggestions,
            "🤖 模型训练": self._show_training_monitoring,

            # 高级功能类
            "🔬 特征工程": self._show_feature_engineering,
            "🧪 A/B测试": self._show_ab_testing,
            "📊 数据管理深度": self._show_data_management_deep,
            "🔍 系统诊断": self._show_bug_detection_status
        }
    
    def render_page(self, page_name: str) -> bool:
        """
        渲染指定页面

        Args:
            page_name: 页面名称

        Returns:
            页面渲染是否成功
        """
        if not page_name:
            return False

        self.current_page = page_name

        if page_name in self.page_functions:
            try:
                # 预检查系统状态
                system_status = self._check_system_status()

                # 显示页面加载状态
                with st.spinner(f"正在加载 {page_name}..."):
                    # 如果系统状态异常，显示警告但仍尝试加载
                    if not system_status['api_available']:
                        st.warning("⚠️ API服务连接异常，部分功能可能不可用")

                    if not system_status['data_available']:
                        st.warning("⚠️ 数据源连接异常，显示的数据可能不是最新的")

                    # 记录页面加载开始时间
                    import time
                    start_time = time.time()

                    # 执行页面函数
                    self.page_functions[page_name]()

                    # 计算加载时间并显示性能提示
                    load_time = time.time() - start_time
                    if load_time > 2.0:
                        st.success(f"⚡ 页面加载完成 (耗时: {load_time:.2f}秒)")
                    elif load_time > 1.0:
                        st.info(f"✅ 页面加载完成 (耗时: {load_time:.2f}秒)")
                return True

            except Exception as e:
                # 记录错误日志
                ErrorLogger.log_error(e, {
                    "page_name": page_name,
                    "system_status": system_status
                })

                # 显示用户友好的错误信息
                self._handle_page_error(page_name, e, system_status)
                return False
        else:
            st.error(f"❌ 未知页面: {page_name}")
            st.info("请检查页面名称是否正确，或联系系统管理员")
            return False
    
    def _check_system_status(self) -> dict:
        """
        检查系统状态

        修复说明：
        - 解决了API健康检查响应格式与前端期望格式不匹配的问题
        - API实际返回：{"status": "healthy", "database_records": 8343}
        - 前端之前期望：{"database": {"connected": true, "total_records": 8343}}
        - 修复后正确映射字段名，避免"数据源连接异常"误报

        Returns:
            系统状态字典，包含：
            - api_available: API服务是否可用
            - data_available: 数据是否可用（基于database_records > 0）
            - database_connected: 数据库是否连接（基于status == 'healthy'）
            - models_loaded: 模型是否已加载
        """
        status = {
            'api_available': False,
            'data_available': False,
            'database_connected': False,
            'models_loaded': False
        }

        try:
            # 检查API服务状态
            api_response = requests.get("http://127.0.0.1:8888/health", timeout=3)
            if api_response.status_code == 200:
                status['api_available'] = True
                health_data = api_response.json()

                # 检查数据库连接
                # 修复：API返回的是status字段，而不是database.connected
                if health_data.get('status') == 'healthy':
                    status['database_connected'] = True

                # 检查数据可用性
                # 修复：API返回的是database_records字段，而不是database.total_records
                if health_data.get('database_records', 0) > 0:
                    status['data_available'] = True

                # 检查模型状态
                if health_data.get('models', {}).get('loaded'):
                    status['models_loaded'] = True

        except:
            pass  # 静默处理连接错误

        return status

    def _handle_page_error(self, page_name: str, error: Exception, system_status: dict = None):
        """
        处理页面加载错误 - 增强版

        Args:
            page_name: 页面名称
            error: 错误对象
            system_status: 系统状态信息
        """
        st.error(f"❌ 页面加载失败: {page_name}")

        # 基于系统状态提供诊断信息
        if system_status:
            st.subheader("🔍 系统诊断")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                api_status = "✅ 正常" if system_status['api_available'] else "❌ 异常"
                st.metric("API服务", api_status)

            with col2:
                db_status = "✅ 已连接" if system_status['database_connected'] else "❌ 断开"
                st.metric("数据库", db_status)

            with col3:
                data_status = "✅ 可用" if system_status['data_available'] else "❌ 不可用"
                st.metric("数据源", data_status)

            with col4:
                model_status = "✅ 已加载" if system_status['models_loaded'] else "❌ 未加载"
                st.metric("模型", model_status)

        # 根据错误类型和系统状态提供解决建议
        st.warning("🔧 **解决建议**:")

        if isinstance(error, ImportError):
            st.markdown("**模块导入错误:**")
            st.markdown("- 检查页面模块文件是否存在")
            st.markdown("- 确认文件路径是否正确")
            st.markdown("- 验证模块依赖是否安装完整")

            # 检查具体的缺失模块
            error_msg = str(error).lower()
            if "pages_disabled" in error_msg:
                st.markdown("- 可能是页面模块文件缺失，请检查 `src/ui/pages_disabled/` 目录")

        elif isinstance(error, AttributeError):
            st.markdown("**属性错误:**")
            st.markdown("- 检查函数名称是否正确")
            st.markdown("- 确认模块是否正确导入")
            st.markdown("- 验证函数是否在模块中正确定义")

        elif "requests" in str(error).lower() or "connection" in str(error).lower():
            st.markdown("**网络连接错误:**")
            if system_status and not system_status['api_available']:
                st.markdown("- API服务(127.0.0.1:8888)未运行，请启动API服务")
                st.markdown("- 检查端口8888是否被占用")
            st.markdown("- 检查网络连接状态")
            st.markdown("- 确认防火墙设置")

        else:
            st.markdown("**通用解决方案:**")
            st.markdown("- 刷新页面重试")
            st.markdown("- 检查系统资源使用情况")
            st.markdown("- 重启相关服务")

            # 基于系统状态的具体建议
            if system_status:
                if not system_status['api_available']:
                    st.markdown("- 启动API服务: `python -m uvicorn main:app --host 127.0.0.1 --port 8888`")
                if not system_status['database_connected']:
                    st.markdown("- 检查数据库连接配置")
                if not system_status['data_available']:
                    st.markdown("- 执行数据更新操作")
                if not system_status['models_loaded']:
                    st.markdown("- 重新加载预测模型")

        # 提供快速恢复选项
        st.subheader("⚡ 快速恢复")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 重新加载页面"):
                st.rerun()

        with col2:
            if st.button("🏠 返回首页"):
                st.session_state.selected_page = "📈 数据概览"
                st.rerun()

        with col3:
            if st.button("🔍 系统检查"):
                self._show_system_diagnostic()

        # 显示详细错误信息（开发模式）
        with st.expander("🔍 详细错误信息 (开发调试用)"):
            st.code(f"错误类型: {type(error).__name__}")
            st.code(f"错误信息: {str(error)}")
            st.code(f"错误堆栈:\n{traceback.format_exc()}")

            if system_status:
                st.code(f"系统状态: {system_status}")

    def _show_system_diagnostic(self):
        """显示系统诊断信息"""
        st.subheader("🔍 系统诊断报告")

        # 重新检查系统状态
        status = self._check_system_status()

        # API服务检查
        st.write("**API服务检查:**")
        try:
            response = requests.get("http://127.0.0.1:8888/health", timeout=5)
            if response.status_code == 200:
                st.success("✅ API服务正常运行")
                health_data = response.json()
                st.json(health_data)
            else:
                st.error(f"❌ API服务响应异常: HTTP {response.status_code}")
        except Exception as e:
            st.error(f"❌ 无法连接到API服务: {str(e)}")
            st.info("请确保API服务在127.0.0.1:8888端口运行")

        # 数据源检查
        st.write("**数据源检查:**")
        try:
            data_response = requests.head("https://data.17500.cn/3d_asc.txt", timeout=10)
            if data_response.status_code == 200:
                st.success("✅ 数据源可访问")
                file_size = data_response.headers.get('content-length', 'N/A')
                if file_size != 'N/A':
                    st.info(f"数据文件大小: {int(file_size) / 1024:.1f} KB")
            else:
                st.warning(f"⚠️ 数据源响应异常: HTTP {data_response.status_code}")
        except Exception as e:
            st.error(f"❌ 无法访问数据源: {str(e)}")

        # 页面模块检查
        st.write("**页面模块检查:**")
        missing_modules = []

        module_checks = [
            ("prediction_analysis", "预测分析"),
            ("data_update", "数据更新"),
            ("frequency_analysis", "频率分析"),
            ("real_time_monitoring", "实时监控")
        ]

        for module_name, display_name in module_checks:
            try:
                exec(f"from ui.pages_disabled.{module_name} import show_{module_name}")
                st.success(f"✅ {display_name}模块正常")
            except ImportError:
                st.error(f"❌ {display_name}模块缺失")
                missing_modules.append(module_name)

        if missing_modules:
            st.warning(f"发现{len(missing_modules)}个缺失模块，请检查相关文件是否存在")

        # 系统建议
        st.write("**系统建议:**")
        if not status['api_available']:
            st.markdown("- 🚨 优先启动API服务")
        if not status['database_connected']:
            st.markdown("- 🔧 检查数据库连接")
        if not status['data_available']:
            st.markdown("- 📊 执行数据更新")
        if missing_modules:
            st.markdown("- 📁 补充缺失的页面模块文件")

        if all(status.values()) and not missing_modules:
            st.success("🎉 系统状态良好，所有组件正常运行！")
    
    def get_available_pages(self) -> list:
        """
        获取所有可用页面列表
        
        Returns:
            页面名称列表
        """
        return list(self.page_functions.keys())
    
    def get_current_page(self) -> Optional[str]:
        """
        获取当前页面名称
        
        Returns:
            当前页面名称
        """
        return self.current_page
    
    # ==================== 页面函数实现 ====================
    
    def _show_data_overview(self):
        """数据概览页面 - 保持原有实现"""
        # 调用原有的数据概览函数
        # 这个函数在main.py中已经定义
        st.markdown("## 📈 数据概览")
        st.info("数据概览页面通过main.py中的show_overview_page()函数实现")

    def _show_latest_draw(self):
        """最新开奖页面"""
        st.header("🎲 最新开奖")
        st.info("此功能将整合到数据概览页面中，显示最新的开奖信息")
        # 临时重定向到数据概览
        self._show_data_overview()

    def _show_system_status(self):
        """系统状态页面"""
        st.header("📊 系统状态")

        # 显示系统状态信息
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("API状态", "✅ 正常", "连接正常")

        with col2:
            st.metric("数据状态", "✅ 实时", "最新更新")

        with col3:
            st.metric("系统性能", "✅ 良好", "运行稳定")

        # 显示详细的系统信息
        st.subheader("详细状态")
        try:
            from ui.pages.real_time_monitoring import show_real_time_monitoring
            show_real_time_monitoring()
        except ImportError:
            st.info("实时监控功能暂时不可用")

    def _show_frequency_analysis(self):
        """频率分析页面"""
        from ui.pages_disabled.frequency_analysis import \
            show_frequency_analysis
        show_frequency_analysis()
    
    def _show_sum_distribution(self):
        """和值分布页面"""
        from ui.pages_disabled.sum_distribution import show_sum_distribution
        show_sum_distribution()
    
    def _show_sales_analysis(self):
        """销售分析页面"""
        from ui.pages_disabled.sales_analysis import show_sales_analysis
        show_sales_analysis()
    
    def _show_data_query(self):
        """数据查询页面"""
        from ui.pages_disabled.data_query import show_data_query
        show_data_query()
    
    def _show_prediction_analysis(self):
        """预测分析页面"""
        from ui.pages_disabled.prediction_analysis import \
            show_prediction_analysis
        show_prediction_analysis()
    
    def _show_intelligent_fusion(self):
        """智能融合优化页面"""
        try:
            from ui.pages.optimization_suggestions import \
                show_optimization_suggestions
            show_optimization_suggestions()
        except ImportError:
            st.error("智能融合优化模块不可用")
            st.info("请检查 ui/pages/optimization_suggestions.py 文件是否存在")
    
    def _show_trend_analysis(self):
        """趋势分析页面"""
        from ui.pages_disabled.trend_analysis import show_trend_analysis
        show_trend_analysis()
    
    def _show_model_library(self):
        """模型库页面"""
        from ui.pages_disabled.model_library import show_model_library
        show_model_library()
    
    def _show_data_update(self):
        """数据更新页面"""
        # 使用正确的数据更新组件，而不是pages_disabled中的错误版本
        from ui.data_update_components import \
            show_enhanced_data_management_page
        show_enhanced_data_management_page()
    
    def _show_real_time_monitoring(self):
        """实时监控页面"""
        try:
            from ui.pages_disabled.real_time_monitoring import \
                show_real_time_monitoring
            show_real_time_monitoring()
        except ImportError:
            st.error("实时监控模块不可用")
            st.info("请检查 ui/pages_disabled/real_time_monitoring.py 文件是否存在")
    
    def _show_optimization_suggestions(self):
        """优化建议页面"""
        try:
            from ui.pages.optimization_suggestions import \
                show_optimization_suggestions
            show_optimization_suggestions()
        except ImportError:
            st.error("优化建议模块不可用")
            st.info("请检查 ui/pages/optimization_suggestions.py 文件是否存在")

    def _show_parameter_backtest(self):
        """参数回测页面"""
        try:
            # 设置session state以指示这是参数回测请求
            st.session_state.requested_analysis_type = "parameter_backtest"

            from ui.pages_disabled.optimization_suggestions import \
                show_optimization_suggestions
            show_optimization_suggestions()
        except ImportError:
            st.error("参数回测模块不可用")
            st.info("请检查 ui/pages_disabled/optimization_suggestions.py 文件是否存在")
    
    def _show_prediction_dashboard(self):
        """预测分析仪表板页面"""
        try:
            from ui.pages_disabled.prediction_dashboard import \
                show_prediction_dashboard
            show_prediction_dashboard()
        except ImportError:
            st.error("预测分析仪表板模块不可用")
            st.info("请检查 ui/pages_disabled/prediction_dashboard.py 文件是否存在")
    
    def _show_data_management_deep(self):
        """数据管理深度页面"""
        try:
            from ui.pages_disabled.data_management_deep import \
                show_data_management_deep
            show_data_management_deep()
        except ImportError:
            st.error("数据管理深度模块不可用")
            st.info("请检查 ui/pages_disabled/data_management_deep.py 文件是否存在")
    
    def _show_feature_engineering(self):
        """特征工程页面"""
        try:
            from ui.pages.feature_engineering_deep import \
                show_feature_engineering_deep_page
            show_feature_engineering_deep_page()
        except ImportError:
            st.error("特征工程模块不可用")
            st.info("请检查 ui/pages/feature_engineering_deep.py 文件是否存在")
    
    def _show_ab_testing(self):
        """A/B测试页面"""
        try:
            from ui.pages_disabled.ab_testing_deep import show_ab_testing_deep
            show_ab_testing_deep()
        except ImportError:
            st.error("A/B测试模块不可用")
            st.info("请检查 ui/pages_disabled/ab_testing_deep.py 文件是否存在")
    
    def _show_training_monitoring(self):
        """训练监控页面"""
        try:
            from ui.pages.training_monitoring_deep import main
            main()
        except ImportError:
            st.error("训练监控模块不可用")
            st.info("请检查 ui/pages/training_monitoring_deep.py 文件是否存在")

    def _show_bug_detection_status(self):
        """Bug检测状态页面"""
        try:
            from ui.pages.bug_detection_status import show_bug_detection_status
            show_bug_detection_status()
        except ImportError:
            st.error("Bug检测状态模块不可用")
            st.info("Bug检测系统可能未正确安装")
        except Exception as e:
            st.error(f"Bug检测状态页面加载失败: {str(e)}")
            st.info("请检查Bug检测系统配置")

    def _show_realtime_bug_dashboard(self):
        """实时Bug检测仪表板页面"""
        try:
            from src.ui.pages.realtime_bug_dashboard import \
                main as show_realtime_bug_dashboard
            show_realtime_bug_dashboard()
        except ImportError:
            st.error("实时Bug检测仪表板模块不可用")
            st.info("实时监控系统可能未正确安装")

            # 显示替代信息
            st.markdown("### 🔍 实时Bug检测仪表板")
            st.warning("⚠️ 实时监控功能需要完整的Bug检测系统支持")

            # 显示基本的WebSocket连接状态
            try:
                from src.ui.components.websocket_client import (
                    display_websocket_messages, display_websocket_status)

                st.subheader("📡 WebSocket连接状态")
                display_websocket_status()

                st.subheader("📨 最近消息")
                display_websocket_messages(5)

            except ImportError:
                st.info("WebSocket客户端组件不可用")

        except Exception as e:
            st.error(f"实时Bug检测仪表板加载失败: {str(e)}")
            st.info("请检查实时监控系统配置")

            # 显示错误详情（开发模式）
            if st.checkbox("显示错误详情"):
                st.code(str(e))
                import traceback
                st.code(traceback.format_exc())

    def _show_debug_data_flow(self):
        """数据流调试页面"""
        try:
            from src.ui.pages.debug_data_flow import show_debug_data_flow
            show_debug_data_flow()
        except ImportError:
            st.error("数据流调试模块不可用")
            st.info("调试系统可能未正确安装")

            # 显示替代信息
            st.markdown("### 🔍 数据流调试监控面板")
            st.warning("⚠️ 数据流调试功能需要完整的追踪系统支持")

            # 显示基本的系统状态
            st.subheader("📊 基本系统状态")

            # 检查API连接
            try:
                import requests
                response = requests.get("http://127.0.0.1:8888/health", timeout=5)
                if response.status_code == 200:
                    st.success("✅ API服务器连接正常")
                else:
                    st.error(f"❌ API服务器响应异常: {response.status_code}")
            except Exception as e:
                st.error(f"❌ API服务器连接失败: {str(e)}")

            # 检查WebSocket连接
            st.subheader("🔌 WebSocket连接测试")
            websocket_test_script = """
            <script>
            // 安全的DOM操作函数
            function safeSetHTML(element, html) {
                if (element) {
                    element.innerHTML = html;
                    return true;
                } else {
                    console.warn('元素不存在');
                    return false;
                }
            }

            function testWebSocket() {
                const statusDiv = document.getElementById('ws-test-status');

                try {
                    const ws = new WebSocket('ws://127.0.0.1:8888/ws/prediction-results');

                    ws.onopen = function() {
                        safeSetHTML(statusDiv, '<div style="color: green;">✅ WebSocket连接成功</div>');
                        ws.close();
                    };

                    ws.onerror = function() {
                        safeSetHTML(statusDiv, '<div style="color: red;">❌ WebSocket连接失败</div>');
                    };

                } catch (error) {
                    safeSetHTML(statusDiv, '<div style="color: red;">❌ WebSocket测试失败: ' + error.message + '</div>');
                }
            }

            testWebSocket();
            </script>

            <div id="ws-test-status">
                <div style="color: gray;">🔄 正在测试WebSocket连接...</div>
            </div>
            """

            import streamlit.components.v1 as components
            components.html(websocket_test_script, height=100)

        except Exception as e:
            st.error(f"数据流调试页面加载失败: {str(e)}")
            st.info("请检查调试系统配置")

            # 显示错误详情（开发模式）
            if st.checkbox("显示调试错误详情"):
                st.code(str(e))
                import traceback
                st.code(traceback.format_exc())
