# MCP工具列表更新和规则完善

## 更新概述
根据实际可用的MCP工具列表，对 `.cursor\rules\mcprules.mdc` 文件进行了全面更新，排除了不存在的MCP工具，完善了现有工具的用法说明，并集成了Augment内置工具。

## 实际可用的MCP工具列表
1. **Playwright** - 浏览器自动化操作
2. **Context 7** - 查询最新库文档和示例
3. **Sequential thinking** - 复杂问题分析和深度思考
4. **mcp-deepwiki** - 获取背景知识和领域概念
5. **knowledge-graph** - 项目知识图谱管理
6. **serena** - 项目管理和精确代码编辑

## 已移除的不存在工具
- ❌ `mcp-feedback-enhanced` - 用户交互和确认
- ❌ `mcp-shrimp-task-manager` - 任务拆分与管理
- ❌ `desktop-commander` - 系统文件操作和命令执行

## 替换为Augment内置工具
- ✅ `launch-process` 替换 `desktop-commander`
- ✅ `task management` 替换 `mcp-shrimp-task-manager`
- ✅ 直接用户交互 替换 `mcp-feedback-enhanced`
- ✅ `save-file` 用于创建文档和文件
- ✅ `view` 用于查看文件和目录
- ✅ `str-replace-editor` 限制为150行编辑

## 详细更新内容

### 1. 各开发模式的工具更新

#### [模式：研究] - 需求分析阶段
- `context7-mcp` → `Context 7`
- `deepwiki-mcp` → `mcp-deepwiki`
- `sequential-thinking` → `Sequential thinking`

#### [模式：构思] - 方案设计阶段
- `sequential-thinking` → `Sequential thinking`
- `context7-mcp` → `Context 7`
- `deepwiki-mcp` → `mcp-deepwiki`

#### [模式：计划] - 详细规划阶段
- `sequential-thinking` → `Sequential thinking`
- `task list` → Augment内置的`task management`功能
- 新增：使用`save-file`工具创建任务文档

#### [模式：执行] - 代码实现阶段
- `desktop-commander` → `launch-process`
- `mcp-shrimp-task-manager` → Augment内置的`task management`功能
- `sequential-thinking` → `Sequential thinking`
- `str-replace-editor`限制：500行 → 150行

#### [模式：评审] - 质量检查阶段
- `desktop-commander` → `launch-process`
- `sequential-thinking` → `Sequential thinking`
- `mcp-feedback-enhanced` → 直接与用户交互

### 2. 开发工作流程更新
- **文件操作**：`desktop-commander` → `launch-process`
- **复杂分析**：`sequential-thinking` → `Sequential thinking`
- **技术查询**：`context7-mcp` → `Context 7`
- **知识背景补充**：`deepwiki-mcp` → `mcp-deepwiki`
- **任务管理**：`mcp-shrimp-task-manager` → Augment内置的`task management`功能

### 3. MCP服务优先级重新排序
**新的MCP工具优先级**：
1. `Sequential thinking` - 复杂问题分析和深度思考
2. `serena` - 项目管理和精确代码编辑
3. `Context 7` - 查询最新库文档和示例
4. `mcp-deepwiki` - 获取背景知识和领域概念
5. `knowledge-graph` - 项目知识图谱管理
6. `Playwright` - 浏览器自动化操作

**新增Augment内置工具优先级**：
1. `codebase-retrieval` - 分析现有代码结构
2. `str-replace-editor` - 代码编辑和修改
3. `launch-process` - 系统文件操作和命令执行
4. `save-file` - 创建新文件
5. `view` - 查看文件和目录
6. `task management` - 任务拆分与状态追踪

### 4. 工具使用指南完善

#### Sequential Thinking 详细功能
- **逐步分析**：`sequentialthinking`工具进行分步推理
- **思维链构建**：建立完整的逻辑推理链条
- **方案评估**：对比多种解决方案的优缺点
- **问题分解**：将复杂问题拆解为可管理的子问题
- **优势**：支持递归思考、可调整思考深度、能处理不确定性问题

#### Context 7 详细功能
- **库文档查询**：`resolve-library-id`和`get-library-docs`获取最新文档
- **API参考**：获取详细的API使用说明和示例
- **最佳实践**：查询行业标准和推荐做法
- **代码示例**：获取实际可用的代码片段
- **优势**：实时更新、权威来源、包含实际代码示例

#### mcp-deepwiki 详细功能
- **知识检索**：`deepwiki_fetch`获取技术背景知识
- **概念解释**：深入解释技术概念和原理
- **架构模式**：提供成熟的设计模式和架构范式
- **行业通识**：补充领域相关的通用知识
- **优势**：深度内容、结构化知识、覆盖面广

#### knowledge-graph 详细功能
- **实体管理**：`create_entities`、`delete_entities`创建和管理实体
- **关系建立**：`create_relations`、`delete_relations`建立实体间关系
- **观察记录**：`add_observations`、`delete_observations`记录重要信息
- **知识检索**：`search_nodes`、`open_nodes`查找相关知识
- **图谱查看**：`read_graph`查看完整知识网络
- **优势**：持久化记忆、关系网络、结构化存储

### 5. 新增Augment内置工具指南

#### codebase-retrieval
- **用途**：分析现有代码结构，获取项目上下文和代码模板
- **使用时机**：需要理解现有代码结构或查找相关实现时

#### str-replace-editor
- **用途**：精确的代码编辑和修改工具
- **限制**：每次编辑不超过150行
- **注意事项**：必须提供instruction_reminder参数

#### launch-process
- **用途**：执行系统命令、运行测试、文件操作
- **使用时机**：需要运行测试、安装依赖、执行脚本时

#### save-file
- **用途**：创建新文件，保存内容到指定路径
- **限制**：文件内容不超过300行
- **注意事项**：必须提供instructions_reminder参数

#### view
- **用途**：查看文件和目录内容，支持正则搜索
- **使用时机**：需要查看文件内容或目录结构时

#### task management
- **核心功能**：
  - `add_tasks` - 创建新任务
  - `update_tasks` - 更新任务状态
  - `reorganize_tasklist` - 重新组织任务结构
  - `view_tasklist` - 查看当前任务列表

### 6. 工作流程控制更新
- **阶段反馈**：直接与用户交互确认，不再依赖特定工具
- **任务结束**：持续与用户交互直到任务完成确认
- **工具协同**：合理组合使用MCP工具和Augment内置工具

## 对福彩3D项目的影响

### 1. 工具使用更加准确
- 所有工具引用都对应实际可用的功能
- 避免了调用不存在工具导致的错误
- 提高了开发效率和成功率

### 2. 功能覆盖更加完整
- **项目分析**：serena + codebase-retrieval
- **深度思考**：Sequential thinking
- **技术查询**：Context 7 + mcp-deepwiki
- **知识管理**：knowledge-graph
- **代码编辑**：serena + str-replace-editor
- **系统操作**：launch-process
- **任务管理**：task management
- **界面测试**：Playwright

### 3. 开发流程更加顺畅
- 每个开发阶段都有明确的工具支持
- 工具间的协同更加合理
- 减少了工具选择的困惑

## 更新效果验证

### 1. MCP工具可用性
✅ **所有列出的MCP工具都是实际可用的**
✅ **移除了所有不存在的工具引用**
✅ **工具功能描述准确详细**

### 2. Augment内置工具集成
✅ **完整集成了Augment的内置工具**
✅ **明确了各工具的使用场景和限制**
✅ **提供了详细的使用指南**

### 3. 工作流程优化
✅ **各开发模式的工具使用更加合理**
✅ **工具优先级排序更加科学**
✅ **工具协同策略更加完善**

## 更新完成确认

✅ **MCP工具列表已更新为实际可用工具**
✅ **不存在的工具已全部移除**
✅ **Augment内置工具已完整集成**
✅ **工具使用指南已详细完善**
✅ **各开发模式的工具配置已优化**
✅ **工作流程控制已更新**

现在 `.cursor\rules\mcprules.mdc` 文件完全反映了实际的工具可用性，为福彩3D预测系统和其他项目的开发提供了准确可靠的工具使用指导！