"""
性能跟踪模块

负责跟踪和监控各模型的性能指标。
"""

import time
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from .base_module import BaseFusionModule


class PerformanceTrackerModule(BaseFusionModule):
    """性能跟踪模块"""
    
    def __init__(self, context):
        super().__init__(context, "PerformanceTracker")
        
    def _do_initialize(self):
        """初始化性能跟踪模块"""
        self.logger.info("性能跟踪模块初始化完成")
    
    def update_model_performance(self, model_name: str, prediction: str, actual: str, confidence: float = None) -> bool:
        """
        更新模型性能
        
        Args:
            model_name: 模型名称
            prediction: 预测结果
            actual: 实际结果
            confidence: 预测置信度
            
        Returns:
            是否更新成功
        """
        if not self.is_ready():
            self.logger.warning("性能跟踪模块未就绪")
            return False
            
        try:
            # 计算准确性
            accuracy = self._calculate_accuracy(prediction, actual)
            
            # 记录性能数据
            performance_record = {
                "model_name": model_name,
                "prediction": prediction,
                "actual": actual,
                "accuracy": accuracy,
                "confidence": confidence,
                "timestamp": datetime.now().isoformat()
            }
            
            # 保存到性能历史
            self._save_performance_record(performance_record)
            
            # 更新模型统计
            self._update_model_statistics(model_name, accuracy, confidence)
            
            self.logger.info(f"更新 {model_name} 性能: 准确率={accuracy:.3f}")
            return True
            
        except Exception as e:
            self._handle_error("更新模型性能", e)
            return False
    
    def get_model_performance(self, model_name: str, days: int = 30) -> Dict[str, Any]:
        """
        获取模型性能统计
        
        Args:
            model_name: 模型名称
            days: 统计天数
            
        Returns:
            性能统计信息
        """
        if not self.is_ready():
            return {"error": "模块未就绪"}
            
        try:
            # 加载性能历史
            records = self._load_performance_records(model_name, days)
            
            if not records:
                return {
                    "model_name": model_name,
                    "total_predictions": 0,
                    "accuracy": 0.0,
                    "confidence": 0.0,
                    "trend": "无数据"
                }
            
            # 计算统计指标
            total_predictions = len(records)
            avg_accuracy = sum(r.get('accuracy', 0) for r in records) / total_predictions
            avg_confidence = sum(r.get('confidence', 0) for r in records if r.get('confidence')) / max(1, len([r for r in records if r.get('confidence')]))
            
            # 计算趋势
            trend = self._calculate_performance_trend(records)
            
            return {
                "model_name": model_name,
                "total_predictions": total_predictions,
                "accuracy": round(avg_accuracy, 3),
                "confidence": round(avg_confidence, 3),
                "trend": trend,
                "last_updated": records[-1].get('timestamp') if records else None
            }
            
        except Exception as e:
            self._handle_error("获取模型性能", e)
            return {"error": str(e)}
    
    def get_all_models_performance(self, days: int = 30) -> Dict[str, Dict[str, Any]]:
        """
        获取所有模型的性能统计
        
        Args:
            days: 统计天数
            
        Returns:
            所有模型的性能统计
        """
        if not self.is_ready():
            return {"error": "模块未就绪"}
            
        try:
            model_names = ['trend', 'pattern', 'lstm', 'fusion']
            all_performance = {}
            
            for model_name in model_names:
                all_performance[model_name] = self.get_model_performance(model_name, days)
            
            # 添加整体统计
            all_performance['summary'] = self._calculate_overall_summary(all_performance)
            
            return all_performance
            
        except Exception as e:
            self._handle_error("获取所有模型性能", e)
            return {"error": str(e)}
    
    def get_performance_ranking(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        获取模型性能排行榜
        
        Args:
            days: 统计天数
            
        Returns:
            性能排行榜
        """
        try:
            all_performance = self.get_all_models_performance(days)
            
            if 'error' in all_performance:
                return []
            
            # 排除summary
            model_performance = {k: v for k, v in all_performance.items() if k != 'summary'}
            
            # 按准确率排序
            ranking = sorted(
                model_performance.items(),
                key=lambda x: x[1].get('accuracy', 0),
                reverse=True
            )
            
            # 格式化排行榜
            formatted_ranking = []
            for rank, (model_name, performance) in enumerate(ranking, 1):
                formatted_ranking.append({
                    "rank": rank,
                    "model_name": model_name,
                    "accuracy": performance.get('accuracy', 0),
                    "confidence": performance.get('confidence', 0),
                    "total_predictions": performance.get('total_predictions', 0),
                    "trend": performance.get('trend', '未知')
                })
            
            return formatted_ranking
            
        except Exception as e:
            self._handle_error("获取性能排行榜", e)
            return []
    
    def _calculate_accuracy(self, prediction: str, actual: str) -> float:
        """计算预测准确性"""
        try:
            if not prediction or not actual:
                return 0.0
            
            pred_str = str(prediction).strip()
            actual_str = str(actual).strip()
            
            if pred_str == actual_str:
                return 1.0  # 完全匹配
            
            # 部分匹配计算
            if len(pred_str) == len(actual_str) == 3:
                matches = sum(1 for a, b in zip(pred_str, actual_str) if a == b)
                return matches / 3.0
            
            return 0.0
            
        except:
            return 0.0
    
    def _save_performance_record(self, record: Dict[str, Any]):
        """保存性能记录"""
        try:
            # 获取性能文件路径
            perf_file = self.context.get_cache_path("performance_history.jsonl")
            
            # 追加记录
            with open(perf_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(record, ensure_ascii=False) + '\n')
                
        except Exception as e:
            self.logger.warning(f"保存性能记录失败: {e}")
    
    def _load_performance_records(self, model_name: str, days: int) -> List[Dict[str, Any]]:
        """加载性能记录"""
        try:
            perf_file = self.context.get_cache_path("performance_history.jsonl")
            
            if not perf_file.exists():
                return []
            
            # 计算时间范围
            cutoff_date = datetime.now() - timedelta(days=days)
            
            records = []
            with open(perf_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        record = json.loads(line.strip())
                        
                        # 过滤模型和时间
                        if record.get('model_name') == model_name:
                            record_time = datetime.fromisoformat(record.get('timestamp', ''))
                            if record_time >= cutoff_date:
                                records.append(record)
                    except:
                        continue
            
            return sorted(records, key=lambda x: x.get('timestamp', ''))
            
        except Exception as e:
            self.logger.warning(f"加载性能记录失败: {e}")
            return []
    
    def _update_model_statistics(self, model_name: str, accuracy: float, confidence: float):
        """更新模型统计信息"""
        try:
            stats_file = self.context.get_cache_path("model_statistics.json")
            
            # 加载现有统计
            stats = {}
            if stats_file.exists():
                with open(stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
            
            # 更新统计
            if model_name not in stats:
                stats[model_name] = {
                    "total_predictions": 0,
                    "total_accuracy": 0.0,
                    "total_confidence": 0.0,
                    "last_updated": None
                }
            
            model_stats = stats[model_name]
            model_stats["total_predictions"] += 1
            model_stats["total_accuracy"] += accuracy
            if confidence is not None:
                model_stats["total_confidence"] += confidence
            model_stats["last_updated"] = datetime.now().isoformat()
            
            # 保存统计
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.warning(f"更新模型统计失败: {e}")
    
    def _calculate_performance_trend(self, records: List[Dict[str, Any]]) -> str:
        """计算性能趋势"""
        try:
            if len(records) < 5:
                return "数据不足"
            
            # 取最近的记录计算趋势
            recent_records = records[-10:]
            first_half = recent_records[:len(recent_records)//2]
            second_half = recent_records[len(recent_records)//2:]
            
            first_avg = sum(r.get('accuracy', 0) for r in first_half) / len(first_half)
            second_avg = sum(r.get('accuracy', 0) for r in second_half) / len(second_half)
            
            diff = second_avg - first_avg
            
            if diff > 0.05:
                return "上升"
            elif diff < -0.05:
                return "下降"
            else:
                return "稳定"
                
        except:
            return "未知"
    
    def _calculate_overall_summary(self, all_performance: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """计算整体摘要"""
        try:
            valid_models = [v for v in all_performance.values() if 'error' not in v]
            
            if not valid_models:
                return {"error": "无有效数据"}
            
            total_predictions = sum(m.get('total_predictions', 0) for m in valid_models)
            avg_accuracy = sum(m.get('accuracy', 0) for m in valid_models) / len(valid_models)
            avg_confidence = sum(m.get('confidence', 0) for m in valid_models) / len(valid_models)
            
            return {
                "total_models": len(valid_models),
                "total_predictions": total_predictions,
                "average_accuracy": round(avg_accuracy, 3),
                "average_confidence": round(avg_confidence, 3),
                "best_model": max(valid_models, key=lambda x: x.get('accuracy', 0)),
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.warning(f"计算整体摘要失败: {e}")
            return {"error": str(e)}
