# 🔧 福彩3D预测系统 - 系统维护指南

## 📋 维护概述

本指南提供福彩3D预测系统的日常维护、定期检查和故障预防措施，确保系统稳定运行和最佳性能。

## 📅 维护计划

### 日常维护（每日）
- [ ] 检查系统运行状态
- [ ] 查看错误日志
- [ ] 验证数据更新
- [ ] 监控系统资源使用

### 周度维护（每周）
- [ ] 重启所有服务
- [ ] 清理临时文件
- [ ] 备份重要数据
- [ ] 检查磁盘空间

### 月度维护（每月）
- [ ] 更新系统依赖
- [ ] 性能优化检查
- [ ] 安全检查
- [ ] 文档更新

## 🔍 日常检查清单

### 1. 系统状态检查
```bash
# 检查API服务状态
curl http://127.0.0.1:8888/health

# 检查Streamlit服务
# 访问 http://127.0.0.1:8501

# 检查进程状态
tasklist | findstr python.exe
```

### 2. 日志检查
```bash
# 查看最新错误日志
cd D:\github\3dyuce\data\logs
type error_*.log | more

# 查看API日志
type api_*.log | more

# 查看调度器日志
type scheduler_*.log | more
```

### 3. 数据验证
```bash
# 检查数据库文件
dir data\lottery.db

# 验证数据完整性
python scripts\validate_data.py

# 检查最新数据更新时间
python -c "import sqlite3; conn=sqlite3.connect('data/lottery.db'); print(conn.execute('SELECT MAX(period) FROM lottery_data').fetchone())"
```

## 🧹 清理任务

### 日志文件清理
```bash
# 清理7天前的日志文件
cd D:\github\3dyuce\data\logs
forfiles /m *.log /d -7 /c "cmd /c del @path"

# 清理临时文件
del /q temp\*.*
rmdir /s /q __pycache__
```

### 缓存清理
```bash
# 清理Python缓存
cd D:\github\3dyuce
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"

# 清理Streamlit缓存
rmdir /s /q .streamlit\cache
```

## 💾 备份策略

### 数据备份
```bash
# 每日数据库备份
cd D:\github\3dyuce
copy data\lottery.db data\backup\lottery_%date:~0,4%%date:~5,2%%date:~8,2%.db

# 配置文件备份
copy *.json data\backup\
copy *.md data\backup\
```

### 代码备份
```bash
# 创建代码快照
git add .
git commit -m "Daily backup - %date%"
git push origin main
```

## 📊 性能监控

### 系统资源监控
```bash
# CPU和内存使用情况
wmic cpu get loadpercentage /value
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value

# 磁盘空间检查
dir D:\github\3dyuce /s | find "bytes free"
```

### 应用性能监控
```bash
# 检查Python进程资源使用
tasklist /fi "imagename eq python.exe" /fo table

# 检查端口使用情况
netstat -ano | findstr :8501
netstat -ano | findstr :8888
```

## 🔄 服务管理

### 服务重启
```bash
# 停止所有Python进程
taskkill /f /im python.exe

# 重新启动服务
cd D:\github\3dyuce
start "API服务" cmd /k "python start_production_api.py"
timeout /t 5
start "Streamlit界面" cmd /k "python -m streamlit run src/ui/main.py"
```

### 服务健康检查
```bash
# API服务健康检查
curl -f http://127.0.0.1:8888/health || echo "API服务异常"

# Streamlit服务检查
curl -f http://127.0.0.1:8501 || echo "Streamlit服务异常"
```

## 🔧 故障预防

### 预防措施
1. **定期重启**: 每周重启一次防止内存泄漏
2. **监控告警**: 设置资源使用告警阈值
3. **数据验证**: 每日验证数据完整性
4. **备份检查**: 定期验证备份文件可用性

### 监控脚本
创建自动监控脚本 `monitor.bat`:
```batch
@echo off
echo 开始系统健康检查...

# 检查API服务
curl -s http://127.0.0.1:8888/health > nul
if %errorlevel% neq 0 (
    echo 警告: API服务异常
    # 发送告警邮件或通知
)

# 检查磁盘空间
for /f "tokens=3" %%a in ('dir D:\github\3dyuce /-c ^| find "bytes free"') do (
    if %%a lss ********** (
        echo 警告: 磁盘空间不足
    )
)

echo 健康检查完成
```

## 📈 性能优化

### 数据库优化
```sql
-- 定期执行数据库优化
VACUUM;
REINDEX;
ANALYZE;
```

### 应用优化
1. **缓存优化**: 启用Streamlit缓存
2. **查询优化**: 优化数据库查询语句
3. **资源优化**: 清理不必要的文件和进程

## 🚨 告警设置

### 资源告警阈值
- **CPU使用率**: >80% 持续5分钟
- **内存使用率**: >90% 持续3分钟
- **磁盘空间**: <1GB 可用空间
- **响应时间**: >5秒

### 告警处理流程
1. **立即响应**: 检查系统状态
2. **问题诊断**: 查看日志和监控数据
3. **快速修复**: 应用临时解决方案
4. **根因分析**: 找出问题根本原因
5. **预防措施**: 制定预防策略

## 📋 维护记录

### 维护日志模板
```
日期: ____年__月__日
维护人员: ________
维护类型: [日常/周度/月度/紧急]

执行的维护任务:
- [ ] 系统状态检查
- [ ] 日志检查
- [ ] 数据备份
- [ ] 性能监控
- [ ] 其他: ________

发现的问题:
1. ________________
2. ________________

解决方案:
1. ________________
2. ________________

后续行动:
- ________________
- ________________

维护完成时间: ____:____
系统状态: [正常/异常]
```

## 🔐 安全维护

### 安全检查项目
- [ ] 检查系统访问日志
- [ ] 验证端口安全配置
- [ ] 检查文件权限设置
- [ ] 更新安全补丁

### 数据安全
- [ ] 定期备份验证
- [ ] 敏感数据加密检查
- [ ] 访问权限审核
- [ ] 数据完整性验证

## 📞 紧急联系

### 紧急情况处理
1. **系统完全宕机**: 立即重启所有服务
2. **数据丢失**: 从最新备份恢复
3. **性能严重下降**: 检查资源使用和优化
4. **安全事件**: 立即隔离并分析

### 联系信息
- **技术负责人**: ________
- **系统管理员**: ________
- **紧急联系电话**: ________

---

**维护指南版本**: v2.0
**最后更新**: 2025-07-23
**下次更新**: 2025-08-23
