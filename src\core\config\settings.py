"""
主配置类

使用Pydantic Settings实现统一的配置管理。
"""

import os
from functools import lru_cache
from pathlib import Path
from typing import Optional

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings

from .database import DatabaseConfig
from .api import APIConfig
from .ml import MLConfig
from .cache import CacheConfig


class Settings(BaseSettings):
    """主配置类"""
    
    # 环境配置
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=False, description="调试模式")
    
    # 项目配置
    project_name: str = Field(default="福彩3D预测系统", description="项目名称")
    version: str = Field(default="2025.2.0", description="版本号")
    
    # 路径配置
    project_root: Path = Field(default_factory=lambda: Path(__file__).parent.parent.parent.parent)
    data_dir: Path = Field(default_factory=lambda: Path("data"))
    logs_dir: Path = Field(default_factory=lambda: Path("logs"))
    cache_dir: Path = Field(default_factory=lambda: Path("data/cache"))
    
    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    ml: MLConfig = Field(default_factory=MLConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.project_root / self.data_dir,
            self.project_root / self.logs_dir,
            self.project_root / self.cache_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment.lower() == "production"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment.lower() == "testing"
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        if self.database.url:
            return self.database.url
        
        # 构建SQLite URL
        db_path = self.project_root / self.data_dir / self.database.name
        return f"sqlite:///{db_path}"
    
    def get_log_file_path(self, log_name: str = "app.log") -> Path:
        """获取日志文件路径"""
        return self.project_root / self.logs_dir / log_name
    
    def get_cache_file_path(self, cache_name: str) -> Path:
        """获取缓存文件路径"""
        return self.project_root / self.cache_dir / cache_name


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    # 根据环境变量加载不同的配置文件
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    config_file = None
    config_dir = Path(__file__).parent.parent.parent.parent / "config"
    
    if config_dir.exists():
        config_file = config_dir / f"{env}.yaml"
        if not config_file.exists():
            config_file = config_dir / "development.yaml"
    
    # 创建配置实例
    if config_file and config_file.exists():
        # 如果有配置文件，可以在这里加载YAML配置
        # 目前先使用环境变量和默认值
        pass
    
    return Settings(environment=env)


# 全局配置实例
settings = get_settings()
