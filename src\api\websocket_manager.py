#!/usr/bin/env python3
"""
预测结果WebSocket管理器
专门用于预测结果的实时推送和管理
"""

import asyncio
import json
import logging
import time
import uuid
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set

from fastapi import WebSocket, WebSocketDisconnect

# 配置日志
logger = logging.getLogger(__name__)

class PredictionMessageType(Enum):
    """预测消息类型枚举"""
    PREDICTION_START = "prediction_start"
    PREDICTION_PROGRESS = "prediction_progress"
    PREDICTION_RESULT = "prediction_result"
    PREDICTION_ERROR = "prediction_error"
    HEARTBEAT = "heartbeat"
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    CONNECTION_STATUS = "connection_status"

@dataclass
class PredictionSession:
    """预测WebSocket会话"""
    id: str
    websocket: WebSocket
    connected_at: float
    last_activity: float
    subscriptions: Set[str]
    user_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class PredictionWebSocketManager:
    """预测结果WebSocket管理器"""
    
    def __init__(self):
        self.active_sessions: Dict[str, PredictionSession] = {}
        self.topic_subscribers: Dict[str, Set[str]] = {}
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'messages_sent': 0,
            'errors': 0,
            'start_time': time.time()
        }
        self._running = False
        self._background_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """启动WebSocket管理器"""
        if self._running:
            return
        
        self._running = True
        self._background_task = asyncio.create_task(self._process_message_queue())
        logger.info("预测WebSocket管理器已启动")
    
    async def stop(self):
        """停止WebSocket管理器"""
        self._running = False
        if self._background_task:
            self._background_task.cancel()
            try:
                await self._background_task
            except asyncio.CancelledError:
                pass
        
        # 断开所有连接
        for session_id in list(self.active_sessions.keys()):
            await self.disconnect(session_id)
        
        logger.info("预测WebSocket管理器已停止")
    
    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None, 
                     metadata: Optional[Dict[str, Any]] = None) -> str:
        """建立WebSocket连接"""
        await websocket.accept()
        
        session_id = str(uuid.uuid4())
        session = PredictionSession(
            id=session_id,
            websocket=websocket,
            connected_at=time.time(),
            last_activity=time.time(),
            subscriptions=set(),
            user_id=user_id,
            metadata=metadata or {}
        )
        
        self.active_sessions[session_id] = session
        self.stats['total_connections'] += 1
        self.stats['active_connections'] = len(self.active_sessions)
        
        # 发送连接确认
        await self._send_to_session(session_id, {
            'type': PredictionMessageType.CONNECTION_STATUS.value,
            'status': 'connected',
            'session_id': session_id,
            'timestamp': time.time(),
            'features': ['prediction_results', 'real_time_updates', 'progress_tracking']
        })
        
        logger.info(f"预测WebSocket连接建立: {session_id}")
        return session_id
    
    async def disconnect(self, session_id: str):
        """断开WebSocket连接"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        # 从所有订阅中移除
        for topic in list(session.subscriptions):
            await self._unsubscribe_from_topic(session_id, topic)
        
        # 关闭WebSocket连接
        try:
            await session.websocket.close()
        except Exception as e:
            logger.warning(f"关闭WebSocket连接失败 [{session_id}]: {e}")
        
        # 移除会话
        del self.active_sessions[session_id]
        self.stats['active_connections'] = len(self.active_sessions)
        
        logger.info(f"预测WebSocket连接断开: {session_id}")
    
    async def subscribe_to_topic(self, session_id: str, topic: str):
        """订阅主题"""
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        session.subscriptions.add(topic)
        
        if topic not in self.topic_subscribers:
            self.topic_subscribers[topic] = set()
        self.topic_subscribers[topic].add(session_id)
        
        logger.debug(f"会话 {session_id} 订阅主题: {topic}")
        return True
    
    async def unsubscribe_from_topic(self, session_id: str, topic: str):
        """取消订阅主题"""
        await self._unsubscribe_from_topic(session_id, topic)
    
    async def _unsubscribe_from_topic(self, session_id: str, topic: str):
        """内部取消订阅方法"""
        if session_id in self.active_sessions:
            self.active_sessions[session_id].subscriptions.discard(topic)
        
        if topic in self.topic_subscribers:
            self.topic_subscribers[topic].discard(session_id)
            if not self.topic_subscribers[topic]:
                del self.topic_subscribers[topic]
        
        logger.debug(f"会话 {session_id} 取消订阅主题: {topic}")
    
    async def broadcast_to_topic(self, topic: str, message: Dict[str, Any]):
        """向主题订阅者广播消息"""
        if topic not in self.topic_subscribers:
            return 0
        
        subscribers = self.topic_subscribers[topic].copy()
        sent_count = 0
        
        for session_id in subscribers:
            if await self._send_to_session(session_id, message):
                sent_count += 1
        
        logger.debug(f"向主题 {topic} 广播消息，发送给 {sent_count} 个订阅者")
        return sent_count
    
    async def send_prediction_result(self, prediction_data: Dict[str, Any], 
                                   target_session: Optional[str] = None):
        """发送预测结果"""
        message = {
            'type': PredictionMessageType.PREDICTION_RESULT.value,
            'data': prediction_data,
            'timestamp': time.time()
        }
        
        if target_session:
            return await self._send_to_session(target_session, message)
        else:
            return await self.broadcast_to_topic('prediction_results', message)
    
    async def send_prediction_progress(self, progress_data: Dict[str, Any],
                                     target_session: Optional[str] = None):
        """发送预测进度"""
        message = {
            'type': PredictionMessageType.PREDICTION_PROGRESS.value,
            'data': progress_data,
            'timestamp': time.time()
        }
        
        if target_session:
            return await self._send_to_session(target_session, message)
        else:
            return await self.broadcast_to_topic('prediction_progress', message)
    
    async def _send_to_session(self, session_id: str, message: Dict[str, Any]) -> bool:
        """向指定会话发送消息"""
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        
        try:
            await session.websocket.send_text(json.dumps(message))
            session.last_activity = time.time()
            self.stats['messages_sent'] += 1
            return True
        except Exception as e:
            logger.error(f"发送消息到会话 {session_id} 失败: {e}")
            self.stats['errors'] += 1
            # 标记连接为断开
            await self.disconnect(session_id)
            return False
    
    async def _process_message_queue(self):
        """处理消息队列"""
        while self._running:
            try:
                # 这里可以添加队列消息处理逻辑
                await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"处理消息队列失败: {e}")
                await asyncio.sleep(1)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats['start_time']
        
        return {
            **self.stats,
            'uptime_seconds': uptime,
            'topics': list(self.topic_subscribers.keys()),
            'topic_subscriber_counts': {
                topic: len(subscribers) 
                for topic, subscribers in self.topic_subscribers.items()
            }
        }

# 全局实例
prediction_websocket_manager = PredictionWebSocketManager()

async def get_prediction_websocket_manager() -> PredictionWebSocketManager:
    """获取预测WebSocket管理器实例"""
    return prediction_websocket_manager
