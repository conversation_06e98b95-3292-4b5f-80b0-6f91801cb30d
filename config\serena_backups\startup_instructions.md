# serena MCP服务器启动说明

## 自动启动配置

### 方法1: 使用批处理脚本
```batch
# 运行一键启动脚本
scripts\start_serena_mcp.bat
```

### 方法2: 使用PowerShell脚本
```powershell
# 运行PowerShell启动脚本
scripts\start_serena_mcp.ps1

# 自动打开仪表板
scripts\start_serena_mcp.ps1 -AutoOpen
```

### 方法3: 使用Python脚本
```bash
# 自动修复和启动
python scripts/serena_auto_fix.py

# 仅启动服务器
python scripts/serena_auto_fix.py --start-only
```

## 健康检查

### 快速检查
```bash
python scripts/serena_health_check.py --quick
```

### 完整检查
```bash
python scripts/serena_health_check.py
```

## Augment MCP配置

### 配置参数
- **服务器名称**: serena
- **可执行文件**: d:/github/3dyuce/venv/Scripts/serena-mcp-server.exe
- **参数**: --project d:/github/3dyuce
- **工作目录**: d:/github/3dyuce

### 验证步骤
1. 重启Cursor IDE
2. 打开Augment MCP设置
3. 检查serena连接状态（应为绿色）
4. 测试serena工具功能

## 故障排除

### 常见问题
1. **Language server failed to start**
   - 解决方案: 安装pyright到系统Python
   - 命令: `C:\Program Files\Python311\python.exe -m pip install pyright`

2. **Dashboard not accessible**
   - 检查serena进程是否运行
   - 检查端口24282是否被占用
   - 重启serena服务器

3. **MCP connection failed**
   - 重启Cursor IDE
   - 重新配置MCP服务器
   - 检查可执行文件路径

### 重置配置
如果遇到严重问题，可以：
1. 运行自动修复脚本
2. 重启Cursor
3. 重新配置MCP连接

## 仪表板访问
- URL: http://127.0.0.1:24282/dashboard/index.html
- 功能: 查看serena状态、日志、工具列表

## 自动化脚本
- `start_serena_mcp.bat`: Windows批处理启动脚本
- `start_serena_mcp.ps1`: PowerShell启动脚本
- `serena_auto_fix.py`: 自动诊断和修复脚本
- `serena_health_check.py`: 健康检查脚本
