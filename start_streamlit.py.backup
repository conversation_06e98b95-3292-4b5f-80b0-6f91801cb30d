#!/usr/bin/env python3
"""
启动Streamlit应用
"""

import os
import subprocess
import sys
from pathlib import Path


def check_virtual_env():
    """检查是否在虚拟环境中运行"""
    venv_path = Path(__file__).parent / "venv"
    if venv_path.exists():
        current_python = sys.executable

        if not str(current_python).startswith(str(venv_path)):
            print("⚠️ 检测到虚拟环境但未激活")
            print(f"🔧 推荐使用: venv\\Scripts\\activate && python {Path(__file__).name}")
            print(f"🔧 或直接使用: venv\\Scripts\\python.exe {Path(__file__).name}")
            print("🚀 继续使用当前Python环境...")


def start_streamlit():
    """启动Streamlit应用"""
    check_virtual_env()

    print(">>> 启动Streamlit应用...")
    print(">>> 应用地址: http://127.0.0.1:8501")
    print(">>> 确保FastAPI服务正在运行: http://127.0.0.1:8888")
    print(">>> 提示: 请确保API服务(端口8888)已启动")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = 'src'
    
    # 启动Streamlit
    try:
        # 使用os.system更直接的方式，确保输出可见
        cmd_str = f'"{sys.executable}" -m streamlit run src/ui/main.py --server.port=8501 --server.address=127.0.0.1 --browser.gatherUsageStats=false'

        print(f"🚀 执行命令: {cmd_str}")
        print("📝 Streamlit启动日志:")
        print("-" * 50)

        # 设置环境变量
        os.environ['PYTHONPATH'] = 'src'

        # 使用os.system直接执行，保持输出可见
        result = os.system(cmd_str)

        if result != 0:
            print(f"⚠️ 进程退出码: {result}")

    except KeyboardInterrupt:
        print("\n👋 Streamlit应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print(f"💡 建议直接使用: python -m streamlit run src/ui/main.py")

if __name__ == "__main__":
    start_streamlit()
