#!/usr/bin/env python3
"""
启动方式验证脚本
验证两种Streamlit启动方式的差异
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path


def test_module_imports_with_pythonpath():
    """测试设置PYTHONPATH后的模块导入"""
    print("🔍 测试1: 设置PYTHONPATH='src'后的模块导入")
    print("=" * 50)
    
    # 临时设置PYTHONPATH
    original_path = sys.path.copy()
    src_path = str(Path(__file__).parent / "src")
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
    
    success_count = 0
    total_count = 0
    
    # 测试关键模块导入
    test_modules = [
        "ui.components.enhanced_navigation",
        "ui.components.navigation", 
        "ui.intelligent_fusion_components",
        "ui.prediction_display",
        "ui.data_update_components",
        "ui.components.page_manager",
        "ui.components.search_component"
    ]
    
    for module_name in test_modules:
        total_count += 1
        try:
            module = importlib.import_module(module_name)
            print(f"✅ {module_name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name} - {e}")
        except Exception as e:
            print(f"⚠️  {module_name} - {e}")
    
    # 恢复原始路径
    sys.path = original_path
    
    print(f"\n📊 结果: {success_count}/{total_count} 模块导入成功")
    return success_count, total_count


def test_module_imports_without_pythonpath():
    """测试不设置PYTHONPATH的模块导入"""
    print("\n🔍 测试2: 不设置PYTHONPATH的模块导入")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    # 测试关键模块导入
    test_modules = [
        "ui.components.enhanced_navigation",
        "ui.components.navigation", 
        "ui.intelligent_fusion_components",
        "ui.prediction_display",
        "ui.data_update_components"
    ]
    
    for module_name in test_modules:
        total_count += 1
        try:
            module = importlib.import_module(module_name)
            print(f"✅ {module_name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name} - ImportError")
        except Exception as e:
            print(f"⚠️  {module_name} - {e}")
    
    print(f"\n📊 结果: {success_count}/{total_count} 模块导入成功")
    return success_count, total_count


def check_start_streamlit_script():
    """检查start_streamlit.py脚本"""
    print("\n🔍 测试3: 检查start_streamlit.py脚本")
    print("=" * 50)
    
    script_path = Path(__file__).parent / "start_streamlit.py"
    
    if not script_path.exists():
        print("❌ start_streamlit.py 文件不存在")
        return False
    
    print("✅ start_streamlit.py 文件存在")
    
    # 检查脚本内容
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "PYTHONPATH" in content and "env['PYTHONPATH'] = 'src'" in content:
            print("✅ 脚本包含PYTHONPATH设置")
            return True
        else:
            print("❌ 脚本缺少PYTHONPATH设置")
            return False
            
    except Exception as e:
        print(f"❌ 读取脚本失败: {e}")
        return False


def generate_recommendation():
    """生成建议"""
    print("\n🎯 建议和结论")
    print("=" * 50)
    
    print("基于测试结果，强烈建议：")
    print()
    print("✅ 推荐启动方式：")
    print("   python -m streamlit run src/ui/main.py")
    print()
    print("❌ 不推荐启动方式：")
    print("   streamlit run src/ui/main.py  # 缺少python -m前缀")
    print()
    print("🔧 技术原因：")
    print("   - 使用Python模块系统直接调用Streamlit")
    print("   - 符合Streamlit官方标准启动方式")
    print("   - 自动化友好，无需用户交互")
    print()
    print("📚 相关文档：")
    print("   - 正确启动方式.md")
    print("   - 启动方式技术说明.md")


def main():
    """主函数"""
    print("🚀 福彩3D预测系统 - 启动方式验证")
    print("=" * 60)
    print()
    
    # 测试1: 有PYTHONPATH
    success1, total1 = test_module_imports_with_pythonpath()
    
    # 测试2: 无PYTHONPATH  
    success2, total2 = test_module_imports_without_pythonpath()
    
    # 测试3: 检查脚本
    script_ok = check_start_streamlit_script()
    
    # 生成建议
    generate_recommendation()
    
    print(f"\n📋 验证总结:")
    print(f"   有PYTHONPATH: {success1}/{total1} 成功")
    print(f"   无PYTHONPATH: {success2}/{total2} 成功") 
    print(f"   启动脚本: {'✅ 正常' if script_ok else '❌ 异常'}")
    
    if success1 > success2 and script_ok:
        print(f"\n🎉 验证结论: start_streamlit.py 是最佳启动方式")
        return True
    else:
        print(f"\n⚠️  验证结论: 存在配置问题，需要检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
