# 生产环境配置

# 基础配置
environment: production
debug: false
project_name: "福彩3D预测系统"
version: "2025.2.0"

# 数据库配置
database:
  type: sqlite
  name: lottery.db
  echo: false
  echo_pool: false
  pool_size: 10
  max_overflow: 20

# API配置
api:
  host: "127.0.0.1"
  port: 8888
  debug: false
  reload: false
  docs_enabled: false
  cors_enabled: true
  cors_origins:
    - "http://127.0.0.1:8501"
    - "http://localhost:8501"
  rate_limit_enabled: true
  rate_limit_requests: 100
  rate_limit_window: 60
  log_level: INFO
  workers: 4
  timeout: 30

# 机器学习配置
ml:
  model_cache_enabled: true
  model_cache_size: 200
  batch_size: 32
  epochs: 100
  learning_rate: 0.001
  validation_split: 0.2
  use_gpu: false
  monitoring_enabled: true
  quantization_enabled: true
  pruning_enabled: true
  compression_ratio: 0.7

# 缓存配置
cache:
  enabled: true
  memory_enabled: true
  redis_enabled: false
  file_enabled: true
  default_ttl: 3600
  memory_max_size: 1000
  file_max_size: 500
  monitoring_enabled: true
  compression_enabled: true
  compression_threshold: 1024
  cache_levels:
    - memory
    - file
