# Augment Prompt Length Exceeded 解决方案配置
# 专门用于优化Augment平台的token使用和上下文管理

augment:
  # 上下文管理配置
  context_management:
    # Token限制配置
    max_tokens: 180000              # 最大token数量，预留20K缓冲
    warning_threshold: 0.8          # 80%时发出预警
    critical_threshold: 0.95        # 95%时紧急处理
    
    # 上下文分层配置
    context_layers:
      core_priority: 0.6            # 核心上下文占比60%
      related_priority: 0.3         # 相关上下文占比30%
      background_priority: 0.1      # 背景上下文占比10%
    
    # 动态调整配置
    dynamic_adjustment:
      enabled: true                 # 启用动态调整
      adjustment_factor: 0.1        # 调整幅度10%
      min_core_ratio: 0.4          # 核心上下文最小比例
      max_core_ratio: 0.8          # 核心上下文最大比例
  
  # 优化策略配置
  optimization:
    # 自动调整
    auto_adjustment: true           # 启用自动调整
    project_size_detection: true    # 启用项目规模检测
    
    # 压缩配置
    compression_enabled: false      # 初期禁用压缩（阶段三启用）
    compression_ratio: 0.7         # 目标压缩比例
    
    # 智能截断配置
    smart_truncation:
      enabled: true                 # 启用智能截断
      preserve_imports: true        # 保留导入语句
      preserve_classes: true        # 保留类定义
      preserve_functions: true      # 保留函数定义
      preserve_comments: false      # 不保留注释
  
  # Token预算管理配置
  token_budget:
    # 预警配置
    warning_enabled: true           # 启用预警
    warning_message: "Token使用量已达到{percentage}%，建议优化上下文"
    critical_message: "Token使用量已达到{percentage}%，正在执行智能截断"
    
    # 统计配置
    statistics_enabled: true        # 启用统计
    history_retention_days: 30      # 历史数据保留30天
    
    # 性能监控
    performance_tracking: true      # 启用性能跟踪
    response_time_threshold: 2.0    # 响应时间阈值2秒
  
  # 缓存配置
  cache:
    # 分层缓存
    layered_cache_enabled: true     # 启用分层缓存
    cache_ttl: 3600                # 缓存生存时间1小时
    
    # 缓存策略
    cache_strategy: "lru"           # LRU缓存策略
    max_cache_size: 1000           # 最大缓存条目数
    
    # 持久化配置
    persistent_cache: true          # 启用持久化缓存
    cache_file: "data/augment_cache.db"  # 缓存文件路径
  
  # 监控配置
  monitoring:
    # 实时监控
    real_time_monitoring: true      # 启用实时监控
    metrics_collection_interval: 60 # 指标收集间隔60秒
    
    # 仪表板配置
    dashboard_enabled: true         # 启用监控仪表板
    dashboard_refresh_interval: 30  # 仪表板刷新间隔30秒
    
    # 日志配置
    logging_enabled: true           # 启用日志记录
    log_level: "INFO"              # 日志级别
    log_file: "logs/augment.log"   # 日志文件路径
  
  # 高级功能配置（后续阶段启用）
  advanced_features:
    # AST分析（阶段三）
    ast_analysis_enabled: false    # AST分析
    
    # 语义压缩（阶段三）
    semantic_compression_enabled: false  # 语义压缩
    
    # 用户行为学习（阶段三）
    behavior_learning_enabled: false     # 用户行为学习
    
    # 分布式处理（阶段四）
    distributed_processing_enabled: false # 分布式处理
    
    # AI文件过滤（阶段四）
    ai_file_filtering_enabled: false     # AI文件过滤
    
    # 插件系统（阶段四）
    plugin_system_enabled: false         # 插件系统

# 环境特定配置
development:
  augment:
    context_management:
      max_tokens: 150000            # 开发环境较小限制
    monitoring:
      log_level: "DEBUG"           # 开发环境详细日志
    cache:
      cache_ttl: 1800              # 开发环境较短缓存时间

production:
  augment:
    context_management:
      max_tokens: 200000            # 生产环境更大限制
    monitoring:
      log_level: "WARNING"         # 生产环境只记录警告
    cache:
      cache_ttl: 7200              # 生产环境较长缓存时间
