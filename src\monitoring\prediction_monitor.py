#!/usr/bin/env python3
"""
预测准确率实时监控系统

实现预测结果实时跟踪、准确率统计、趋势分析和预警机制
集成到现有ModelPerformanceTracker系统
"""

import json
import logging
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

# 导入现有系统组件
try:
    from ..core.cache.manager import CacheManager
    from ..core.config.settings import Settings
    from ..core.exceptions.business import PredictionException
    from ..core.model_performance_tracker import ModelPerformanceTracker
except ImportError as e:
    print(f"导入警告: {e}")


class AlertLevel(Enum):
    """预警级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class PredictionRecord:
    """预测记录"""
    prediction_id: str
    model_name: str
    predicted_value: str
    actual_value: Optional[str]
    confidence_score: float
    prediction_time: datetime
    verification_time: Optional[datetime]
    is_correct: Optional[bool]
    accuracy_type: str  # 'direct', 'position', 'group'
    metadata: Dict[str, Any]


@dataclass
class AccuracyMetrics:
    """准确率指标"""
    direct_accuracy: float
    position_accuracy: float
    group_accuracy: float
    total_predictions: int
    correct_predictions: int
    confidence_avg: float
    confidence_std: float
    time_period: str
    last_updated: datetime


@dataclass
class TrendAnalysis:
    """趋势分析"""
    trend_direction: str  # 'up', 'down', 'stable'
    trend_strength: float  # 0-1
    slope: float
    r_squared: float
    forecast_7d: float
    forecast_30d: float
    confidence_interval: Tuple[float, float]


@dataclass
class Alert:
    """预警信息"""
    alert_id: str
    level: AlertLevel
    message: str
    model_name: str
    metric_name: str
    current_value: float
    threshold_value: float
    timestamp: datetime
    is_resolved: bool
    metadata: Dict[str, Any]


class PredictionMonitor:
    """预测准确率实时监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化预测监控器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        try:
            self.settings = Settings()
        except Exception:
            self.settings = None

        try:
            self.performance_tracker = ModelPerformanceTracker()
        except Exception:
            self.performance_tracker = None

        try:
            from ..core.cache.memory import MemoryCache
            self.cache_manager = CacheManager([MemoryCache()])
        except Exception:
            self.cache_manager = None
        
        # 监控配置
        self.monitoring_interval = self.config.get('monitoring_interval', 60)  # 秒
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'accuracy_drop': 0.1,      # 准确率下降10%触发警告
            'confidence_drop': 0.15,   # 置信度下降15%触发警告
            'prediction_volume': 0.5   # 预测量下降50%触发警告
        })
        
        # 数据存储
        self.prediction_records: deque = deque(maxlen=10000)  # 最近10000条记录
        self.accuracy_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.active_alerts: Dict[str, Alert] = {}
        
        # 线程控制
        self._monitoring_thread = None
        self._stop_monitoring = threading.Event()
        self._data_lock = threading.RLock()
        
        # 统计缓存
        self._stats_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
        
        self.logger.info("预测监控器初始化完成")
    
    def start_monitoring(self) -> bool:
        """
        启动实时监控
        
        Returns:
            是否启动成功
        """
        try:
            if self._monitoring_thread and self._monitoring_thread.is_alive():
                self.logger.warning("监控已在运行中")
                return True
            
            self._stop_monitoring.clear()
            self._monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                name="PredictionMonitor",
                daemon=True
            )
            self._monitoring_thread.start()
            
            self.logger.info("预测监控已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """
        停止实时监控
        
        Returns:
            是否停止成功
        """
        try:
            self._stop_monitoring.set()
            
            if self._monitoring_thread and self._monitoring_thread.is_alive():
                self._monitoring_thread.join(timeout=10)
            
            self.logger.info("预测监控已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止监控失败: {e}")
            return False
    
    def record_prediction(self, model_name: str, predicted_value: str, 
                         confidence_score: float, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        记录预测结果
        
        Args:
            model_name: 模型名称
            predicted_value: 预测值
            confidence_score: 置信度分数
            metadata: 元数据
            
        Returns:
            预测记录ID
        """
        try:
            prediction_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            record = PredictionRecord(
                prediction_id=prediction_id,
                model_name=model_name,
                predicted_value=predicted_value,
                actual_value=None,
                confidence_score=confidence_score,
                prediction_time=datetime.now(),
                verification_time=None,
                is_correct=None,
                accuracy_type='unknown',
                metadata=metadata or {}
            )
            
            with self._data_lock:
                self.prediction_records.append(record)
            
            self.logger.debug(f"记录预测: {prediction_id}")
            return prediction_id
            
        except Exception as e:
            self.logger.error(f"记录预测失败: {e}")
            raise PredictionException(f"记录预测失败: {e}")
    
    def verify_prediction(self, prediction_id: str, actual_value: str) -> bool:
        """
        验证预测结果
        
        Args:
            prediction_id: 预测记录ID
            actual_value: 实际值
            
        Returns:
            是否验证成功
        """
        try:
            with self._data_lock:
                # 查找预测记录
                record = None
                for r in self.prediction_records:
                    if r.prediction_id == prediction_id:
                        record = r
                        break
                
                if not record:
                    self.logger.warning(f"未找到预测记录: {prediction_id}")
                    return False
                
                # 更新验证信息
                record.actual_value = actual_value
                record.verification_time = datetime.now()
                record.is_correct, record.accuracy_type = self._calculate_accuracy(
                    record.predicted_value, actual_value
                )
                
                # 更新准确率历史
                self._update_accuracy_history(record)
                
                # 检查预警条件
                self._check_alert_conditions(record.model_name)
            
            self.logger.debug(f"验证预测: {prediction_id}, 正确: {record.is_correct}")
            return True

        except Exception as e:
            self.logger.error(f"验证预测失败: {e}")
            return False

    def get_accuracy_metrics(self, model_name: str, time_period: str = '24h') -> AccuracyMetrics:
        """
        获取准确率指标

        Args:
            model_name: 模型名称
            time_period: 时间周期 ('1h', '24h', '7d', '30d')

        Returns:
            准确率指标
        """
        try:
            # 检查缓存
            cache_key = f"accuracy_metrics_{model_name}_{time_period}"
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result

            # 计算时间范围
            time_delta = self._parse_time_period(time_period)
            cutoff_time = datetime.now() - time_delta

            # 筛选记录
            with self._data_lock:
                relevant_records = [
                    r for r in self.prediction_records
                    if (r.model_name == model_name and
                        r.verification_time and
                        r.verification_time >= cutoff_time and
                        r.is_correct is not None)
                ]

            if not relevant_records:
                return AccuracyMetrics(
                    direct_accuracy=0.0,
                    position_accuracy=0.0,
                    group_accuracy=0.0,
                    total_predictions=0,
                    correct_predictions=0,
                    confidence_avg=0.0,
                    confidence_std=0.0,
                    time_period=time_period,
                    last_updated=datetime.now()
                )

            # 计算各类准确率
            total_predictions = len(relevant_records)
            direct_correct = sum(1 for r in relevant_records if r.accuracy_type == 'direct' and r.is_correct)
            position_correct = sum(1 for r in relevant_records if r.accuracy_type == 'position' and r.is_correct)
            group_correct = sum(1 for r in relevant_records if r.accuracy_type == 'group' and r.is_correct)

            direct_total = sum(1 for r in relevant_records if r.accuracy_type == 'direct')
            position_total = sum(1 for r in relevant_records if r.accuracy_type == 'position')
            group_total = sum(1 for r in relevant_records if r.accuracy_type == 'group')

            # 计算置信度统计
            confidences = [r.confidence_score for r in relevant_records]

            metrics = AccuracyMetrics(
                direct_accuracy=direct_correct / direct_total if direct_total > 0 else 0.0,
                position_accuracy=position_correct / position_total if position_total > 0 else 0.0,
                group_accuracy=group_correct / group_total if group_total > 0 else 0.0,
                total_predictions=total_predictions,
                correct_predictions=sum(1 for r in relevant_records if r.is_correct),
                confidence_avg=float(np.mean(confidences)) if confidences else 0.0,
                confidence_std=float(np.std(confidences)) if confidences else 0.0,
                time_period=time_period,
                last_updated=datetime.now()
            )

            # 缓存结果
            self._cache_result(cache_key, metrics)

            return metrics

        except Exception as e:
            self.logger.error(f"获取准确率指标失败: {e}")
            raise PredictionException(f"获取准确率指标失败: {e}")

    def get_trend_analysis(self, model_name: str, metric: str = 'direct_accuracy',
                          days: int = 30) -> TrendAnalysis:
        """
        获取趋势分析

        Args:
            model_name: 模型名称
            metric: 指标名称
            days: 分析天数

        Returns:
            趋势分析结果
        """
        try:
            # 获取历史数据
            history_data = self._get_metric_history(model_name, metric, days)

            if len(history_data) < 3:
                return TrendAnalysis(
                    trend_direction='stable',
                    trend_strength=0.0,
                    slope=0.0,
                    r_squared=0.0,
                    forecast_7d=0.0,
                    forecast_30d=0.0,
                    confidence_interval=(0.0, 0.0)
                )

            # 准备数据
            x = np.arange(len(history_data))
            y = np.array([d['value'] for d in history_data])

            # 线性回归分析
            slope, intercept, r_value, p_value, std_err = self._linear_regression(x, y)
            r_squared = r_value ** 2

            # 趋势方向和强度
            trend_direction = 'up' if slope > 0.001 else 'down' if slope < -0.001 else 'stable'
            trend_strength = min(abs(slope) * 100, 1.0)  # 归一化到0-1

            # 预测未来值
            forecast_7d = intercept + slope * (len(history_data) + 7)
            forecast_30d = intercept + slope * (len(history_data) + 30)

            # 置信区间
            confidence_interval = self._calculate_confidence_interval(y, slope, std_err)

            return TrendAnalysis(
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                slope=slope,
                r_squared=r_squared,
                forecast_7d=max(0.0, min(1.0, forecast_7d)),
                forecast_30d=max(0.0, min(1.0, forecast_30d)),
                confidence_interval=confidence_interval
            )

        except Exception as e:
            self.logger.error(f"趋势分析失败: {e}")
            raise PredictionException(f"趋势分析失败: {e}")

    def get_active_alerts(self, model_name: Optional[str] = None) -> List[Alert]:
        """
        获取活跃预警

        Args:
            model_name: 模型名称（可选）

        Returns:
            活跃预警列表
        """
        try:
            with self._data_lock:
                alerts = list(self.active_alerts.values())

                if model_name:
                    alerts = [a for a in alerts if a.model_name == model_name]

                # 按级别和时间排序
                alerts.sort(key=lambda x: (x.level.value, x.timestamp), reverse=True)

                return alerts

        except Exception as e:
            self.logger.error(f"获取预警失败: {e}")
            return []

    def generate_monitoring_report(self, model_name: Optional[str] = None,
                                 time_period: str = '24h') -> Dict[str, Any]:
        """
        生成监控报告

        Args:
            model_name: 模型名称（可选，为空则生成所有模型报告）
            time_period: 时间周期

        Returns:
            监控报告
        """
        try:
            report = {
                'report_time': datetime.now().isoformat(),
                'time_period': time_period,
                'models': {}
            }

            # 确定要报告的模型
            if model_name:
                model_names = [model_name]
            else:
                model_names = self._get_active_models()

            # 为每个模型生成报告
            for name in model_names:
                model_report = {
                    'accuracy_metrics': self.get_accuracy_metrics(name, time_period),
                    'trend_analysis': self.get_trend_analysis(name),
                    'active_alerts': self.get_active_alerts(name),
                    'prediction_volume': self._get_prediction_volume(name, time_period)
                }
                report['models'][name] = model_report

            # 添加整体统计
            report['summary'] = self._generate_summary_stats(report['models'])

            return report

        except Exception as e:
            self.logger.error(f"生成监控报告失败: {e}")
            return {'error': str(e)}

    # 私有辅助方法
    def _monitoring_loop(self):
        """监控主循环"""
        while not self._stop_monitoring.is_set():
            try:
                # 执行定期监控任务
                self._periodic_monitoring_tasks()

                # 等待下一个监控周期
                self._stop_monitoring.wait(self.monitoring_interval)

            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(60)  # 错误时等待1分钟

    def _periodic_monitoring_tasks(self):
        """定期监控任务"""
        try:
            # 清理过期数据
            self._cleanup_expired_data()

            # 更新统计缓存
            self._update_stats_cache()

            # 检查所有模型的预警条件
            for model_name in self._get_active_models():
                self._check_alert_conditions(model_name)

            # 清理已解决的预警
            self._cleanup_resolved_alerts()

        except Exception as e:
            self.logger.error(f"定期监控任务失败: {e}")

    def _calculate_accuracy(self, predicted: str, actual: str) -> Tuple[bool, str]:
        """
        计算预测准确性

        Args:
            predicted: 预测值
            actual: 实际值

        Returns:
            (是否正确, 准确性类型)
        """
        try:
            # 直接匹配
            if predicted == actual:
                return True, 'direct'

            # 位置匹配（福彩3D特定逻辑）
            if len(predicted) == 3 and len(actual) == 3:
                position_matches = sum(1 for i in range(3) if predicted[i] == actual[i])
                if position_matches >= 2:
                    return True, 'position'

                # 组合匹配（数字相同但位置不同）
                pred_digits = sorted(predicted)
                actual_digits = sorted(actual)
                if pred_digits == actual_digits:
                    return True, 'group'

            return False, 'none'

        except Exception as e:
            self.logger.error(f"计算准确性失败: {e}")
            return False, 'error'

    def _update_accuracy_history(self, record: PredictionRecord):
        """更新准确率历史"""
        try:
            model_name = record.model_name
            accuracy_value = 1.0 if record.is_correct else 0.0

            history_entry = {
                'timestamp': record.verification_time,
                'value': accuracy_value,
                'accuracy_type': record.accuracy_type,
                'confidence': record.confidence_score
            }

            self.accuracy_history[model_name].append(history_entry)

        except Exception as e:
            self.logger.error(f"更新准确率历史失败: {e}")

    def _check_alert_conditions(self, model_name: str):
        """检查预警条件"""
        try:
            # 获取最近的准确率指标
            current_metrics = self.get_accuracy_metrics(model_name, '1h')
            baseline_metrics = self.get_accuracy_metrics(model_name, '24h')

            # 检查准确率下降
            accuracy_drop = baseline_metrics.direct_accuracy - current_metrics.direct_accuracy
            if accuracy_drop > self.alert_thresholds['accuracy_drop']:
                self._create_alert(
                    model_name=model_name,
                    level=AlertLevel.WARNING,
                    message=f"准确率下降 {accuracy_drop:.2%}",
                    metric_name='direct_accuracy',
                    current_value=current_metrics.direct_accuracy,
                    threshold_value=baseline_metrics.direct_accuracy - self.alert_thresholds['accuracy_drop']
                )

            # 检查置信度下降
            confidence_drop = baseline_metrics.confidence_avg - current_metrics.confidence_avg
            if confidence_drop > self.alert_thresholds['confidence_drop']:
                self._create_alert(
                    model_name=model_name,
                    level=AlertLevel.WARNING,
                    message=f"置信度下降 {confidence_drop:.2%}",
                    metric_name='confidence_avg',
                    current_value=current_metrics.confidence_avg,
                    threshold_value=baseline_metrics.confidence_avg - self.alert_thresholds['confidence_drop']
                )

        except Exception as e:
            self.logger.error(f"检查预警条件失败: {e}")

    def _create_alert(self, model_name: str, level: AlertLevel, message: str,
                     metric_name: str, current_value: float, threshold_value: float):
        """创建预警"""
        try:
            alert_id = f"{model_name}_{metric_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            alert = Alert(
                alert_id=alert_id,
                level=level,
                message=message,
                model_name=model_name,
                metric_name=metric_name,
                current_value=current_value,
                threshold_value=threshold_value,
                timestamp=datetime.now(),
                is_resolved=False,
                metadata={}
            )

            with self._data_lock:
                self.active_alerts[alert_id] = alert

            self.logger.warning(f"创建预警: {message} (模型: {model_name})")

        except Exception as e:
            self.logger.error(f"创建预警失败: {e}")

    def _get_cached_result(self, cache_key: str) -> Optional[Any]:
        """获取缓存结果"""
        try:
            if cache_key in self._stats_cache:
                cached_time, result = self._stats_cache[cache_key]
                if (datetime.now() - cached_time).seconds < self._cache_ttl:
                    return result
            return None
        except Exception:
            return None

    def _cache_result(self, cache_key: str, result: Any):
        """缓存结果"""
        try:
            self._stats_cache[cache_key] = (datetime.now(), result)
        except Exception as e:
            self.logger.error(f"缓存结果失败: {e}")

    def _parse_time_period(self, time_period: str) -> timedelta:
        """解析时间周期"""
        try:
            if time_period.endswith('h'):
                hours = int(time_period[:-1])
                return timedelta(hours=hours)
            elif time_period.endswith('d'):
                days = int(time_period[:-1])
                return timedelta(days=days)
            else:
                return timedelta(hours=24)  # 默认24小时
        except Exception:
            return timedelta(hours=24)

    def _get_active_models(self) -> List[str]:
        """获取活跃模型列表"""
        try:
            with self._data_lock:
                models = set()
                for record in self.prediction_records:
                    models.add(record.model_name)
                return list(models)
        except Exception:
            return ['trend_analysis', 'pattern_prediction', 'lstm_sequence', 'intelligent_fusion']

    def _get_prediction_volume(self, model_name: str, time_period: str) -> int:
        """获取预测量"""
        try:
            time_delta = self._parse_time_period(time_period)
            cutoff_time = datetime.now() - time_delta

            with self._data_lock:
                count = sum(1 for r in self.prediction_records
                           if r.model_name == model_name and r.prediction_time >= cutoff_time)
                return count
        except Exception:
            return 0

    def _generate_summary_stats(self, models_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总统计"""
        try:
            if not models_data:
                return {}

            total_predictions = sum(
                model_data['accuracy_metrics'].total_predictions
                for model_data in models_data.values()
            )

            avg_accuracy = np.mean([
                model_data['accuracy_metrics'].direct_accuracy
                for model_data in models_data.values()
            ]) if models_data else 0.0

            total_alerts = sum(
                len(model_data['active_alerts'])
                for model_data in models_data.values()
            )

            return {
                'total_predictions': total_predictions,
                'average_accuracy': float(avg_accuracy),
                'total_active_alerts': total_alerts,
                'models_count': len(models_data)
            }
        except Exception as e:
            self.logger.error(f"生成汇总统计失败: {e}")
            return {}

    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            # 清理过期的预测记录（保留最近30天）
            cutoff_time = datetime.now() - timedelta(days=30)

            with self._data_lock:
                # 清理预测记录
                self.prediction_records = deque(
                    [r for r in self.prediction_records if r.prediction_time >= cutoff_time],
                    maxlen=self.prediction_records.maxlen
                )

                # 清理准确率历史
                for model_name in list(self.accuracy_history.keys()):
                    self.accuracy_history[model_name] = deque(
                        [h for h in self.accuracy_history[model_name]
                         if h['timestamp'] and h['timestamp'] >= cutoff_time],
                        maxlen=self.accuracy_history[model_name].maxlen
                    )

        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")

    def _update_stats_cache(self):
        """更新统计缓存"""
        try:
            # 清理过期缓存
            current_time = datetime.now()
            expired_keys = []

            for key, (cached_time, _) in self._stats_cache.items():
                if (current_time - cached_time).seconds > self._cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self._stats_cache[key]

        except Exception as e:
            self.logger.error(f"更新统计缓存失败: {e}")

    def _cleanup_resolved_alerts(self):
        """清理已解决的预警"""
        try:
            with self._data_lock:
                # 移除超过24小时的已解决预警
                cutoff_time = datetime.now() - timedelta(hours=24)
                resolved_alerts = []

                for alert_id, alert in self.active_alerts.items():
                    if alert.is_resolved and alert.timestamp < cutoff_time:
                        resolved_alerts.append(alert_id)

                for alert_id in resolved_alerts:
                    del self.active_alerts[alert_id]

        except Exception as e:
            self.logger.error(f"清理已解决预警失败: {e}")

    def _get_metric_history(self, model_name: str, metric: str, days: int) -> List[Dict[str, Any]]:
        """获取指标历史数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)

            with self._data_lock:
                history = self.accuracy_history.get(model_name, [])

                # 筛选时间范围内的数据
                filtered_history = [
                    h for h in history
                    if h['timestamp'] and h['timestamp'] >= cutoff_time
                ]

                # 按指标类型筛选
                if metric == 'direct_accuracy':
                    return [h for h in filtered_history if h.get('accuracy_type') == 'direct']
                elif metric == 'confidence':
                    return [{'timestamp': h['timestamp'], 'value': h['confidence']}
                           for h in filtered_history]
                else:
                    return filtered_history

        except Exception as e:
            self.logger.error(f"获取指标历史失败: {e}")
            return []

    def _linear_regression(self, x: np.ndarray, y: np.ndarray) -> Tuple[float, float, float, float, float]:
        """线性回归分析"""
        # 简化的线性回归实现
        n = len(x)
        if n < 2:
            return 0.0, 0.0, 0.0, 1.0, 0.0

        x_mean = float(np.mean(x))
        y_mean = float(np.mean(y))

        numerator = float(np.sum((x - x_mean) * (y - y_mean)))
        denominator = float(np.sum((x - x_mean) ** 2))

        if denominator == 0:
            return 0.0, y_mean, 0.0, 1.0, 0.0

        slope = numerator / denominator
        intercept = y_mean - slope * x_mean

        # 计算相关系数
        y_pred = slope * x + intercept
        ss_res = float(np.sum((y - y_pred) ** 2))
        ss_tot = float(np.sum((y - y_mean) ** 2))
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        r_value = float(np.sqrt(r_squared)) if r_squared >= 0 else 0.0

        # 标准误差
        std_err = float(np.sqrt(ss_res / (n - 2))) if n > 2 else 0.0

        return slope, intercept, r_value, 1.0, std_err

    def _calculate_confidence_interval(self, y: np.ndarray, slope: float, std_err: float) -> Tuple[float, float]:
        """计算置信区间"""
        try:
            # 简化的95%置信区间计算
            margin = 1.96 * std_err  # 95%置信区间
            y_mean = np.mean(y)

            lower = max(0.0, float(y_mean - margin))
            upper = min(1.0, float(y_mean + margin))

            return (lower, upper)
        except Exception:
            return (0.0, 1.0)


# 集成接口和工厂函数
class PredictionMonitorIntegration:
    """预测监控系统集成接口"""

    def __init__(self, monitor: PredictionMonitor):
        self.monitor = monitor
        self.logger = logging.getLogger(__name__)

    def integrate_with_performance_tracker(self, performance_tracker: ModelPerformanceTracker) -> bool:
        """
        与ModelPerformanceTracker集成

        Args:
            performance_tracker: 性能跟踪器实例

        Returns:
            是否集成成功
        """
        try:
            # 设置性能跟踪器引用
            self.monitor.performance_tracker = performance_tracker

            # 同步历史数据
            self._sync_historical_data(performance_tracker)

            self.logger.info("与ModelPerformanceTracker集成成功")
            return True

        except Exception as e:
            self.logger.error(f"集成失败: {e}")
            return False

    def _sync_historical_data(self, performance_tracker: ModelPerformanceTracker):
        """同步历史数据"""
        try:
            # 从性能跟踪器获取历史数据并同步到监控器
            # 这里可以根据实际的ModelPerformanceTracker接口进行调整
            pass
        except Exception as e:
            self.logger.error(f"同步历史数据失败: {e}")

    def create_monitoring_hooks(self) -> Dict[str, Any]:
        """
        创建监控钩子函数

        Returns:
            监控钩子函数字典
        """
        return {
            'on_prediction': self.monitor.record_prediction,
            'on_verification': self.monitor.verify_prediction,
            'get_metrics': self.monitor.get_accuracy_metrics,
            'get_alerts': self.monitor.get_active_alerts,
            'generate_report': self.monitor.generate_monitoring_report
        }


def create_prediction_monitor(config: Optional[Dict[str, Any]] = None) -> PredictionMonitor:
    """
    创建预测监控器实例

    Args:
        config: 配置参数

    Returns:
        预测监控器实例
    """
    try:
        # 默认配置
        default_config = {
            'monitoring_interval': 60,
            'alert_thresholds': {
                'accuracy_drop': 0.1,
                'confidence_drop': 0.15,
                'prediction_volume': 0.5
            },
            'cache_ttl': 300,
            'max_records': 10000
        }

        # 合并配置
        final_config = {**default_config, **(config or {})}

        # 创建监控器
        monitor = PredictionMonitor(final_config)

        # 启动监控
        monitor.start_monitoring()

        return monitor

    except Exception as e:
        logging.getLogger(__name__).error(f"创建预测监控器失败: {e}")
        raise


def create_integrated_monitor(performance_tracker: ModelPerformanceTracker,
                            config: Optional[Dict[str, Any]] = None) -> Tuple[PredictionMonitor, PredictionMonitorIntegration]:
    """
    创建集成的预测监控器

    Args:
        performance_tracker: 性能跟踪器实例
        config: 配置参数

    Returns:
        (预测监控器, 集成接口)
    """
    try:
        # 创建监控器
        monitor = create_prediction_monitor(config)

        # 创建集成接口
        integration = PredictionMonitorIntegration(monitor)

        # 执行集成
        integration.integrate_with_performance_tracker(performance_tracker)

        return monitor, integration

    except Exception as e:
        logging.getLogger(__name__).error(f"创建集成监控器失败: {e}")
        raise


# 导出主要类和函数
__all__ = [
    'PredictionMonitor',
    'PredictionMonitorIntegration',
    'PredictionRecord',
    'AccuracyMetrics',
    'TrendAnalysis',
    'Alert',
    'AlertLevel',
    'create_prediction_monitor',
    'create_integrated_monitor'
]
