"""
增强状态指示器组件
提供实时系统状态、连接状态和数据状态的可视化显示
"""

import streamlit as st
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import time
import json


class StatusType:
    """状态类型常量"""
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INFO = "info"
    LOADING = "loading"


class EnhancedStatusIndicators:
    """增强状态指示器"""
    
    def __init__(self):
        """初始化状态指示器"""
        self.status_history = []
        self.last_update = datetime.now()
        self._inject_status_styles()
    
    def _inject_status_styles(self):
        """注入状态指示器样式"""
        status_css = """
        <style>
        /* 状态指示器容器 */
        .status-dashboard {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: 20px;
            padding: 24px;
            margin: 16px 0;
            border: 1px solid rgba(255,255,255,0.15);
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .status-title {
            color: white;
            font-size: 18px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-timestamp {
            color: rgba(255,255,255,0.7);
            font-size: 12px;
            font-weight: 500;
        }
        
        /* 状态网格 */
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: rgba(255,255,255,0.08);
            border: 1px solid rgba(255,255,255,0.15);
            border-radius: 16px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .status-card:hover {
            background: rgba(255,255,255,0.12);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--status-color);
        }
        
        /* 状态类型颜色 */
        .status-success { --status-color: #10b981; }
        .status-warning { --status-color: #f59e0b; }
        .status-error { --status-color: #ef4444; }
        .status-info { --status-color: #3b82f6; }
        .status-loading { --status-color: #8b5cf6; }
        
        .status-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .status-icon {
            font-size: 24px;
            margin-right: 8px;
        }
        
        .status-label {
            color: white;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
        }
        
        .status-badge {
            background: var(--status-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
        }
        
        .status-value {
            color: var(--status-color);
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .status-description {
            color: rgba(255,255,255,0.7);
            font-size: 12px;
            line-height: 1.4;
        }
        
        /* 状态趋势图 */
        .status-trend {
            height: 40px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            margin-top: 12px;
            position: relative;
            overflow: hidden;
        }
        
        .status-trend-line {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(to top, var(--status-color), transparent);
            width: 100%;
            opacity: 0.3;
            animation: trend-pulse 2s ease-in-out infinite;
        }
        
        @keyframes trend-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }
        
        /* 实时状态指示器 */
        .realtime-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 16px;
            padding: 12px;
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .realtime-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: realtime-blink 1.5s ease-in-out infinite;
        }
        
        @keyframes realtime-blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        .realtime-text {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
            font-weight: 500;
        }
        
        /* 加载动画 */
        .status-loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: var(--status-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 状态历史 */
        .status-history {
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
        }
        
        .status-history-header {
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-history-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .status-history-item:last-child {
            border-bottom: none;
        }
        
        .status-history-time {
            color: rgba(255,255,255,0.6);
            font-size: 11px;
            min-width: 60px;
        }
        
        .status-history-message {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
            flex: 1;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .status-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .status-card {
                padding: 16px;
            }
            
            .status-dashboard {
                padding: 16px;
                margin: 12px 0;
            }
        }
        </style>
        """
        
        st.markdown(status_css, unsafe_allow_html=True)
    
    def create_status_card(self, 
                          icon: str,
                          label: str, 
                          value: str,
                          description: str,
                          status_type: str = StatusType.SUCCESS,
                          show_trend: bool = False,
                          trend_data: Optional[List[float]] = None) -> str:
        """创建状态卡片"""
        
        badge_text = {
            StatusType.SUCCESS: "正常",
            StatusType.WARNING: "警告", 
            StatusType.ERROR: "错误",
            StatusType.INFO: "信息",
            StatusType.LOADING: "加载中"
        }.get(status_type, "未知")
        
        trend_html = ""
        if show_trend and trend_data:
            trend_html = '''
            <div class="status-trend">
                <div class="status-trend-line"></div>
            </div>
            '''
        
        loading_html = ""
        if status_type == StatusType.LOADING:
            loading_html = '<div class="status-loading-spinner"></div>'
        
        card_html = f'''
        <div class="status-card status-{status_type}">
            <div class="status-card-header">
                <div style="display: flex; align-items: center;">
                    <span class="status-icon">{icon}</span>
                    <span class="status-label">{label}</span>
                </div>
                <div class="status-badge">{badge_text}</div>
            </div>
            <div class="status-value">{loading_html}{value}</div>
            <div class="status-description">{description}</div>
            {trend_html}
        </div>
        '''
        
        return card_html
    
    def render_system_status_dashboard(self) -> None:
        """渲染系统状态仪表板"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 模拟获取系统状态数据
        api_status = self._get_api_status()
        data_status = self._get_data_status()
        performance_status = self._get_performance_status()
        connection_status = self._get_connection_status()
        
        dashboard_html = f'''
        <div class="status-dashboard">
            <div class="status-header">
                <div class="status-title">
                    <span>📊</span>
                    <span>系统状态监控</span>
                </div>
                <div class="status-timestamp">最后更新: {current_time}</div>
            </div>
            
            <div class="status-grid">
                {self.create_status_card("🔗", "API连接", api_status["value"], api_status["description"], api_status["type"])}
                {self.create_status_card("💾", "数据状态", data_status["value"], data_status["description"], data_status["type"])}
                {self.create_status_card("⚡", "系统性能", performance_status["value"], performance_status["description"], performance_status["type"])}
                {self.create_status_card("🌐", "网络连接", connection_status["value"], connection_status["description"], connection_status["type"])}
            </div>
            
            <div class="realtime-indicator">
                <div class="realtime-dot"></div>
                <span class="realtime-text">实时监控中 - 数据每30秒更新</span>
            </div>
        </div>
        '''
        
        st.markdown(dashboard_html, unsafe_allow_html=True)
    
    def _get_api_status(self) -> Dict[str, str]:
        """获取API状态"""
        # 这里应该实际检查API状态
        return {
            "value": "✅ 正常",
            "description": "API服务响应正常，延迟 < 100ms",
            "type": StatusType.SUCCESS
        }
    
    def _get_data_status(self) -> Dict[str, str]:
        """获取数据状态"""
        return {
            "value": "🔄 实时",
            "description": "数据同步正常，最新更新5分钟前",
            "type": StatusType.SUCCESS
        }
    
    def _get_performance_status(self) -> Dict[str, str]:
        """获取性能状态"""
        return {
            "value": "🚀 优秀",
            "description": "CPU: 15%, 内存: 45%, 响应时间: 0.8s",
            "type": StatusType.SUCCESS
        }
    
    def _get_connection_status(self) -> Dict[str, str]:
        """获取连接状态"""
        return {
            "value": "🔒 安全",
            "description": "HTTPS连接，WebSocket稳定",
            "type": StatusType.SUCCESS
        }
    
    def add_status_history(self, message: str, status_type: str = StatusType.INFO):
        """添加状态历史记录"""
        self.status_history.append({
            "time": datetime.now(),
            "message": message,
            "type": status_type
        })
        
        # 保持最近50条记录
        if len(self.status_history) > 50:
            self.status_history = self.status_history[-50:]
    
    def render_status_history(self, max_items: int = 10) -> None:
        """渲染状态历史"""
        if not self.status_history:
            return
        
        history_html = '''
        <div class="status-history">
            <div class="status-history-header">
                <span>📋</span>
                <span>状态历史</span>
            </div>
        '''
        
        recent_items = self.status_history[-max_items:]
        for item in reversed(recent_items):
            time_str = item["time"].strftime("%H:%M")
            history_html += f'''
            <div class="status-history-item">
                <div class="status-history-time">{time_str}</div>
                <div class="status-history-message">{item["message"]}</div>
            </div>
            '''
        
        history_html += '</div>'
        st.markdown(history_html, unsafe_allow_html=True)
