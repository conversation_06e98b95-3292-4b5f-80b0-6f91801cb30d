"""
融合系统共享上下文

管理各模块间的共享资源和配置信息。
"""

import logging
from pathlib import Path
from typing import Any, Dict, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class FusionContext:
    """融合系统共享上下文类"""
    
    # 路径配置
    db_path: Optional[str] = None
    cache_dir: Optional[Path] = None
    training_state_file: Optional[Path] = None
    
    # 预测器实例
    trend_analyzer: Optional[Any] = None
    pattern_predictor: Optional[Any] = None
    fusion_system: Optional[Any] = None
    quality_monitor: Optional[Any] = None
    cross_validator: Optional[Any] = None
    model_selector: Optional[Any] = None
    
    # 状态信息
    models_trained: bool = False
    fusion_ready: bool = False
    last_training_time: Optional[str] = None
    training_data_count: int = 0
    
    # 验证配置
    validation_enabled: bool = True
    last_validation_time: Optional[str] = None
    validation_results: Dict[str, Any] = field(default_factory=dict)
    
    # 缓存数据
    last_data_hash: Optional[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.cache_dir and not self.cache_dir.exists():
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建缓存目录: {self.cache_dir}")
    
    def is_ready(self) -> bool:
        """检查上下文是否就绪"""
        return (
            self.db_path is not None and
            self.cache_dir is not None and
            self.trend_analyzer is not None and
            self.pattern_predictor is not None and
            self.fusion_system is not None
        )
    
    def get_cache_path(self, filename: str) -> Path:
        """获取缓存文件路径"""
        if not self.cache_dir:
            raise ValueError("缓存目录未设置")
        return self.cache_dir / filename
    
    def update_training_status(self, trained: bool = True, data_count: int = 0):
        """更新训练状态"""
        self.models_trained = trained
        self.training_data_count = data_count
        if trained:
            from datetime import datetime
            self.last_training_time = datetime.now().isoformat()
            logger.info(f"更新训练状态: 已训练, 数据量: {data_count}")
    
    def update_validation_status(self, results: Dict[str, Any]):
        """更新验证状态"""
        self.validation_results = results
        from datetime import datetime
        self.last_validation_time = datetime.now().isoformat()
        logger.info("更新验证状态完成")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取上下文摘要信息"""
        return {
            "ready": self.is_ready(),
            "models_trained": self.models_trained,
            "fusion_ready": self.fusion_ready,
            "training_data_count": self.training_data_count,
            "last_training_time": self.last_training_time,
            "validation_enabled": self.validation_enabled,
            "last_validation_time": self.last_validation_time,
            "cache_dir": str(self.cache_dir) if self.cache_dir else None,
            "db_path": self.db_path
        }
