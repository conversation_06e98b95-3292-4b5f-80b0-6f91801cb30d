"""
推理引擎

提供高效的模型推理、批处理、缓存等功能。
"""

import torch
import torch.nn as nn
from torch.cuda.amp import autocast
from typing import Dict, Any, Optional, List, Union, Tuple
import numpy as np
import logging
import time
from collections import OrderedDict
import threading
from queue import Queue, Empty

logger = logging.getLogger(__name__)


class InferenceEngine:
    """推理引擎"""
    
    def __init__(
        self,
        model: nn.Module,
        device: str = 'cpu',
        use_mixed_precision: bool = True,
        max_batch_size: int = 32,
        cache_size: int = 1000
    ):
        self.model = model.to(device)
        self.device = device
        self.use_mixed_precision = use_mixed_precision and device != 'cpu'
        self.max_batch_size = max_batch_size
        
        # 设置模型为评估模式
        self.model.eval()
        
        # 推理缓存
        self.cache = OrderedDict()
        self.cache_size = cache_size
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 批处理队列
        self.batch_queue = Queue()
        self.result_queues = {}
        self.batch_thread = None
        self.is_running = False
        
        # 性能统计
        self.inference_stats = {
            'total_inferences': 0,
            'total_time': 0.0,
            'batch_inferences': 0,
            'single_inferences': 0
        }
        
        # 优化模型
        self._optimize_model()
        
        logger.info(f"推理引擎初始化完成，设备: {device}, 混合精度: {use_mixed_precision}")
    
    def _optimize_model(self):
        """优化模型以进行推理"""
        # 禁用梯度计算
        for param in self.model.parameters():
            param.requires_grad = False
        
        # 尝试使用TorchScript优化
        try:
            # 创建示例输入
            example_input = torch.randn(1, 10, 3).to(self.device)
            self.model = torch.jit.trace(self.model, example_input)
            logger.info("模型已使用TorchScript优化")
        except Exception as e:
            logger.warning(f"TorchScript优化失败: {e}")
        
        # 设置优化标志
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
    
    def predict_single(
        self,
        input_data: Union[torch.Tensor, np.ndarray],
        use_cache: bool = True
    ) -> torch.Tensor:
        """
        单个样本推理
        
        Args:
            input_data: 输入数据
            use_cache: 是否使用缓存
            
        Returns:
            预测结果
        """
        start_time = time.time()
        
        # 转换输入数据
        if isinstance(input_data, np.ndarray):
            input_tensor = torch.from_numpy(input_data).float()
        else:
            input_tensor = input_data.float()
        
        # 确保输入维度正确
        if input_tensor.dim() == 2:
            input_tensor = input_tensor.unsqueeze(0)  # 添加batch维度
        
        input_tensor = input_tensor.to(self.device)
        
        # 检查缓存
        if use_cache:
            cache_key = self._get_cache_key(input_tensor)
            if cache_key in self.cache:
                self.cache_hits += 1
                # 移动到末尾（LRU）
                self.cache.move_to_end(cache_key)
                return self.cache[cache_key]
            else:
                self.cache_misses += 1
        
        # 执行推理
        with torch.no_grad():
            if self.use_mixed_precision:
                with autocast():
                    output = self.model(input_tensor)
            else:
                output = self.model(input_tensor)
        
        # 更新缓存
        if use_cache:
            self._update_cache(cache_key, output.clone())
        
        # 更新统计
        inference_time = time.time() - start_time
        self.inference_stats['total_inferences'] += 1
        self.inference_stats['total_time'] += inference_time
        self.inference_stats['single_inferences'] += 1
        
        return output
    
    def predict_batch(
        self,
        input_data: Union[torch.Tensor, np.ndarray, List],
        batch_size: Optional[int] = None
    ) -> torch.Tensor:
        """
        批量推理
        
        Args:
            input_data: 输入数据
            batch_size: 批处理大小
            
        Returns:
            预测结果
        """
        start_time = time.time()
        
        # 转换输入数据
        if isinstance(input_data, list):
            input_tensor = torch.stack([
                torch.from_numpy(x) if isinstance(x, np.ndarray) else x 
                for x in input_data
            ]).float()
        elif isinstance(input_data, np.ndarray):
            input_tensor = torch.from_numpy(input_data).float()
        else:
            input_tensor = input_data.float()
        
        input_tensor = input_tensor.to(self.device)
        
        if batch_size is None:
            batch_size = min(self.max_batch_size, input_tensor.size(0))
        
        # 分批处理
        outputs = []
        for i in range(0, input_tensor.size(0), batch_size):
            batch_input = input_tensor[i:i + batch_size]
            
            with torch.no_grad():
                if self.use_mixed_precision:
                    with autocast():
                        batch_output = self.model(batch_input)
                else:
                    batch_output = self.model(batch_input)
            
            outputs.append(batch_output)
        
        # 合并结果
        result = torch.cat(outputs, dim=0)
        
        # 更新统计
        inference_time = time.time() - start_time
        self.inference_stats['total_inferences'] += input_tensor.size(0)
        self.inference_stats['total_time'] += inference_time
        self.inference_stats['batch_inferences'] += input_tensor.size(0)
        
        return result
    
    def start_batch_service(self, max_wait_time: float = 0.1):
        """启动批处理服务"""
        if self.is_running:
            logger.warning("批处理服务已在运行")
            return
        
        self.is_running = True
        self.batch_thread = threading.Thread(
            target=self._batch_worker,
            args=(max_wait_time,),
            daemon=True
        )
        self.batch_thread.start()
        logger.info("批处理服务已启动")
    
    def stop_batch_service(self):
        """停止批处理服务"""
        self.is_running = False
        if self.batch_thread is not None:
            self.batch_thread.join()
        logger.info("批处理服务已停止")
    
    def predict_async(self, input_data: Union[torch.Tensor, np.ndarray]) -> str:
        """
        异步推理
        
        Args:
            input_data: 输入数据
            
        Returns:
            请求ID
        """
        if not self.is_running:
            raise RuntimeError("批处理服务未启动，请先调用start_batch_service()")
        
        request_id = f"req_{time.time()}_{id(input_data)}"
        result_queue = Queue()
        self.result_queues[request_id] = result_queue
        
        self.batch_queue.put((request_id, input_data))
        return request_id
    
    def get_result(self, request_id: str, timeout: float = 5.0) -> Optional[torch.Tensor]:
        """
        获取异步推理结果
        
        Args:
            request_id: 请求ID
            timeout: 超时时间
            
        Returns:
            预测结果
        """
        if request_id not in self.result_queues:
            return None
        
        try:
            result = self.result_queues[request_id].get(timeout=timeout)
            del self.result_queues[request_id]
            return result
        except Empty:
            logger.warning(f"请求 {request_id} 超时")
            return None
    
    def _batch_worker(self, max_wait_time: float):
        """批处理工作线程"""
        while self.is_running:
            batch_requests = []
            batch_data = []
            
            # 收集批处理请求
            start_time = time.time()
            while (len(batch_requests) < self.max_batch_size and 
                   time.time() - start_time < max_wait_time):
                try:
                    request_id, input_data = self.batch_queue.get(timeout=0.01)
                    batch_requests.append(request_id)
                    batch_data.append(input_data)
                except Empty:
                    if batch_requests:
                        break
                    continue
            
            if not batch_requests:
                continue
            
            # 执行批处理推理
            try:
                batch_results = self.predict_batch(batch_data)
                
                # 分发结果
                for i, request_id in enumerate(batch_requests):
                    if request_id in self.result_queues:
                        self.result_queues[request_id].put(batch_results[i:i+1])
            except Exception as e:
                logger.error(f"批处理推理失败: {e}")
                # 向所有请求返回错误
                for request_id in batch_requests:
                    if request_id in self.result_queues:
                        self.result_queues[request_id].put(None)
    
    def _get_cache_key(self, input_tensor: torch.Tensor) -> str:
        """生成缓存键"""
        # 使用输入张量的哈希作为缓存键
        return str(hash(input_tensor.cpu().numpy().tobytes()))
    
    def _update_cache(self, key: str, value: torch.Tensor):
        """更新缓存"""
        if len(self.cache) >= self.cache_size:
            # 删除最旧的条目（LRU）
            self.cache.popitem(last=False)
        
        self.cache[key] = value
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0.0
        
        return {
            'cache_size': len(self.cache),
            'max_cache_size': self.cache_size,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': hit_rate
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_time = (self.inference_stats['total_time'] / 
                   self.inference_stats['total_inferences'] 
                   if self.inference_stats['total_inferences'] > 0 else 0.0)
        
        throughput = (self.inference_stats['total_inferences'] / 
                     self.inference_stats['total_time'] 
                     if self.inference_stats['total_time'] > 0 else 0.0)
        
        return {
            **self.inference_stats,
            'avg_inference_time': avg_time,
            'throughput_per_sec': throughput,
            **self.get_cache_stats()
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("推理缓存已清空")
    
    def warmup(self, input_shape: Tuple[int, ...], num_warmup: int = 10):
        """模型预热"""
        logger.info(f"开始模型预热，预热次数: {num_warmup}")
        
        dummy_input = torch.randn(*input_shape).to(self.device)
        
        for i in range(num_warmup):
            with torch.no_grad():
                if self.use_mixed_precision:
                    with autocast():
                        _ = self.model(dummy_input)
                else:
                    _ = self.model(dummy_input)
        
        logger.info("模型预热完成")


if __name__ == "__main__":
    # 测试推理引擎
    from optimized_cnn_lstm import create_optimized_model
    
    config = {'input_size': 3, 'hidden_size': 64, 'num_classes': 1000}
    model = create_optimized_model(config)
    
    engine = InferenceEngine(model, device='cpu')
    
    # 测试单个推理
    test_input = torch.randn(10, 3)
    result = engine.predict_single(test_input)
    print(f"单个推理结果形状: {result.shape}")
    
    # 测试批量推理
    batch_input = torch.randn(5, 10, 3)
    batch_result = engine.predict_batch(batch_input)
    print(f"批量推理结果形状: {batch_result.shape}")
    
    # 性能统计
    stats = engine.get_performance_stats()
    print(f"性能统计: {stats}")
