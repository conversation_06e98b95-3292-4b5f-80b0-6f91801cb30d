# Streamlit界面启动方式 - 唯一标准

## ✅ 唯一正确启动命令
```bash
python -m streamlit run src/ui/main.py
```

## ❌ 严禁使用的错误命令
```bash
# 以下命令严禁使用，已从开发流程中移除
python start_streamlit.py           # 需要用户交互，不适合标准化
streamlit run src/ui/main.py        # 缺少python -m前缀，可能有路径问题
```

## 🔍 技术原理
- 使用Python模块系统直接调用Streamlit
- 依赖Streamlit默认配置（端口8501，localhost绑定）
- 符合Streamlit官方标准和最佳实践
- 自动化友好，无需用户交互
- 零维护成本，无需自定义脚本

## 📍 关键要求
1. **前置条件**: 必须先启动API服务 `python start_production_api.py`
2. **工作目录**: 必须在项目根目录执行
3. **端口检查**: 确保8501端口未被占用
4. **环境要求**: 确保虚拟环境已激活

## ✅ 验证方法
```bash
# 启动后访问以下地址验证
http://127.0.0.1:8501
```
预期结果：界面正常加载，API连接状态显示正常

## 🚨 重要提醒
- 项目只使用一种界面启动方式，便于标准化开发
- 任何其他启动方式都不应在文档或代码中出现
- 此命令已通过深度测试验证，确保可靠性