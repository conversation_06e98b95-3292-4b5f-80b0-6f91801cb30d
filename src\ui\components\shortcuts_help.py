#!/usr/bin/env python3
"""
快捷键帮助文档组件
创建日期: 2025年7月31日
用途: 提供详细的快捷键帮助文档和使用指南
"""

import streamlit as st
import streamlit.components.v1 as components
from typing import Dict, List


class ShortcutsHelpManager:
    """快捷键帮助管理器"""
    
    def __init__(self):
        self.shortcuts_data = self._get_shortcuts_data()
    
    def _get_shortcuts_data(self) -> Dict[str, List[Dict]]:
        """获取快捷键数据"""
        return {
            "通用操作": [
                {"key": "Ctrl+H", "desc": "显示/隐藏快捷键帮助", "tip": "随时查看所有可用快捷键"},
                {"key": "Ctrl+R", "desc": "刷新页面数据", "tip": "重新加载当前页面的数据"},
                {"key": "F1", "desc": "显示帮助文档", "tip": "打开详细的使用帮助"},
                {"key": "F5", "desc": "强制刷新页面", "tip": "完全重新加载页面"},
                {"key": "Escape", "desc": "关闭当前对话框", "tip": "关闭弹出的对话框或帮助窗口"}
            ],
            "页面导航": [
                {"key": "Ctrl+D", "desc": "切换到数据概览", "tip": "快速访问数据概览页面"},
                {"key": "Ctrl+F", "desc": "切换到频率分析", "tip": "快速访问频率分析页面"},
                {"key": "Ctrl+S", "desc": "切换到和值分布", "tip": "快速访问和值分布分析页面"},
                {"key": "Ctrl+Q", "desc": "切换到数据查询", "tip": "快速访问数据查询页面"},
                {"key": "Ctrl+M", "desc": "切换到数据管理", "tip": "快速访问数据管理页面"}
            ],
            "预测功能": [
                {"key": "Ctrl+P", "desc": "开始预测分析", "tip": "在预测页面快速开始预测"},
                {"key": "Ctrl+T", "desc": "切换预测方法", "tip": "在不同预测方法间切换"},
                {"key": "Ctrl+E", "desc": "导出预测结果", "tip": "将预测结果导出为文件"}
            ],
            "数据操作": [
                {"key": "Ctrl+U", "desc": "更新数据", "tip": "手动触发数据更新"},
                {"key": "Ctrl+B", "desc": "备份数据", "tip": "创建数据备份"},
                {"key": "Ctrl+I", "desc": "导入数据", "tip": "导入外部数据文件"}
            ]
        }
    
    def show_help_dialog(self):
        """显示快捷键帮助对话框"""
        help_dialog_html = """
        <div id="shortcuts-help-modal" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        ">
            <div style="
                background: white;
                border-radius: 12px;
                padding: 30px;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                position: relative;
            ">
                <button onclick="document.getElementById('shortcuts-help-modal').remove()" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                ">×</button>
                
                <h2 style="text-align: center; color: #333; margin-bottom: 30px;">
                    🔥 键盘快捷键指南
                </h2>
                
                <div id="shortcuts-content">
                    <!-- 内容将由JavaScript动态生成 -->
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <p style="color: #666; margin: 0;">
                        💡 提示：按 <kbd>Ctrl+H</kbd> 可以随时显示/隐藏此帮助
                    </p>
                </div>
            </div>
        </div>
        
        <style>
        #shortcuts-help-modal kbd {
            background-color: #f7f7f7;
            border: 1px solid #ccc;
            border-radius: 3px;
            box-shadow: 0 1px 0 rgba(0,0,0,0.2), 0 0 0 2px #fff inset;
            color: #333;
            display: inline-block;
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 0 .1em;
            padding: .1em .6em;
            text-shadow: 0 1px 0 #fff;
        }
        
        .shortcut-category {
            margin-bottom: 25px;
        }
        
        .shortcut-category h3 {
            color: #444;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
            margin-bottom: 15px;
        }
        
        .shortcut-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .shortcut-item:last-child {
            border-bottom: none;
        }
        
        .shortcut-key {
            min-width: 120px;
            margin-right: 15px;
        }
        
        .shortcut-desc {
            flex: 1;
            font-weight: 500;
            color: #333;
        }
        
        .shortcut-tip {
            font-size: 12px;
            color: #666;
            margin-top: 3px;
        }
        </style>
        
        <script>
        // 生成快捷键内容
        const shortcutsData = """ + str(self.shortcuts_data).replace("'", '"') + """;
        
        function generateShortcutsContent() {
            const content = document.getElementById('shortcuts-content');
            let html = '';
            
            Object.entries(shortcutsData).forEach(([category, shortcuts]) => {
                html += `<div class="shortcut-category">`;
                html += `<h3>${category}</h3>`;
                
                shortcuts.forEach(shortcut => {
                    html += `
                        <div class="shortcut-item">
                            <div class="shortcut-key">
                                <kbd>${shortcut.key}</kbd>
                            </div>
                            <div>
                                <div class="shortcut-desc">${shortcut.desc}</div>
                                <div class="shortcut-tip">${shortcut.tip}</div>
                            </div>
                        </div>
                    `;
                });
                
                html += `</div>`;
            });
            
            content.innerHTML = html;
        }
        
        // 生成内容
        generateShortcutsContent();
        
        // 点击外部关闭
        document.getElementById('shortcuts-help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.remove();
            }
        });
        </script>
        """
        
        components.html(help_dialog_html, height=0)
    
    def show_help_page(self):
        """显示完整的帮助页面"""
        st.title("🔥 键盘快捷键指南")
        
        st.markdown("""
        欢迎使用福彩3D预测分析工具的键盘快捷键功能！通过快捷键，您可以大大提升操作效率。
        
        ## 📖 使用说明
        
        - **快捷键格式**：`Ctrl+字母` 表示同时按住 Ctrl 键和指定字母
        - **功能键**：`F1`、`F5` 等功能键可直接按下
        - **特殊键**：`Escape` 键用于关闭对话框
        - **实时帮助**：按 `Ctrl+H` 可随时显示快捷键帮助
        
        ---
        """)
        
        # 显示各类别的快捷键
        for category, shortcuts in self.shortcuts_data.items():
            st.subheader(f"🔸 {category}")
            
            # 创建表格显示
            cols = st.columns([2, 4, 4])
            with cols[0]:
                st.write("**快捷键**")
            with cols[1]:
                st.write("**功能**")
            with cols[2]:
                st.write("**说明**")
            
            st.markdown("---")
            
            for shortcut in shortcuts:
                cols = st.columns([2, 4, 4])
                with cols[0]:
                    st.code(shortcut['key'])
                with cols[1]:
                    st.write(shortcut['desc'])
                with cols[2]:
                    st.write(shortcut['tip'])
            
            st.markdown("")
        
        # 使用技巧
        st.subheader("💡 使用技巧")
        
        tips = [
            "**快速导航**：使用 `Ctrl+D/F/S/Q/M` 可以快速在不同页面间切换",
            "**数据刷新**：`Ctrl+R` 只刷新数据，`F5` 会重新加载整个页面",
            "**预测操作**：在预测页面使用 `Ctrl+P` 可以快速开始预测",
            "**帮助系统**：`F1` 显示详细帮助，`Ctrl+H` 显示快捷键列表",
            "**应急操作**：`Escape` 可以关闭大部分对话框和弹窗"
        ]
        
        for tip in tips:
            st.markdown(f"- {tip}")
        
        # 自定义快捷键
        st.subheader("⚙️ 自定义设置")
        
        st.info("""
        **即将推出的功能**：
        - 自定义快捷键绑定
        - 快捷键使用统计
        - 个性化快捷键方案
        - 快捷键冲突检测
        """)
        
        # 常见问题
        with st.expander("❓ 常见问题"):
            st.markdown("""
            **Q: 快捷键不起作用怎么办？**
            A: 请确保页面已完全加载，且焦点在页面内容区域。某些浏览器扩展可能会干扰快捷键。
            
            **Q: 如何记住这么多快捷键？**
            A: 建议先记住最常用的几个：`Ctrl+H`（帮助）、`Ctrl+R`（刷新）、`Ctrl+D`（概览）。
            
            **Q: 快捷键与浏览器快捷键冲突怎么办？**
            A: 我们尽量避免了常见的浏览器快捷键冲突。如有问题，可以使用鼠标操作作为替代。
            
            **Q: 移动设备支持快捷键吗？**
            A: 快捷键主要为桌面用户设计。移动设备用户请使用触摸操作。
            """)
    
    def create_floating_help_button(self):
        """创建浮动帮助按钮"""
        floating_button_html = """
        <div id="floating-help-btn" style="
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
        " onclick="showShortcutsHelp()">
            <span style="color: white; font-size: 20px;">🔥</span>
        </div>
        
        <style>
        #floating-help-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        }
        
        #floating-help-btn:active {
            transform: scale(0.95);
        }
        </style>
        
        <script>
        function showShortcutsHelp() {
            // 触发 Ctrl+H 快捷键
            document.dispatchEvent(new KeyboardEvent('keydown', {
                key: 'H',
                ctrlKey: true,
                bubbles: true
            }));
        }
        </script>
        """
        
        components.html(floating_button_html, height=0)
    
    def show_quick_reference(self):
        """显示快速参考卡片"""
        st.sidebar.markdown("### 🔥 快捷键参考")
        
        quick_shortcuts = [
            ("Ctrl+H", "帮助"),
            ("Ctrl+R", "刷新"),
            ("Ctrl+D", "概览"),
            ("Ctrl+P", "预测"),
            ("F1", "详细帮助")
        ]
        
        for key, desc in quick_shortcuts:
            st.sidebar.markdown(f"`{key}` {desc}")
        
        if st.sidebar.button("📖 查看完整指南"):
            st.session_state.show_shortcuts_help = True


# 便捷函数
def show_shortcuts_help_dialog():
    """显示快捷键帮助对话框"""
    help_manager = ShortcutsHelpManager()
    help_manager.show_help_dialog()

def show_shortcuts_help_page():
    """显示快捷键帮助页面"""
    help_manager = ShortcutsHelpManager()
    help_manager.show_help_page()

def create_floating_help_button():
    """创建浮动帮助按钮"""
    help_manager = ShortcutsHelpManager()
    help_manager.create_floating_help_button()

def show_quick_reference():
    """显示快速参考"""
    help_manager = ShortcutsHelpManager()
    help_manager.show_quick_reference()

# 全局帮助管理器实例
global_help_manager = ShortcutsHelpManager()
