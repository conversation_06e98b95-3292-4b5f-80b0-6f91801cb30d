@echo off
chcp 65001 >nul
title 福彩3D预测系统 - 一键启动

echo.
echo ========================================
echo    福彩3D预测分析工具 - 一键启动
echo ========================================
echo.
echo 🚀 正在启动系统...
echo.

echo 📋 启动步骤：
echo 1. 启动API服务（后台运行）
echo 2. 等待5秒确保API服务启动完成
echo 3. 启动Streamlit界面
echo.

:: 第一步：启动API服务（后台运行）
echo 🔧 步骤1: 启动API服务...
start cmd /k ".\venv\Scripts\activate && python start_production_api.py"

:: 第二步：等待API服务启动
echo ⏳ 步骤2: 等待API服务启动（5秒）...
timeout /t 5 /nobreak >nul

:: 第三步：启动Streamlit界面
echo 🎨 步骤3: 启动Streamlit界面...
echo.
echo ✅ 系统启动完成！
echo.
echo 📱 访问地址：
echo    - Streamlit界面: http://127.0.0.1:8501
echo    - API服务: http://127.0.0.1:8888
echo    - API文档: http://127.0.0.1:8888/docs
echo.
echo 💡 提示：
echo    - 关闭此窗口将停止Streamlit界面
echo    - API服务在独立窗口中运行
echo    - 如需停止所有服务，请关闭所有相关窗口
echo.

:: 激活虚拟环境并启动Streamlit
call .\venv\Scripts\activate
python -m streamlit run src/ui/main.py

echo.
echo 👋 Streamlit界面已停止
echo 💡 API服务可能仍在后台运行，请手动关闭API服务窗口
pause
