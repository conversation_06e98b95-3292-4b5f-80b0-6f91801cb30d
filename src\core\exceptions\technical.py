"""
技术异常类

定义与技术基础设施相关的异常类型。
"""

from typing import Any, Dict, Optional

from .base import ErrorCode, ErrorSeverity, LotterySystemException


class DatabaseException(LotterySystemException):
    """数据库相关异常"""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.DATABASE_CONNECTION_ERROR,
        query: Optional[str] = None,
        table_name: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if query:
            details['query'] = query
        if table_name:
            details['table_name'] = table_name
        
        kwargs['details'] = details
        super().__init__(
            message,
            code,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class DatabaseConnectionException(DatabaseException):
    """数据库连接异常"""
    
    def __init__(self, db_url: str, **kwargs):
        message = f"数据库连接失败: {db_url}"
        super().__init__(
            message,
            code=ErrorCode.DATABASE_CONNECTION_ERROR,
            details={'db_url': db_url},
            **kwargs
        )
        # 在初始化后设置severity为CRITICAL
        self.severity = ErrorSeverity.CRITICAL


class DatabaseQueryException(DatabaseException):
    """数据库查询异常"""
    
    def __init__(self, query: str, error_detail: str, **kwargs):
        message = f"数据库查询失败: {error_detail}"
        super().__init__(
            message,
            code=ErrorCode.DATABASE_QUERY_ERROR,
            query=query,
            details={'error_detail': error_detail},
            **kwargs
        )


class DatabaseTransactionException(DatabaseException):
    """数据库事务异常"""
    
    def __init__(self, operation: str, **kwargs):
        message = f"数据库事务失败: {operation}"
        super().__init__(
            message,
            code=ErrorCode.DATABASE_TRANSACTION_ERROR,
            details={'operation': operation},
            **kwargs
        )


class NetworkException(LotterySystemException):
    """网络相关异常"""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.NETWORK_CONNECTION_ERROR,
        url: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if url:
            details['url'] = url
        if status_code:
            details['status_code'] = status_code
        
        kwargs['details'] = details
        super().__init__(
            message,
            code,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class NetworkConnectionException(NetworkException):
    """网络连接异常"""
    
    def __init__(self, url: str, **kwargs):
        message = f"网络连接失败: {url}"
        super().__init__(
            message,
            code=ErrorCode.NETWORK_CONNECTION_ERROR,
            url=url,
            **kwargs
        )


class NetworkTimeoutException(NetworkException):
    """网络超时异常"""
    
    def __init__(self, url: str, timeout: int, **kwargs):
        message = f"网络请求超时: {url} (超时时间: {timeout}秒)"
        super().__init__(
            message,
            code=ErrorCode.NETWORK_TIMEOUT,
            url=url,
            details={'timeout': timeout},
            **kwargs
        )


class APIRateLimitException(NetworkException):
    """API限流异常"""
    
    def __init__(self, url: str, retry_after: Optional[int] = None, **kwargs):
        message = f"API请求频率限制: {url}"
        details = kwargs.get('details', {})
        if retry_after:
            details['retry_after'] = retry_after
            message += f" (请在{retry_after}秒后重试)"
        
        super().__init__(
            message,
            code=ErrorCode.API_RATE_LIMIT,
            url=url,
            details=details,
            **kwargs
        )


class FileSystemException(LotterySystemException):
    """文件系统相关异常"""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.FILE_NOT_FOUND,
        file_path: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if file_path:
            details['file_path'] = file_path
        
        kwargs['details'] = details
        super().__init__(
            message,
            code,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class FileNotFoundException(FileSystemException):
    """文件未找到异常"""
    
    def __init__(self, file_path: str, **kwargs):
        message = f"文件不存在: {file_path}"
        super().__init__(
            message,
            code=ErrorCode.FILE_NOT_FOUND,
            file_path=file_path,
            **kwargs
        )


class FilePermissionException(FileSystemException):
    """文件权限异常"""
    
    def __init__(self, file_path: str, operation: str, **kwargs):
        message = f"文件权限不足: {file_path} (操作: {operation})"
        super().__init__(
            message,
            code=ErrorCode.FILE_PERMISSION_ERROR,
            file_path=file_path,
            details={'operation': operation},
            **kwargs
        )


class DiskSpaceException(FileSystemException):
    """磁盘空间异常"""
    
    def __init__(self, path: str, required_space: int, available_space: int, **kwargs):
        message = f"磁盘空间不足: {path} (需要: {required_space}MB, 可用: {available_space}MB)"
        super().__init__(
            message,
            code=ErrorCode.DISK_SPACE_ERROR,
            file_path=path,
            details={
                'required_space': required_space,
                'available_space': available_space
            },
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class CacheException(LotterySystemException):
    """缓存相关异常"""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.CACHE_OPERATION_ERROR,
        cache_key: Optional[str] = None,
        cache_backend: Optional[str] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if cache_key:
            details['cache_key'] = cache_key
        if cache_backend:
            details['cache_backend'] = cache_backend
        
        kwargs['details'] = details
        super().__init__(
            message,
            code,
            severity=ErrorSeverity.LOW,
            **kwargs
        )


class CacheConnectionException(CacheException):
    """缓存连接异常"""
    
    def __init__(self, backend: str, connection_info: str, **kwargs):
        message = f"缓存连接失败: {backend} ({connection_info})"
        super().__init__(
            message,
            code=ErrorCode.CACHE_CONNECTION_ERROR,
            cache_backend=backend,
            details={'connection_info': connection_info},
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class CacheSerializationException(CacheException):
    """缓存序列化异常"""

    def __init__(self, cache_key: str, data_type: str, **kwargs):
        message = f"缓存序列化失败: {cache_key} (数据类型: {data_type})"
        super().__init__(
            message,
            code=ErrorCode.CACHE_SERIALIZATION_ERROR,
            cache_key=cache_key,
            details={'data_type': data_type},
            **kwargs
        )


class ValidationException(LotterySystemException):
    """数据验证异常"""

    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.DATA_VALIDATION_ERROR,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        **kwargs
    ):
        details = kwargs.get('details', {})
        if field_name:
            details['field_name'] = field_name
        if field_value is not None:
            details['field_value'] = str(field_value)

        kwargs['details'] = details
        super().__init__(
            message,
            code,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
