"""
福彩3D预测应用主类

将原有的巨型main函数重构为模块化的应用类。
"""

import logging
from typing import Any, Dict, Optional

import streamlit as st

from ui.components.enhanced_status_indicators import EnhancedStatusIndicators
from ui.components.layout_optimizer import LayoutOptimizer
from ui.components.navigation_styles import NavigationStyleManager
# 导入组件
from ui.components.unified_navigation import (NavigationStyle,
                                              UnifiedNavigationComponent)
from ui.components.user_preferences import UserPreferenceManager

logger = logging.getLogger(__name__)


class LotteryPredictionApp:
    """福彩3D预测应用主类"""
    
    def __init__(self):
        """初始化应用"""
        self.style_manager = None
        self.unified_nav = None
        self.layout_optimizer = None
        self.enhanced_status = None
        self.pref_manager = None
        self.fallback_manager = None
        self.websocket_status = False

        # 初始化状态
        self._initialize_session_state()
        
    def _initialize_session_state(self):
        """初始化session状态"""
        if 'app_initialized' not in st.session_state:
            st.session_state.app_initialized = False
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "数据概览"
        if 'navigation_history' not in st.session_state:
            st.session_state.navigation_history = []
    
    def setup_components(self) -> bool:
        """
        设置应用组件
        
        Returns:
            是否设置成功
        """
        try:
            # 初始化样式管理器
            self.style_manager = NavigationStyleManager()
            self.style_manager.inject_theme_styles()
            
            # 初始化统一导航组件
            self.unified_nav = UnifiedNavigationComponent(NavigationStyle.ENHANCED)
            
            # 初始化布局优化器
            self.layout_optimizer = LayoutOptimizer()
            self.layout_optimizer.inject_layout_styles()
            
            # 初始化增强状态指示器
            self.enhanced_status = EnhancedStatusIndicators()
            
            # 初始化用户偏好管理器
            if 'pref_manager' not in st.session_state:
                st.session_state.pref_manager = UserPreferenceManager()
            self.pref_manager = st.session_state.pref_manager
            
            logger.info("应用组件设置成功")
            return True
            
        except Exception as e:
            logger.error(f"应用组件设置失败: {e}")
            st.warning(f"⚠️ 组件加载失败: {str(e)}")
            return False
    
    def setup_security_enhancements(self):
        """设置安全增强"""
        try:
            from ui.main import inject_security_enhancements
            inject_security_enhancements()
            logger.info("安全增强设置完成")
        except Exception as e:
            logger.warning(f"安全增强设置失败: {e}")
    
    def setup_keyboard_shortcuts(self):
        """设置键盘快捷键"""
        try:
            from ui.components.keyboard_shortcuts import \
                inject_keyboard_shortcuts
            from ui.components.shortcuts_help import (
                create_floating_help_button, show_quick_reference)

            # 注入键盘快捷键
            inject_keyboard_shortcuts()
            
            # 创建浮动帮助按钮
            create_floating_help_button()
            
            # 在侧边栏显示快速参考
            show_quick_reference()
            
            logger.info("键盘快捷键设置完成")
            
        except Exception as e:
            logger.warning(f"键盘快捷键设置失败: {e}")
            st.warning(f"⚠️ 快捷键功能加载失败: {str(e)}")
    
    def setup_bug_detection(self):
        """设置Bug检测系统"""
        try:
            from bug_detection.monitoring.js_monitor import inject_js_monitor
            from ui.components.fallback_manager import (get_fallback_manager,
                                                        show_connection_status)
            from ui.components.websocket_client import inject_websocket_client
            from ui.main import check_websocket_connection

            # 注入JavaScript错误监控
            inject_js_monitor("lottery_prediction_system", log_level='ERROR')
            
            # 注入WebSocket客户端
            inject_websocket_client("ws://127.0.0.1:8888/ws/prediction-results", log_level='WARN')
            
            # 初始化降级管理器
            self.fallback_manager = get_fallback_manager()
            
            # 检查WebSocket连接状态
            self.websocket_status = check_websocket_connection()
            
            logger.info("Bug检测系统设置完成")
            
        except Exception as e:
            logger.warning(f"Bug检测系统设置失败: {e}")
    
    def render_header(self):
        """渲染应用头部"""
        try:
            # 设置页面配置
            st.set_page_config(
                page_title="福彩3D预测系统",
                page_icon="🎯",
                layout="wide",
                initial_sidebar_state="expanded"
            )
            
            # 渲染主标题
            st.title("🎯 福彩3D智能预测系统")
            st.markdown("---")
            
        except Exception as e:
            logger.error(f"渲染头部失败: {e}")
    
    def render_navigation(self):
        """渲染导航"""
        try:
            if self.unified_nav:
                # 使用统一导航组件
                page = self.unified_nav.render()
                if page:
                    st.session_state.current_page = page
            else:
                # 简单导航回退
                pages = ["数据概览", "预测分析", "数据管理", "系统设置"]
                page = st.sidebar.selectbox("选择页面", pages)
                st.session_state.current_page = page

        except Exception as e:
            logger.error(f"渲染导航失败: {e}")
            # 最简单的回退导航
            st.session_state.current_page = "数据概览"
    
    def render_sidebar(self):
        """渲染侧边栏"""
        try:
            with st.sidebar:
                st.markdown("### 🔧 系统状态")
                
                # 显示连接状态
                if self.websocket_status:
                    st.success("🟢 WebSocket连接正常")
                else:
                    st.warning("🟡 WebSocket连接异常")
                
                # 显示增强状态指示器
                if self.enhanced_status:
                    try:
                        self.enhanced_status.render()
                    except AttributeError:
                        st.info("状态指示器加载中...")
                
                st.markdown("---")
                
        except Exception as e:
            logger.error(f"渲染侧边栏失败: {e}")
    
    def render_content(self):
        """渲染主要内容"""
        try:
            current_page = st.session_state.get('current_page', '数据概览')
            
            # 根据当前页面渲染内容
            if current_page == "数据概览":
                self._render_data_overview()
            elif current_page == "预测分析":
                self._render_prediction_analysis()
            elif current_page == "数据管理":
                self._render_data_management()
            elif current_page == "系统设置":
                self._render_system_settings()
            else:
                st.error(f"未知页面: {current_page}")
                
        except Exception as e:
            logger.error(f"渲染内容失败: {e}")
            st.error(f"页面渲染失败: {e}")
    
    def _render_data_overview(self):
        """渲染数据概览页面"""
        st.header("📊 数据概览")
        st.info("数据概览页面 - 待实现具体内容")
    
    def _render_prediction_analysis(self):
        """渲染预测分析页面"""
        st.header("🎯 预测分析")
        st.info("预测分析页面 - 待实现具体内容")
    
    def _render_data_management(self):
        """渲染数据管理页面"""
        st.header("🔄 数据管理")
        st.info("数据管理页面 - 待实现具体内容")
    
    def _render_system_settings(self):
        """渲染系统设置页面"""
        st.header("⚙️ 系统设置")
        st.info("系统设置页面 - 待实现具体内容")
    
    def run(self):
        """运行应用"""
        try:
            # 1. 设置安全增强
            self.setup_security_enhancements()
            
            # 2. 设置组件
            components_ready = self.setup_components()
            
            # 3. 设置键盘快捷键
            self.setup_keyboard_shortcuts()
            
            # 4. 设置Bug检测
            self.setup_bug_detection()
            
            # 5. 渲染界面
            self.render_header()
            self.render_navigation()
            self.render_sidebar()
            self.render_content()
            
            # 标记应用已初始化
            st.session_state.app_initialized = True
            
            logger.info("应用运行成功")
            
        except Exception as e:
            logger.error(f"应用运行失败: {e}")
            st.error(f"应用启动失败: {e}")
            
            # 显示错误恢复选项
            if st.button("🔄 重新启动应用"):
                st.rerun()
