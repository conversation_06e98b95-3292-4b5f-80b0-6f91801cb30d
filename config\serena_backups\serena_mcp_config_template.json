{"serena_mcp_config": {"executable": "d:/github/3dyuce/venv/Scripts/serena-mcp-server.exe", "args": ["--project", "d:/github/3dyuce", "--enable-web-dashboard", "true", "--host", "127.0.0.1", "--port", "24282"], "working_directory": "d:/github/3dyuce", "dashboard_url": "http://127.0.0.1:24282/dashboard/index.html", "auto_start": true, "health_check_interval": 30}, "dependencies": {"pyright": {"required": true, "install_command": "C:\\Program Files\\Python311\\python.exe -m pip install pyright", "check_command": "C:\\Program Files\\Python311\\python.exe -c \"import pyright.langserver\""}}, "troubleshooting": {"common_issues": [{"issue": "Language server failed to start", "solution": "Install pyright in system Python"}, {"issue": "Dashboard not accessible", "solution": "Check if serena process is running and port 24282 is available"}, {"issue": "MCP connection failed", "solution": "Restart Cursor and reconfigure MCP server"}]}}