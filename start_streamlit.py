#!/usr/bin/env python3
"""
启动Streamlit应用 - 重定向到标准命令

⚠️ 注意: 推荐直接使用标准命令启动:

标准启动命令:
python -m streamlit run src/ui/main.py
"""

import os
import sys
from pathlib import Path


def main():
    """主函数 - 显示启动说明"""
    print("=" * 80)
    print("🚀 福彩3D预测系统 - Streamlit界面启动说明")
    print("=" * 80)
    print()
    print("📋 推荐启动方式 (标准方式):")
    print("   python -m streamlit run src/ui/main.py")
    print()
    print("📋 应用信息:")
    print("   - 界面地址: http://127.0.0.1:8501")
    print("   - API地址: http://127.0.0.1:8888")
    print("   - 确保API服务已启动: python start_production_api.py")
    print()
    print("=" * 80)
    print()

    # 询问是否自动启动
    try:
        choice = input("是否现在启动Streamlit? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("\n🚀 正在启动Streamlit...")
            print("=" * 50)

            # 设置环境变量
            os.environ['PYTHONPATH'] = 'src'

            # 使用标准命令启动
            cmd = f'"{sys.executable}" -m streamlit run src/ui/main.py'
            os.system(cmd)
        else:
            print("\n👋 请手动使用上述命令启动Streamlit")

    except KeyboardInterrupt:
        print("\n👋 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")


if __name__ == "__main__":
    main()
