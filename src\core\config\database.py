"""
数据库配置

管理数据库连接相关的配置参数。
"""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseConfig(BaseSettings):
    """数据库配置类"""
    
    # 数据库类型
    type: str = Field(default="sqlite", description="数据库类型")
    
    # SQLite配置
    name: str = Field(default="lottery.db", description="数据库名称")
    
    # PostgreSQL/MySQL配置
    host: Optional[str] = Field(default=None, description="数据库主机")
    port: Optional[int] = Field(default=None, description="数据库端口")
    user: Optional[str] = Field(default=None, description="数据库用户名")
    password: Optional[str] = Field(default=None, description="数据库密码")
    
    # 连接配置
    url: Optional[str] = Field(default=None, description="完整数据库URL")
    pool_size: int = Field(default=5, description="连接池大小")
    max_overflow: int = Field(default=10, description="连接池最大溢出")
    pool_timeout: int = Field(default=30, description="连接池超时时间")
    pool_recycle: int = Field(default=3600, description="连接回收时间")
    
    # 查询配置
    echo: bool = Field(default=False, description="是否打印SQL语句")
    echo_pool: bool = Field(default=False, description="是否打印连接池信息")
    
    # 事务配置
    autocommit: bool = Field(default=False, description="是否自动提交")
    autoflush: bool = Field(default=True, description="是否自动刷新")
    
    class Config:
        env_prefix = "DB_"
        env_file = ".env"
        case_sensitive = False
    
    def get_connection_params(self) -> dict:
        """获取数据库连接参数"""
        params = {
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_timeout": self.pool_timeout,
            "pool_recycle": self.pool_recycle,
            "echo": self.echo,
            "echo_pool": self.echo_pool,
        }
        
        return {k: v for k, v in params.items() if v is not None}
    
    def get_session_params(self) -> dict:
        """获取会话参数"""
        return {
            "autocommit": self.autocommit,
            "autoflush": self.autoflush,
        }
    
    @property
    def is_sqlite(self) -> bool:
        """是否为SQLite数据库"""
        return self.type.lower() == "sqlite"
    
    @property
    def is_postgresql(self) -> bool:
        """是否为PostgreSQL数据库"""
        return self.type.lower() in ["postgresql", "postgres"]
    
    @property
    def is_mysql(self) -> bool:
        """是否为MySQL数据库"""
        return self.type.lower() == "mysql"
