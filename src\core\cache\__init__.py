"""
缓存系统模块

提供多层缓存架构，支持内存、Redis和文件缓存。
"""

from .interface import CacheInterface
from .manager import CacheManager
from .memory import MemoryCache
from .file import FileCache
from .decorators import cached, cache_result

# 尝试导入Redis缓存，如果Redis不可用则跳过
try:
    from .redis import RedisCache
    HAS_REDIS = True
except ImportError:
    RedisCache = None
    HAS_REDIS = False

__all__ = [
    'CacheInterface',
    'CacheManager', 
    'MemoryCache',
    'FileCache',
    'cached',
    'cache_result',
]

if HAS_REDIS:
    __all__.append('RedisCache')
