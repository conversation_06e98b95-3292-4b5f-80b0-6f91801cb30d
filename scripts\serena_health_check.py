#!/usr/bin/env python3
"""
serena MCP服务器健康检查脚本
用于验证serena MCP服务器的状态和功能
"""

import requests
import subprocess
import time
import sys
import os
from pathlib import Path

class SerenaHealthChecker:
    def __init__(self):
        self.dashboard_url = "http://127.0.0.1:24282/dashboard/index.html"
        self.api_url = "http://127.0.0.1:24282"
        self.project_root = Path(__file__).parent.parent
        self.venv_path = self.project_root / "venv"
        self.serena_exe = self.venv_path / "Scripts" / "serena-mcp-server.exe"
        
    def check_serena_executable(self):
        """检查serena可执行文件是否存在"""
        print("🔍 检查serena可执行文件...")
        if self.serena_exe.exists():
            print(f"  ✅ serena-mcp-server.exe 存在: {self.serena_exe}")
            return True
        else:
            print(f"  ❌ serena-mcp-server.exe 不存在: {self.serena_exe}")
            return False
    
    def check_pyright_dependency(self):
        """检查pyright依赖是否可用"""
        print("🔍 检查pyright依赖...")
        try:
            # 检查系统Python中的pyright
            result = subprocess.run([
                "C:\\Program Files\\Python311\\python.exe", 
                "-c", 
                "import pyright.langserver; print('pyright available')"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("  ✅ pyright.langserver 在系统Python中可用")
                return True
            else:
                print(f"  ❌ pyright.langserver 不可用: {result.stderr}")
                return False
        except Exception as e:
            print(f"  ❌ 检查pyright时出错: {e}")
            return False
    
    def check_dashboard_accessibility(self):
        """检查serena仪表板是否可访问"""
        print("🔍 检查serena仪表板可访问性...")
        try:
            response = requests.get(self.dashboard_url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ serena仪表板可访问: {self.dashboard_url}")
                return True
            else:
                print(f"  ❌ serena仪表板返回状态码: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 无法连接到serena仪表板: {self.dashboard_url}")
            return False
        except Exception as e:
            print(f"  ❌ 检查仪表板时出错: {e}")
            return False
    
    def check_serena_process(self):
        """检查serena进程是否运行"""
        print("🔍 检查serena进程...")
        try:
            # 使用tasklist检查进程
            result = subprocess.run([
                "tasklist", "/FI", "IMAGENAME eq serena-mcp-server.exe"
            ], capture_output=True, text=True)
            
            if "serena-mcp-server.exe" in result.stdout:
                print("  ✅ serena-mcp-server.exe 进程正在运行")
                return True
            else:
                print("  ❌ serena-mcp-server.exe 进程未运行")
                return False
        except Exception as e:
            print(f"  ❌ 检查进程时出错: {e}")
            return False
    
    def test_serena_tools(self):
        """测试serena工具是否可用（需要MCP连接）"""
        print("🔍 测试serena工具可用性...")
        print("  ℹ️  此项需要通过Augment MCP连接进行测试")
        print("  ℹ️  请在Augment中运行serena工具进行验证")
        return True
    
    def run_comprehensive_check(self):
        """运行全面的健康检查"""
        print("🚀 开始serena MCP服务器健康检查...")
        print("=" * 50)
        
        checks = [
            ("serena可执行文件", self.check_serena_executable),
            ("pyright依赖", self.check_pyright_dependency),
            ("serena进程", self.check_serena_process),
            ("仪表板可访问性", self.check_dashboard_accessibility),
            ("serena工具", self.test_serena_tools),
        ]
        
        results = {}
        for check_name, check_func in checks:
            try:
                results[check_name] = check_func()
            except Exception as e:
                print(f"  ❌ {check_name}检查失败: {e}")
                results[check_name] = False
            print()
        
        # 生成报告
        print("📊 健康检查报告")
        print("=" * 50)
        
        passed = 0
        total = len(results)
        
        for check_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {check_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总体状态: {passed}/{total} 项检查通过")
        
        if passed == total:
            print("🎉 serena MCP服务器状态良好！")
            return True
        else:
            print("⚠️  serena MCP服务器存在问题，请检查失败项")
            return False
    
    def get_quick_status(self):
        """快速状态检查"""
        dashboard_ok = self.check_dashboard_accessibility()
        process_ok = self.check_serena_process()
        
        if dashboard_ok and process_ok:
            return "🟢 serena MCP服务器运行正常"
        elif process_ok:
            return "🟡 serena进程运行中，但仪表板不可访问"
        else:
            return "🔴 serena MCP服务器未运行"

def main():
    """主函数"""
    checker = SerenaHealthChecker()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        print(checker.get_quick_status())
    else:
        success = checker.run_comprehensive_check()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
