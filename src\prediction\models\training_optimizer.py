"""
训练优化器

提供高效的训练策略、学习率调度、混合精度训练等优化技术。
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
from typing import Dict, Any, Optional, List, Callable
import numpy as np
import logging
import time
from collections import defaultdict

logger = logging.getLogger(__name__)


class TrainingOptimizer:
    """训练优化器"""
    
    def __init__(
        self,
        model: nn.Module,
        device: str = 'cpu',
        use_mixed_precision: bool = True,
        gradient_clipping: float = 1.0
    ):
        self.model = model.to(device)
        self.device = device
        self.use_mixed_precision = use_mixed_precision and device != 'cpu'
        self.gradient_clipping = gradient_clipping
        
        # 混合精度训练
        self.scaler = GradScaler() if self.use_mixed_precision else None
        
        # 训练统计
        self.training_stats = defaultdict(list)
        
        logger.info(f"训练优化器初始化完成，设备: {device}, 混合精度: {use_mixed_precision}")
    
    def create_optimizer(
        self,
        optimizer_type: str = 'adamw',
        learning_rate: float = 0.001,
        weight_decay: float = 0.01,
        **kwargs
    ) -> optim.Optimizer:
        """
        创建优化器
        
        Args:
            optimizer_type: 优化器类型
            learning_rate: 学习率
            weight_decay: 权重衰减
            
        Returns:
            优化器实例
        """
        if optimizer_type.lower() == 'adamw':
            optimizer = optim.AdamW(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay,
                betas=kwargs.get('betas', (0.9, 0.999)),
                eps=kwargs.get('eps', 1e-8)
            )
        elif optimizer_type.lower() == 'adam':
            optimizer = optim.Adam(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay,
                betas=kwargs.get('betas', (0.9, 0.999))
            )
        elif optimizer_type.lower() == 'sgd':
            optimizer = optim.SGD(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay,
                momentum=kwargs.get('momentum', 0.9),
                nesterov=kwargs.get('nesterov', True)
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
        
        logger.info(f"创建优化器: {optimizer_type}, 学习率: {learning_rate}")
        return optimizer
    
    def create_scheduler(
        self,
        optimizer: optim.Optimizer,
        scheduler_type: str = 'cosine',
        **kwargs
    ) -> optim.lr_scheduler._LRScheduler:
        """
        创建学习率调度器
        
        Args:
            optimizer: 优化器
            scheduler_type: 调度器类型
            
        Returns:
            学习率调度器
        """
        if scheduler_type.lower() == 'cosine':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=kwargs.get('T_max', 100),
                eta_min=kwargs.get('eta_min', 1e-6)
            )
        elif scheduler_type.lower() == 'step':
            scheduler = optim.lr_scheduler.StepLR(
                optimizer,
                step_size=kwargs.get('step_size', 30),
                gamma=kwargs.get('gamma', 0.1)
            )
        elif scheduler_type.lower() == 'plateau':
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode=kwargs.get('mode', 'min'),
                factor=kwargs.get('factor', 0.5),
                patience=kwargs.get('patience', 10),
                verbose=True
            )
        elif scheduler_type.lower() == 'warmup':
            scheduler = self._create_warmup_scheduler(optimizer, **kwargs)
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")
        
        logger.info(f"创建学习率调度器: {scheduler_type}")
        return scheduler
    
    def train_epoch(
        self,
        train_loader: torch.utils.data.DataLoader,
        optimizer: optim.Optimizer,
        criterion: nn.Module,
        scheduler: Optional[optim.lr_scheduler._LRScheduler] = None,
        accumulation_steps: int = 1
    ) -> Dict[str, float]:
        """
        训练一个epoch
        
        Args:
            train_loader: 训练数据加载器
            optimizer: 优化器
            criterion: 损失函数
            scheduler: 学习率调度器
            accumulation_steps: 梯度累积步数
            
        Returns:
            训练统计信息
        """
        self.model.train()
        
        total_loss = 0.0
        total_samples = 0
        start_time = time.time()
        
        optimizer.zero_grad()
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            # 前向传播
            if self.use_mixed_precision:
                with autocast():
                    output = self.model(data)
                    loss = criterion(output, target) / accumulation_steps
                
                # 反向传播
                self.scaler.scale(loss).backward()
                
                # 梯度累积
                if (batch_idx + 1) % accumulation_steps == 0:
                    # 梯度裁剪
                    if self.gradient_clipping > 0:
                        self.scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(), 
                            self.gradient_clipping
                        )
                    
                    self.scaler.step(optimizer)
                    self.scaler.update()
                    optimizer.zero_grad()
            else:
                output = self.model(data)
                loss = criterion(output, target) / accumulation_steps
                
                loss.backward()
                
                if (batch_idx + 1) % accumulation_steps == 0:
                    if self.gradient_clipping > 0:
                        torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(), 
                            self.gradient_clipping
                        )
                    
                    optimizer.step()
                    optimizer.zero_grad()
            
            total_loss += loss.item() * accumulation_steps
            total_samples += data.size(0)
            
            # 更新学习率（如果使用OneCycleLR等需要每步更新的调度器）
            if scheduler is not None and hasattr(scheduler, 'step_update'):
                scheduler.step_update(batch_idx)
        
        # Epoch结束后更新学习率
        if scheduler is not None and not hasattr(scheduler, 'step_update'):
            if isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                scheduler.step(total_loss / len(train_loader))
            else:
                scheduler.step()
        
        epoch_time = time.time() - start_time
        avg_loss = total_loss / len(train_loader)
        
        stats = {
            'loss': avg_loss,
            'samples_per_sec': total_samples / epoch_time,
            'epoch_time': epoch_time,
            'learning_rate': optimizer.param_groups[0]['lr']
        }
        
        # 记录统计信息
        for key, value in stats.items():
            self.training_stats[key].append(value)
        
        return stats
    
    def validate(
        self,
        val_loader: torch.utils.data.DataLoader,
        criterion: nn.Module
    ) -> Dict[str, float]:
        """
        验证模型
        
        Args:
            val_loader: 验证数据加载器
            criterion: 损失函数
            
        Returns:
            验证统计信息
        """
        self.model.eval()
        
        total_loss = 0.0
        correct = 0
        total_samples = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                if self.use_mixed_precision:
                    with autocast():
                        output = self.model(data)
                        loss = criterion(output, target)
                else:
                    output = self.model(data)
                    loss = criterion(output, target)
                
                total_loss += loss.item()
                
                # 计算准确率（如果是分类任务）
                if output.dim() > 1 and output.size(1) > 1:
                    pred = output.argmax(dim=1)
                    correct += pred.eq(target).sum().item()
                
                total_samples += data.size(0)
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total_samples if total_samples > 0 else 0.0
        
        stats = {
            'val_loss': avg_loss,
            'val_accuracy': accuracy
        }
        
        return stats
    
    def _create_warmup_scheduler(
        self,
        optimizer: optim.Optimizer,
        warmup_steps: int = 1000,
        max_lr: float = 0.001,
        min_lr: float = 1e-6
    ) -> optim.lr_scheduler.LambdaLR:
        """创建预热学习率调度器"""
        def lr_lambda(step):
            if step < warmup_steps:
                return (max_lr - min_lr) * step / warmup_steps + min_lr
            else:
                return max_lr * 0.95 ** ((step - warmup_steps) // 100)
        
        return optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    def get_training_stats(self) -> Dict[str, List[float]]:
        """获取训练统计信息"""
        return dict(self.training_stats)
    
    def save_checkpoint(
        self,
        path: str,
        optimizer: optim.Optimizer,
        scheduler: Optional[optim.lr_scheduler._LRScheduler] = None,
        epoch: int = 0,
        best_metric: float = 0.0
    ):
        """保存训练检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'best_metric': best_metric,
            'training_stats': dict(self.training_stats)
        }
        
        if scheduler is not None:
            checkpoint['scheduler_state_dict'] = scheduler.state_dict()
        
        if self.scaler is not None:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()
        
        torch.save(checkpoint, path)
        logger.info(f"检查点已保存到: {path}")
    
    def load_checkpoint(
        self,
        path: str,
        optimizer: Optional[optim.Optimizer] = None,
        scheduler: Optional[optim.lr_scheduler._LRScheduler] = None
    ) -> Dict[str, Any]:
        """加载训练检查点"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if scheduler is not None and 'scheduler_state_dict' in checkpoint:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        if self.scaler is not None and 'scaler_state_dict' in checkpoint:
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
        
        if 'training_stats' in checkpoint:
            self.training_stats = defaultdict(list, checkpoint['training_stats'])
        
        logger.info(f"检查点已从 {path} 加载")
        return checkpoint


if __name__ == "__main__":
    # 测试训练优化器
    from optimized_cnn_lstm import create_optimized_model
    
    config = {'input_size': 3, 'hidden_size': 64, 'num_classes': 1000}
    model = create_optimized_model(config)
    
    trainer = TrainingOptimizer(model, device='cpu')
    optimizer = trainer.create_optimizer('adamw', learning_rate=0.001)
    scheduler = trainer.create_scheduler(optimizer, 'cosine', T_max=100)
    
    print(f"优化器: {type(optimizer).__name__}")
    print(f"调度器: {type(scheduler).__name__}")
    print(f"混合精度: {trainer.use_mixed_precision}")
