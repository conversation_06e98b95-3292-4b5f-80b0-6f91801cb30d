# RIPER-5规则完善和工具集成更新

## 更新概述
已成功将 `mcprules.mdc` 中的完善内容更新到 `riper-5.mdc` 文件中，使RIPER-5开发协议的工具使用规则更加完善全面，确保与实际可用的MCP工具和Augment内置工具保持一致。

## 主要更新内容

### 1. 工具引用标准化
将所有工具引用更新为实际可用的名称：
- `resolve-library-id`和`get-library-docs` → `Context 7`
- `deepwiki_fetch` → `mcp-deepwiki`
- `sequentialthinking` → `Sequential thinking`
- 新增 `serena` 工具的完整集成
- 新增 Augment内置工具的使用指导

### 2. 各RIPER-5模式的工具配置更新

#### RESEARCH模式工具更新
- **新增**：`serena`工具进行项目激活、入门分析和符号概览
- **更新**：`Context 7`查询技术文档，`mcp-deepwiki`获取背景知识
- **工具组合**：`search_nodes` + `codebase-retrieval` + `serena` + `Context 7` + `mcp-deepwiki`

#### INNOVATE模式工具更新
- **新增**：`serena`工具查找相关代码符号和引用关系
- **更新**：`Sequential thinking`深度方案思考，`Context 7`获取技术方案
- **工具组合**：`Sequential thinking` + `serena` + `Context 7` + `mcp-deepwiki` + `search_nodes`

#### PLAN模式工具更新
- **新增**：`serena`工具精确定位需要修改的代码符号和文件
- **更新**：`Sequential thinking`制定执行计划，`task management`拆解任务
- **工具组合**：`serena` + `Sequential thinking` + `task management` + `search_nodes`

#### EXECUTE模式工具更新
- **新增**：`serena`工具进行精确的代码编辑和符号替换
- **更新**：`launch-process`系统操作，`task management`任务跟踪
- **工具组合**：`serena` + `str-replace-editor` + `launch-process` + `Sequential thinking` + `task management`

#### REVIEW模式工具更新
- **新增**：`serena`工具验证代码符号的正确性和完整性
- **更新**：`Playwright`自动化测试，`Sequential thinking`质量分析
- **工具组合**：`serena` + `Playwright` + `Sequential thinking` + `add_observations`

### 3. 工具优先级体系重构

#### MCP工具优先级（新）
1. `Sequential thinking` - 复杂问题分析和深度思考
2. `serena` - 项目管理和精确代码编辑
3. `Context 7` - 查询最新库文档和示例
4. `mcp-deepwiki` - 获取背景知识和领域概念
5. `knowledge-graph` - 项目知识图谱管理
6. `Playwright` - 浏览器自动化操作

#### Augment内置工具优先级（新增）
1. `codebase-retrieval` - 分析现有代码结构
2. `str-replace-editor` - 代码编辑和修改
3. `launch-process` - 系统文件操作和命令执行
4. `save-file` - 创建新文件
5. `view` - 查看文件和目录
6. `task management` - 任务拆分与状态追踪

### 4. 新增详细工具使用指南

#### MCP工具详细说明
每个MCP工具都包含：
- **用途**：工具的主要功能和目标
- **核心功能**：具体的功能模块和API
- **适用场景**：最佳使用场景
- **使用时机**：在各RIPER-5模式中的具体应用
- **优势**：工具的独特优势和特点

#### Serena MCP完整集成
- **项目管理**：`activate_project`、`onboarding`、`get_active_project`
- **代码分析**：`find_symbol`、`get_symbols_overview`、`find_referencing_symbols`
- **精确编辑**：`replace_symbol_body`、`insert_after_symbol`、`insert_before_symbol`
- **文件操作**：`create_text_file`、`read_file`、`list_dir`
- **项目记忆**：`write_memory`、`read_memory`、`list_memories`
- **执行工具**：`execute_shell_command`、`search_for_pattern`

#### Playwright详细功能
- **浏览器控制**：`browser_navigate`、`browser_close`、`browser_resize`
- **页面交互**：`browser_click`、`browser_type`、`browser_select_option`
- **元素操作**：`browser_hover`、`browser_drag`、`browser_wait_for`
- **内容获取**：`browser_snapshot`、`browser_take_screenshot`、`browser_evaluate`
- **调试工具**：`browser_console_messages`、`browser_network_requests`

#### Sequential Thinking深度功能
- **逐步分析**：`sequentialthinking`工具进行分步推理
- **思维链构建**：建立完整的逻辑推理链条
- **方案评估**：对比多种解决方案的优缺点
- **问题分解**：将复杂问题拆解为可管理的子问题

#### Context 7实时查询
- **库文档查询**：`resolve-library-id`和`get-library-docs`获取最新文档
- **API参考**：获取详细的API使用说明和示例
- **最佳实践**：查询行业标准和推荐做法
- **代码示例**：获取实际可用的代码片段

#### mcp-deepwiki知识检索
- **知识检索**：`deepwiki_fetch`获取技术背景知识
- **概念解释**：深入解释技术概念和原理
- **架构模式**：提供成熟的设计模式和架构范式
- **行业通识**：补充领域相关的通用知识

#### knowledge-graph记忆管理
- **实体管理**：`create_entities`、`delete_entities`创建和管理实体
- **关系建立**：`create_relations`、`delete_relations`建立实体间关系
- **观察记录**：`add_observations`、`delete_observations`记录重要信息
- **知识检索**：`search_nodes`、`open_nodes`查找相关知识
- **图谱查看**：`read_graph`查看完整知识网络

### 5. Augment内置工具完整集成

#### 核心编辑工具
- **str-replace-editor**：精确代码编辑，限制150行，需instruction_reminder
- **save-file**：创建新文件，限制300行，需instructions_reminder
- **view**：查看文件和目录，支持正则搜索

#### 系统操作工具
- **launch-process**：执行系统命令、运行测试、文件操作
- **codebase-retrieval**：分析现有代码结构，获取项目上下文

#### 任务管理工具
- **task management**：
  - `add_tasks` - 创建新任务
  - `update_tasks` - 更新任务状态
  - `reorganize_tasklist` - 重新组织任务结构
  - `view_tasklist` - 查看当前任务列表

### 6. 工具协同最佳实践

#### 信息流管理
1. 始终从 knowledge-graph 开始，建立上下文
2. 使用 Sequential thinking 处理复杂逻辑
3. 用 Context 7/mcp-deepwiki 补充技术细节
4. 将结果反馈到 knowledge-graph

#### 错误处理策略
1. Sequential thinking 分析问题根因
2. Context 7 查找解决方案
3. Playwright 验证修复效果
4. knowledge-graph 记录解决过程

#### 性能优化原则
1. 优先使用缓存的知识图谱信息
2. 批量处理 Context 7 查询
3. 合理使用 Sequential thinking 避免过度分析
4. Playwright 操作尽量自动化

#### 质量保证机制
1. 每个阶段都有对应的验证工具
2. 关键决策必须有 Sequential thinking 支持
3. 重要发现必须记录到 knowledge-graph
4. 最终结果必须通过 Playwright 验证

## 对RIPER-5协议的改进效果

### 1. 工具可用性保证
- 所有工具引用都对应实际可用功能
- 避免调用不存在工具导致的错误
- 提高RIPER-5协议的执行成功率

### 2. 功能覆盖完整性
- 每个RIPER-5模式都有完整的工具支持
- MCP工具与内置工具形成完整工具链
- 工具间协同更加合理高效

### 3. 使用指导精确性
- 明确的使用时机和场景指导
- 详细的功能说明和使用限制
- 实用的最佳实践建议

### 4. 开发效率提升
- 工具选择更加明确和合理
- 减少工具使用的困惑和错误
- 提高开发任务的执行效率

## 对福彩3D项目的特殊价值

### 1. 项目管理优化
- serena工具提供精确的项目分析和代码编辑
- knowledge-graph保持项目上下文和经验积累
- task management支持复杂任务的依赖管理

### 2. 技术方案优化
- Sequential thinking支持复杂算法的深度分析
- Context 7提供最新的机器学习和数据处理技术
- mcp-deepwiki补充预测算法的理论背景

### 3. 质量保证优化
- Playwright自动化测试Streamlit界面功能
- serena验证代码修改的正确性
- 完整的工具链确保开发质量

### 4. 开发流程优化
- 每个RIPER-5模式都有明确的工具配置
- 工具协同策略提高开发效率
- 最佳实践指导减少开发错误

## 更新完成确认

✅ **所有工具引用已更新为实际可用名称**
✅ **各RIPER-5模式的工具配置已优化**
✅ **工具优先级体系已重构**
✅ **详细工具使用指南已添加**
✅ **Augment内置工具已完整集成**
✅ **工具协同最佳实践已完善**
✅ **RIPER-5协议功能覆盖已完整**

现在 `riper-5.mdc` 文件包含了完善的工具使用规则，为福彩3D预测系统和其他项目的RIPER-5开发提供了准确可靠的工具指导，确保开发效率和质量的双重提升！