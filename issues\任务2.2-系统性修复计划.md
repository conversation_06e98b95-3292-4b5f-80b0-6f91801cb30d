# 任务2.2 系统性修复计划

## 📋 任务概述

**任务名称**: 修复WebSocket端点不一致和页面导航问题  
**创建时间**: 2025-08-01  
**优先级**: 高  
**预计工期**: 1-2小时  
**前置任务**: 任务2.2 UI集成修复（已部分完成）

## 🎯 问题描述

在任务2.2的评审中发现以下系统性问题：

### 1. WebSocket端点配置不一致 ❌
**问题**: 多个文件仍使用旧的 `bug-detection` 端点，导致系统行为不一致
- `src/ui/components/page_manager.py` 第636行
- `src/ui/components/websocket_client.py` 第32行和493行  
- `src/ui/lottery_prediction_app.py` 第128行

**影响**: 控制台仍显示 `ws://127.0.0.1:8888/ws/bug-detection`，用户看到混合的连接状态

### 2. 页面导航功能异常 ❌
**问题**: 无法正常切换到预测分析页面
- 点击预测分析按钮后页面仍显示"数据概览"
- 阻止了对实时推送功能的完整验证

**影响**: 用户无法访问修复后的实时推送功能

## 🔧 修复目标

- ✅ 统一所有文件中的WebSocket端点为 `prediction-results`
- ✅ 修复页面导航功能，确保预测分析页面可正常访问
- ✅ 验证实时推送功能的完整工作流程
- ✅ 确保系统行为一致性和用户体验

## 📁 涉及文件

| 文件路径 | 修改类型 | 问题描述 |
|---------|---------|---------|
| `src/ui/components/page_manager.py` | 修改 | 第636行JavaScript中的WebSocket URL |
| `src/ui/components/websocket_client.py` | 修改 | 第32行和493行的默认参数 |
| `src/ui/lottery_prediction_app.py` | 修改 | 第128行的函数调用参数 |
| `src/ui/main.py` | 调试 | main_legacy函数中的页面路由逻辑 |

## 🔄 详细实施计划

### 阶段1: 修复WebSocket端点配置

#### 步骤1.1: 修复page_manager.py
**文件**: `src/ui/components/page_manager.py`  
**位置**: 第636行  
**修改内容**:
```javascript
// 修改前
const ws = new WebSocket('ws://127.0.0.1:8888/ws/bug-detection');

// 修改后
const ws = new WebSocket('ws://127.0.0.1:8888/ws/prediction-results');
```
**风险等级**: 低  
**预期结果**: JavaScript WebSocket连接使用正确端点

#### 步骤1.2: 修复websocket_client.py
**文件**: `src/ui/components/websocket_client.py`  
**位置**: 第32行和493行  
**修改内容**:
```python
# 修改前 (第32行)
def __init__(self, websocket_url: str = "ws://127.0.0.1:8888/ws/bug-detection", log_level: str = 'WARN'):

# 修改后
def __init__(self, websocket_url: str = "ws://127.0.0.1:8888/ws/prediction-results", log_level: str = 'WARN'):

# 修改前 (第493行)
def inject_websocket_client(websocket_url: str = "ws://127.0.0.1:8888/ws/bug-detection", log_level: str = 'WARN'):

# 修改后
def inject_websocket_client(websocket_url: str = "ws://127.0.0.1:8888/ws/prediction-results", log_level: str = 'WARN'):
```
**风险等级**: 中  
**预期结果**: 所有使用默认参数的WebSocket客户端连接到正确端点

#### 步骤1.3: 修复lottery_prediction_app.py
**文件**: `src/ui/lottery_prediction_app.py`  
**位置**: 第128行  
**修改内容**:
```python
# 修改前
inject_websocket_client("ws://127.0.0.1:8888/ws/bug-detection", log_level='WARN')

# 修改后
inject_websocket_client("ws://127.0.0.1:8888/ws/prediction-results", log_level='WARN')
```
**风险等级**: 低  
**预期结果**: lottery应用使用正确的WebSocket端点

### 阶段2: 调试页面导航问题

#### 步骤2.1: 分析页面路由逻辑
**文件**: `src/ui/main.py`  
**函数**: `main_legacy` (行1124-1642)  
**调试内容**:
- 检查 `selected_page` 变量的处理逻辑
- 确认"预测分析"选项的路由配置
- 验证 `show_prediction_page` 函数的调用条件

#### 步骤2.2: 修复页面切换机制
**预期修复**:
- 确保页面选择器正确响应用户操作
- 修复页面内容显示逻辑
- 验证页面状态管理

### 阶段3: 验证修复效果

#### 步骤3.1: WebSocket端点一致性验证
**验证内容**:
- 重启Streamlit服务
- 检查控制台日志，确保没有bug-detection端点连接
- 验证WebSocket连接状态显示

#### 步骤3.2: 页面导航功能验证
**验证内容**:
- 测试从数据概览切换到预测分析页面
- 确保预测分析页面正确显示
- 验证实时推送仪表盘显示

#### 步骤3.3: 端到端功能验证
**验证内容**:
- 验证实时推送功能完整工作流程
- 测试WebSocket连接和推送功能
- 确保用户体验流畅

## ⚠️ 风险评估

| 风险等级 | 风险描述 | 缓解措施 |
|---------|---------|---------|
| 低 | JavaScript字符串替换 | 精确匹配，影响范围明确 |
| 低 | lottery_prediction_app.py参数修改 | 简单字符串替换 |
| 中 | websocket_client.py默认参数修改 | 可能影响其他使用默认参数的代码 |
| 高 | 页面导航逻辑调试 | 可能涉及复杂的路由机制 |

## 🎯 验收标准

### WebSocket端点一致性
- [ ] 所有文件中的WebSocket URL统一为prediction-results
- [ ] 控制台不再显示bug-detection端点连接尝试
- [ ] WebSocket连接状态正确显示

### 页面导航功能
- [ ] 可以正常从数据概览切换到预测分析页面
- [ ] 预测分析页面正确显示内容
- [ ] 页面切换响应及时，无异常

### 实时推送功能
- [ ] 预测分析页面显示"🔄 实时预测监控"部分
- [ ] WebSocket连接状态指示器正常工作
- [ ] 实时推送仪表盘正确渲染
- [ ] 用户可以完整体验实时推送功能

### 系统稳定性
- [ ] 修改后系统启动正常
- [ ] 所有现有功能不受影响
- [ ] 错误处理机制正常工作

## 📝 实施清单

1. [ ] 修复page_manager.py中的WebSocket端点 (预计15分钟)
2. [ ] 修复websocket_client.py中的默认参数 (预计10分钟)  
3. [ ] 修复lottery_prediction_app.py中的调用 (预计5分钟)
4. [ ] 调试页面导航问题 (预计30-45分钟)
5. [ ] 验证系统性修复效果 (预计20分钟)

**总预计时间**: 1小时20分钟 - 1小时35分钟

## 🔄 回滚计划

如果修复过程中出现问题：
1. 立即停止当前修改
2. 使用git回滚到修改前状态
3. 分析问题原因，调整修复策略
4. 分步骤重新实施，每步验证

## 📊 成功指标

- **系统一致性**: 100% - 所有WebSocket端点统一
- **功能完整性**: 100% - 页面导航和实时推送功能正常
- **用户体验**: 优秀 - 界面响应流畅，功能易用
- **稳定性**: 无错误 - 系统稳定运行

## 🔗 相关任务

- **前置任务**: 任务2.2 UI集成修复 (已部分完成)
- **后续任务**: 任务2.2最终验收和用户确认

---

**创建者**: AI Assistant  
**审核者**: 待定  
**状态**: 待实施
