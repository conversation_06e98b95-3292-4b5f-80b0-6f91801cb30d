"""
训练完成深度管理页面
提供超参数调节面板、实时训练曲线、WebSocket连接管理
"""

import asyncio
import json
import os
# 导入训练监控模块
import sys
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from plotly.subplots import make_subplots

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from model_library.training.bayesian_recommender import \
        BayesianHyperparameterRecommender
    from model_library.training.websocket_monitor import \
        WebSocketTrainingMonitor
except ImportError as e:
    st.error(f"导入训练监控模块失败: {e}")
    st.stop()


def initialize_session_state():
    """初始化会话状态"""
    if 'ws_monitor' not in st.session_state:
        st.session_state.ws_monitor = WebSocketTrainingMonitor()
    
    if 'bayesian_recommender' not in st.session_state:
        st.session_state.bayesian_recommender = BayesianHyperparameterRecommender()
    
    if 'current_session_id' not in st.session_state:
        st.session_state.current_session_id = None
    
    if 'training_config' not in st.session_state:
        st.session_state.training_config = {}
    
    if 'metrics_history' not in st.session_state:
        st.session_state.metrics_history = []
    
    if 'auto_refresh' not in st.session_state:
        st.session_state.auto_refresh = True


def show_hyperparameter_tuning_panel():
    """显示超参数调节面板"""
    st.subheader("🎛️ 智能超参数调节")
    
    # 模型选择
    col1, col2 = st.columns([2, 1])
    
    with col1:
        model_options = {
            "intelligent_fusion": "智能融合预测系统",
            "deep_learning_cnn_lstm": "深度学习预测模型", 
            "trend_analyzer": "趋势分析模型",
            "markov_enhanced": "增强版马尔可夫链模型"
        }
        
        selected_model = st.selectbox(
            "选择目标模型",
            options=list(model_options.keys()),
            format_func=lambda x: model_options[x],
            key="training_model_select"
        )
    
    with col2:
        if st.button("🤖 获取智能推荐", type="primary"):
            with st.spinner("正在生成智能推荐..."):
                recommendation = st.session_state.bayesian_recommender.recommend_next_hyperparameters(
                    selected_model
                )
                st.session_state.recommendation = recommendation
    
    # 显示推荐结果
    if hasattr(st.session_state, 'recommendation'):
        rec = st.session_state.recommendation
        
        st.success("✅ 智能推荐完成！")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("推荐置信度", f"{rec['confidence']:.3f}")
        with col2:
            st.metric("预期改进", f"{rec['expected_improvement']:.3f}")
        with col3:
            st.metric("采集函数", rec.get('acquisition_function', 'N/A'))
        
        st.info(f"💡 **推荐理由**: {rec['reasoning']}")
        
        # 显示推荐参数
        st.markdown("#### 📋 推荐参数配置")
        recommended_params = rec['recommended_parameters']
    else:
        # 默认参数
        recommended_params = {
            "learning_rate": 0.001,
            "batch_size": 64,
            "epochs": 100,
            "optimizer": "adam"
        }
    
    # 参数调节界面
    st.markdown("#### ⚙️ 参数配置")
    
    config = {}
    
    col1, col2 = st.columns(2)
    
    with col1:
        config["learning_rate"] = st.number_input(
            "学习率",
            min_value=0.00001,
            max_value=0.1,
            value=float(recommended_params.get("learning_rate", 0.001)),
            step=0.0001,
            format="%.5f"
        )
        
        # 智能batch_size匹配逻辑
        valid_batch_sizes = [16, 32, 64, 128, 256]
        recommended_batch = recommended_params.get("batch_size", 64)

        def find_closest_batch_size(recommended_size, valid_options):
            """找到最接近推荐值的有效batch_size"""
            return min(valid_options, key=lambda x: abs(x - recommended_size))

        closest_batch = find_closest_batch_size(recommended_batch, valid_batch_sizes)

        # 如果推荐值与最接近值不同，显示调整提示
        if recommended_batch != closest_batch:
            st.info(f"💡 推荐的batch_size ({recommended_batch}) 已调整为最接近的有效值 ({closest_batch})")

        config["batch_size"] = st.selectbox(
            "批次大小",
            options=valid_batch_sizes,
            index=valid_batch_sizes.index(closest_batch)
        )
        
        config["epochs"] = st.slider(
            "训练轮次",
            min_value=10,
            max_value=500,
            value=int(recommended_params.get("epochs", 100)),
            step=10
        )
    
    with col2:
        config["optimizer"] = st.selectbox(
            "优化器",
            options=["adam", "sgd", "rmsprop", "adamw"],
            index=["adam", "sgd", "rmsprop", "adamw"].index(recommended_params.get("optimizer", "adam"))
        )
        
        config["dropout_rate"] = st.slider(
            "Dropout率",
            min_value=0.0,
            max_value=0.8,
            value=float(recommended_params.get("dropout_rate", 0.2)),
            step=0.1
        )
        
        config["l2_regularization"] = st.number_input(
            "L2正则化",
            min_value=0.000001,
            max_value=0.01,
            value=float(recommended_params.get("l2_regularization", 0.0001)),
            step=0.000001,
            format="%.6f"
        )
    
    # 高级参数
    with st.expander("🔧 高级参数配置"):
        col1, col2 = st.columns(2)
        
        with col1:
            config["early_stopping"] = st.checkbox("启用早停", value=True)
            if config["early_stopping"]:
                config["patience"] = st.slider("早停耐心值", 5, 50, 10)
                config["min_delta"] = st.number_input("最小改进", 0.0001, 0.01, 0.001, step=0.0001)
        
        with col2:
            config["lr_scheduler"] = st.selectbox(
                "学习率调度器",
                options=["none", "step", "exponential", "cosine"],
                index=0
            )
            
            if config["lr_scheduler"] != "none":
                config["lr_decay"] = st.slider("学习率衰减", 0.1, 0.9, 0.5, step=0.1)
    
    st.session_state.training_config = config
    
    return config


def show_real_time_training_curves():
    """显示实时训练曲线"""
    st.subheader("📈 实时训练曲线")
    
    # 获取训练指标历史
    if st.session_state.current_session_id:
        metrics_history = st.session_state.ws_monitor.get_session_metrics(
            st.session_state.current_session_id
        )
    else:
        # 生成模拟数据用于演示
        metrics_history = generate_mock_training_metrics()
    
    if not metrics_history:
        st.info("暂无训练数据，请启动训练会话")
        return
    
    # 创建训练曲线图表
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('训练损失', '训练准确率', '验证损失', '验证准确率'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # 提取数据
    epochs = [m.get('epoch', i) for i, m in enumerate(metrics_history)]
    train_loss = [m.get('metrics', {}).get('loss', 0) for m in metrics_history]
    train_acc = [m.get('metrics', {}).get('accuracy', 0) for m in metrics_history]
    val_loss = [m.get('metrics', {}).get('val_loss', 0) for m in metrics_history]
    val_acc = [m.get('metrics', {}).get('val_accuracy', 0) for m in metrics_history]
    
    # 训练损失
    fig.add_trace(
        go.Scatter(x=epochs, y=train_loss, mode='lines+markers', name='训练损失', 
                  line=dict(color='red', width=2)),
        row=1, col=1
    )
    
    # 训练准确率
    fig.add_trace(
        go.Scatter(x=epochs, y=train_acc, mode='lines+markers', name='训练准确率',
                  line=dict(color='blue', width=2)),
        row=1, col=2
    )
    
    # 验证损失
    fig.add_trace(
        go.Scatter(x=epochs, y=val_loss, mode='lines+markers', name='验证损失',
                  line=dict(color='orange', width=2)),
        row=2, col=1
    )
    
    # 验证准确率
    fig.add_trace(
        go.Scatter(x=epochs, y=val_acc, mode='lines+markers', name='验证准确率',
                  line=dict(color='green', width=2)),
        row=2, col=2
    )
    
    fig.update_layout(
        height=600,
        showlegend=False,
        title_text="实时训练指标监控"
    )
    
    # 更新坐标轴标签
    fig.update_xaxes(title_text="轮次", row=2, col=1)
    fig.update_xaxes(title_text="轮次", row=2, col=2)
    fig.update_yaxes(title_text="损失", row=1, col=1)
    fig.update_yaxes(title_text="准确率", row=1, col=2)
    fig.update_yaxes(title_text="损失", row=2, col=1)
    fig.update_yaxes(title_text="准确率", row=2, col=2)
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 显示最新指标
    if metrics_history:
        latest_metrics = metrics_history[-1].get('metrics', {})
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("当前损失", f"{latest_metrics.get('loss', 0):.4f}")
        with col2:
            st.metric("当前准确率", f"{latest_metrics.get('accuracy', 0):.4f}")
        with col3:
            st.metric("验证损失", f"{latest_metrics.get('val_loss', 0):.4f}")
        with col4:
            st.metric("验证准确率", f"{latest_metrics.get('val_accuracy', 0):.4f}")


def show_websocket_connection_management():
    """显示WebSocket连接管理"""
    st.subheader("🔌 训练会话管理")
    
    # 连接状态
    active_sessions = st.session_state.ws_monitor.get_active_sessions()
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("活跃会话", len(active_sessions))
    
    with col2:
        connection_status = "🟢 已连接" if st.session_state.current_session_id else "🔴 未连接"
        st.metric("连接状态", connection_status)
    
    with col3:
        if st.session_state.current_session_id:
            session_info = active_sessions.get(st.session_state.current_session_id, {})
            progress = session_info.get('progress', 0)
            st.metric("训练进度", f"{progress:.1%}")
    
    # 会话控制
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🚀 启动训练", type="primary"):
            if st.session_state.training_config:
                try:
                    # 模拟启动训练会话
                    session_id = f"session_{int(time.time())}"
                    st.session_state.current_session_id = session_id
                    st.success(f"✅ 训练会话已启动: {session_id[:8]}...")
                except Exception as e:
                    st.error(f"启动训练失败: {e}")
            else:
                st.warning("请先配置训练参数")
    
    with col2:
        if st.button("⏸️ 暂停训练"):
            if st.session_state.current_session_id:
                st.info("训练已暂停")
            else:
                st.warning("没有活跃的训练会话")
    
    with col3:
        if st.button("▶️ 恢复训练"):
            if st.session_state.current_session_id:
                st.info("训练已恢复")
            else:
                st.warning("没有活跃的训练会话")
    
    with col4:
        if st.button("🛑 停止训练"):
            if st.session_state.current_session_id:
                st.session_state.current_session_id = None
                st.info("训练已停止")
            else:
                st.warning("没有活跃的训练会话")
    
    # 活跃会话列表
    if active_sessions:
        st.markdown("#### 📋 活跃会话列表")
        
        sessions_data = []
        for session_id, session_info in active_sessions.items():
            sessions_data.append({
                "会话ID": session_id[:12] + "...",
                "模型": session_info.get('model_id', 'N/A'),
                "状态": session_info.get('status', 'unknown'),
                "进度": f"{session_info.get('progress', 0):.1%}",
                "当前轮次": f"{session_info.get('current_epoch', 0)}/{session_info.get('total_epochs', 0)}",
                "开始时间": session_info.get('start_time', 'N/A')[:19] if session_info.get('start_time') else 'N/A'
            })
        
        st.dataframe(pd.DataFrame(sessions_data), use_container_width=True)


def show_training_analytics():
    """显示训练分析"""
    st.subheader("📊 训练分析")
    
    # 性能分析
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 🎯 性能指标分析")
        
        # 生成模拟的性能分析数据
        performance_data = {
            "指标": ["准确率", "精确率", "召回率", "F1分数", "AUC"],
            "当前值": [0.847, 0.832, 0.861, 0.846, 0.923],
            "目标值": [0.850, 0.840, 0.860, 0.850, 0.930],
            "差距": [-0.003, -0.008, 0.001, -0.004, -0.007]
        }
        
        df_performance = pd.DataFrame(performance_data)
        
        for _, row in df_performance.iterrows():
            delta_color = "normal" if row["差距"] >= 0 else "inverse"
            st.metric(
                row["指标"],
                f"{row['当前值']:.3f}",
                delta=f"{row['差距']:+.3f}",
                delta_color=delta_color
            )
    
    with col2:
        st.markdown("#### ⏱️ 训练效率分析")
        
        efficiency_metrics = {
            "每轮次时间": "2.3秒",
            "预计完成时间": "8分钟",
            "GPU利用率": "87%",
            "内存使用": "6.2GB",
            "收敛预测": "第78轮"
        }
        
        for metric, value in efficiency_metrics.items():
            st.metric(metric, value)
    
    # 超参数优化历史
    st.markdown("#### 📈 超参数优化历史")
    
    # 生成模拟的优化历史
    optimization_history = generate_mock_optimization_history()
    
    fig = px.scatter(
        optimization_history,
        x="learning_rate",
        y="accuracy",
        size="batch_size",
        color="optimizer",
        hover_data=["epochs"],
        title="超参数优化历史",
        labels={
            "learning_rate": "学习率",
            "accuracy": "准确率",
            "batch_size": "批次大小",
            "optimizer": "优化器"
        }
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_training_logs():
    """显示训练日志"""
    st.subheader("📝 训练日志")
    
    # 日志级别选择
    log_level = st.selectbox(
        "日志级别",
        options=["ALL", "INFO", "WARNING", "ERROR"],
        index=0
    )
    
    # 生成模拟日志
    logs = generate_mock_training_logs()
    
    # 过滤日志
    if log_level != "ALL":
        logs = [log for log in logs if log["level"] == log_level]
    
    # 显示日志
    log_container = st.container()
    
    with log_container:
        for log in logs[-20:]:  # 显示最近20条日志
            level_color = {
                "INFO": "🔵",
                "WARNING": "🟡", 
                "ERROR": "🔴"
            }.get(log["level"], "⚪")
            
            st.text(f"{level_color} {log['timestamp']} [{log['level']}] {log['message']}")
    
    # 自动刷新选项
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.session_state.auto_refresh = st.checkbox("自动刷新日志", value=st.session_state.auto_refresh)
    
    with col2:
        if st.button("🔄 手动刷新"):
            st.rerun()


def generate_mock_training_metrics():
    """生成模拟训练指标"""
    metrics = []
    
    for epoch in range(50):
        # 模拟训练过程中的指标变化
        base_loss = 1.0 * np.exp(-epoch * 0.05) + np.random.normal(0, 0.02)
        base_acc = 0.5 + 0.4 * (1 - np.exp(-epoch * 0.08)) + np.random.normal(0, 0.01)
        
        metrics.append({
            "epoch": epoch,
            "timestamp": (datetime.now() - timedelta(minutes=50-epoch)).isoformat(),
            "metrics": {
                "loss": max(0.1, base_loss),
                "accuracy": min(0.95, max(0.5, base_acc)),
                "val_loss": max(0.1, base_loss + np.random.normal(0, 0.05)),
                "val_accuracy": min(0.95, max(0.5, base_acc + np.random.normal(0, 0.02)))
            }
        })
    
    return metrics


def generate_mock_optimization_history():
    """生成模拟优化历史"""
    np.random.seed(42)
    
    data = []
    for i in range(20):
        data.append({
            "learning_rate": np.random.uniform(0.0001, 0.01),
            "batch_size": np.random.choice([32, 64, 128]),
            "epochs": np.random.randint(50, 200),
            "optimizer": np.random.choice(["adam", "sgd", "rmsprop"]),
            "accuracy": np.random.uniform(0.75, 0.92)
        })
    
    return pd.DataFrame(data)


def generate_mock_training_logs():
    """生成模拟训练日志"""
    logs = []
    
    log_messages = [
        ("INFO", "训练开始，模型：intelligent_fusion"),
        ("INFO", "数据加载完成，训练集：8000，验证集：2000"),
        ("INFO", "第1轮训练开始"),
        ("INFO", "第1轮完成，损失：0.856，准确率：0.723"),
        ("WARNING", "学习率可能过大，建议调整"),
        ("INFO", "第10轮完成，损失：0.432，准确率：0.834"),
        ("INFO", "验证集评估完成"),
        ("ERROR", "内存使用过高，建议减小批次大小"),
        ("INFO", "第20轮完成，损失：0.298，准确率：0.867"),
        ("INFO", "模型检查点已保存")
    ]
    
    for i, (level, message) in enumerate(log_messages):
        logs.append({
            "timestamp": (datetime.now() - timedelta(minutes=30-i*3)).strftime("%Y-%m-%d %H:%M:%S"),
            "level": level,
            "message": message
        })
    
    return logs


def main():
    """主函数"""
    st.set_page_config(
        page_title="训练监控深度管理",
        page_icon="📈",
        layout="wide"
    )
    
    st.title("📈 训练监控深度管理")
    st.markdown("---")
    
    # 初始化会话状态
    initialize_session_state()
    
    # 创建标签页
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🎛️ 超参数调节", 
        "📈 实时曲线", 
        "🔌 会话管理", 
        "📊 训练分析", 
        "📝 训练日志"
    ])
    
    with tab1:
        show_hyperparameter_tuning_panel()
    
    with tab2:
        show_real_time_training_curves()
    
    with tab3:
        show_websocket_connection_management()
    
    with tab4:
        show_training_analytics()
    
    with tab5:
        show_training_logs()
    
    # 自动刷新
    if st.session_state.auto_refresh and st.session_state.current_session_id:
        time.sleep(2)
        st.rerun()


if __name__ == "__main__":
    main()
