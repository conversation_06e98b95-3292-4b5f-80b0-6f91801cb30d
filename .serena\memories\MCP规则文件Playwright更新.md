# MCP规则文件Playwright更新记录

## 更新概述
已成功将 `.cursor\rules\mcprules.mdc` 文件中的 Browser MCP 替换为 Playwright，确保MCP工具使用规则与实际配置保持一致。

## 更新内容详解

### 1. 工具名称统一
- **替换前**：Browser MCP​
- **替换后**：Playwright
- **影响范围**：所有相关引用和说明

### 2. 具体修改位置

#### 评审模式更新
```markdown
# 修改前
- 使用`playwright`进行浏览器自动化操作，辅助排查网页问题

# 修改后  
- 使用`Playwright`进行浏览器自动化操作，辅助排查网页问题
```

#### 开发工作流程更新
```markdown
# 修改前
- **自检验证**：...可使用`Browser MCP`进行浏览器自动化操作，辅助排查网页问题

# 修改后
- **自检验证**：...可使用`Playwright`进行浏览器自动化操作，辅助排查网页问题
```

#### MCP服务优先级更新
```markdown
# 修改前
9. `Browser MCP​` -浏览器自动化操作

# 修改后
9. `Playwright` - 浏览器自动化操作
```

### 3. 工具使用指南完全重写

#### 新的Playwright工具说明
- **用途**：基于微软开源的Playwright框架，提供跨浏览器的自动化测试和网页交互能力
- **支持浏览器**：Chrome、Firefox、Safari等主流浏览器
- **运行模式**：支持有头模式和无头模式

#### 核心功能分类
1. **浏览器控制**：
   - `browser_navigate` - 页面导航
   - `browser_close` - 关闭浏览器
   - `browser_resize` - 调整窗口大小

2. **页面交互**：
   - `browser_click` - 点击元素
   - `browser_type` - 输入文本
   - `browser_select_option` - 选择下拉选项

3. **元素操作**：
   - `browser_hover` - 悬停操作
   - `browser_drag` - 拖拽操作
   - `browser_wait_for` - 等待元素或条件

4. **内容获取**：
   - `browser_snapshot` - 获取页面快照
   - `browser_take_screenshot` - 截图
   - `browser_evaluate` - 执行JavaScript

5. **调试工具**：
   - `browser_console_messages` - 获取控制台消息
   - `browser_network_requests` - 获取网络请求

#### 适用场景
- **Web应用自动化测试** - 全面测试Web应用功能
- **用户界面验证** - 验证UI组件和交互
- **网页功能测试** - 测试表单、按钮、链接等功能
- **浏览器兼容性测试** - 跨浏览器兼容性验证
- **网页问题诊断** - 排查加载、交互、性能问题

#### 使用时机
- **评审阶段**：验证Web界面功能和用户体验
- **测试阶段**：自动化测试Web应用的各项功能
- **调试阶段**：排查网页加载、交互、性能问题
- **验证阶段**：确认修改后的Web功能正常工作

#### 独特优势
- **多浏览器支持** - 一套代码测试多个浏览器
- **无头模式运行** - 提高测试执行效率
- **丰富的调试信息** - 详细的错误和性能数据
- **MCP协议深度集成** - 与Augment环境无缝集成

## 对福彩3D项目的特殊价值

### 1. Streamlit界面测试
- **页面功能验证**：自动测试所有8个功能页面
- **交互组件测试**：验证按钮、表单、图表交互
- **数据展示验证**：确认数据正确显示和更新

### 2. API接口测试
- **健康检查验证**：自动验证API服务状态
- **接口功能测试**：测试预测、数据管理等API
- **错误处理验证**：测试异常情况的处理

### 3. 用户体验验证
- **响应式设计测试**：验证不同屏幕尺寸的适配
- **加载性能测试**：监控页面加载时间
- **用户流程测试**：模拟完整的用户操作流程

### 4. 问题诊断支持
- **JavaScript错误监控**：捕获前端错误
- **网络请求分析**：分析API调用性能
- **控制台日志收集**：收集调试信息

## 与其他MCP工具的协同

### 1. 与Serena的协同
- **代码修改后验证**：使用Playwright验证Serena修改的代码效果
- **功能完整性测试**：确保代码修改不影响现有功能

### 2. 与Sequential Thinking的协同
- **测试策略分析**：使用Sequential Thinking制定测试计划
- **问题根因分析**：结合测试结果进行深度分析

### 3. 与Context7的协同
- **测试最佳实践**：查询Playwright测试的最佳实践
- **问题解决方案**：获取测试问题的解决方案

## 更新后的工具链优势

### 1. 完整的开发生命周期支持
- **研究阶段**：Serena项目分析 + Context7技术调研
- **构思阶段**：Sequential Thinking方案设计 + DeepWiki知识补充
- **计划阶段**：Serena精确定位 + Task Manager任务管理
- **执行阶段**：Serena精确编辑 + Desktop Commander系统操作
- **评审阶段**：Playwright功能验证 + Sequential Thinking质量分析

### 2. 质量保证体系
- **代码质量**：Serena精确编辑避免错误
- **功能质量**：Playwright自动化测试验证
- **用户体验**：Playwright用户流程测试
- **性能质量**：Playwright性能监控

## 更新确认

✅ **Browser MCP完全替换为Playwright**
✅ **所有相关引用已更新**
✅ **工具使用指南已重写**
✅ **核心功能详细说明已添加**
✅ **适用场景和使用时机已明确**
✅ **与福彩3D项目的结合点已阐述**

现在MCP规则文件与实际的Playwright配置完全一致，为福彩3D预测系统的开发和测试提供了更准确的工具使用指导！