"""
智能融合控制器

协调各个模块的工作，提供统一的接口。
"""

import logging
from typing import Any, Dict, List, Optional

from .fusion_context import FusionContext
from .trend_analysis_module import TrendAnalysisModule
from .pattern_prediction_module import PatternPredictionModule
from .lstm_prediction_module import LSTMPredictionModule
from .fusion_engine_module import FusionEngineModule
from .performance_tracker_module import PerformanceTrackerModule
from .validation_module import ValidationModule

logger = logging.getLogger(__name__)


class IntelligentFusionController:
    """智能融合控制器"""
    
    def __init__(self, context: FusionContext):
        """
        初始化控制器
        
        Args:
            context: 共享上下文
        """
        self.context = context
        self.logger = logger
        
        # 初始化各模块
        self.trend_module = TrendAnalysisModule(context)
        self.pattern_module = PatternPredictionModule(context)
        self.lstm_module = LSTMPredictionModule(context)
        self.fusion_module = FusionEngineModule(context)
        self.performance_module = PerformanceTrackerModule(context)
        self.validation_module = ValidationModule(context)
        
        self._initialized = False
    
    def initialize(self) -> bool:
        """
        初始化所有模块
        
        Returns:
            是否初始化成功
        """
        try:
            if not self.context.is_ready():
                self.logger.error("上下文未就绪，无法初始化控制器")
                return False
            
            # 初始化各模块
            modules = [
                self.trend_module,
                self.pattern_module,
                self.lstm_module,
                self.fusion_module,
                self.performance_module,
                self.validation_module
            ]
            
            success_count = 0
            for module in modules:
                if module.initialize():
                    success_count += 1
                else:
                    self.logger.warning(f"模块 {module.module_name} 初始化失败")
            
            self._initialized = success_count == len(modules)
            
            if self._initialized:
                self.logger.info("智能融合控制器初始化成功")
            else:
                self.logger.warning(f"智能融合控制器部分初始化失败 ({success_count}/{len(modules)})")
            
            return self._initialized
            
        except Exception as e:
            self.logger.error(f"控制器初始化失败: {e}")
            return False
    
    def is_ready(self) -> bool:
        """检查控制器是否就绪"""
        return self._initialized and self.context.is_ready()
    
    def generate_fusion_prediction(self, data_count: int = 50, **kwargs) -> Dict[str, Any]:
        """
        生成融合预测
        
        Args:
            data_count: 数据量
            **kwargs: 其他参数
            
        Returns:
            融合预测结果
        """
        if not self.is_ready():
            raise RuntimeError("控制器未就绪")
        
        try:
            self.logger.info(f"开始生成融合预测，数据量: {data_count}")
            
            # 1. 生成各模型预测
            trend_result = self.trend_module.generate_trend_predictions(data_count, **kwargs)
            pattern_result = self.pattern_module.generate_pattern_predictions(data_count, **kwargs)
            lstm_result = self.lstm_module.generate_lstm_predictions(data_count, **kwargs)
            
            # 2. 执行融合算法
            fusion_result = self.fusion_module.generate_fusion_prediction(
                trend_result, pattern_result, lstm_result, **kwargs
            )
            
            # 3. 验证结果
            validation_result = self.validation_module.validate_prediction_result(
                fusion_result['fusion_prediction']
            )
            
            # 4. 组装最终结果
            final_result = {
                "fusion_prediction": fusion_result['fusion_prediction'],
                "individual_predictions": {
                    "trend": trend_result,
                    "pattern": pattern_result,
                    "lstm": lstm_result
                },
                "fusion_weights": fusion_result['weights'],
                "validation": validation_result,
                "metadata": {
                    "data_count": data_count,
                    "controller_version": "1.0.0",
                    "timestamp": fusion_result.get('timestamp')
                }
            }
            
            self.logger.info("融合预测生成完成")
            return final_result
            
        except Exception as e:
            self.logger.error(f"生成融合预测失败: {e}")
            raise
    
    def train_all_models(self, **kwargs) -> Dict[str, Any]:
        """
        训练所有模型
        
        Args:
            **kwargs: 训练参数
            
        Returns:
            训练结果
        """
        if not self.is_ready():
            raise RuntimeError("控制器未就绪")
        
        try:
            self.logger.info("开始训练所有模型")
            
            # 这里应该调用各模型的训练方法
            # 由于当前模块主要负责预测，训练逻辑可能在其他地方
            # 暂时返回模拟的训练结果
            
            training_result = {
                "status": "completed",
                "models_trained": ["trend", "pattern", "lstm", "fusion"],
                "training_time": "模拟训练完成",
                "performance_summary": self.get_system_summary()
            }
            
            # 更新上下文状态
            self.context.update_training_status(True, kwargs.get('data_count', 0))
            
            self.logger.info("模型训练完成")
            return training_result
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
    
    def get_system_summary(self) -> Dict[str, Any]:
        """
        获取系统摘要
        
        Returns:
            系统摘要信息
        """
        try:
            # 获取各模块状态
            modules_status = {
                "trend": self.trend_module.get_status(),
                "pattern": self.pattern_module.get_status(),
                "lstm": self.lstm_module.get_status(),
                "fusion": self.fusion_module.get_status(),
                "performance": self.performance_module.get_status(),
                "validation": self.validation_module.get_status()
            }
            
            # 获取性能统计
            performance_summary = self.performance_module.get_all_models_performance()
            
            # 获取验证状态
            validation_status = self.validation_module.get_validation_status()
            
            return {
                "controller_ready": self.is_ready(),
                "context_summary": self.context.get_summary(),
                "modules_status": modules_status,
                "performance_summary": performance_summary,
                "validation_status": validation_status,
                "system_health": self._assess_system_health(modules_status)
            }
            
        except Exception as e:
            self.logger.error(f"获取系统摘要失败: {e}")
            return {"error": str(e)}
    
    def update_model_performance(self, model_name: str, prediction: str, actual: str, confidence: float = None) -> bool:
        """
        更新模型性能
        
        Args:
            model_name: 模型名称
            prediction: 预测结果
            actual: 实际结果
            confidence: 置信度
            
        Returns:
            是否更新成功
        """
        try:
            return self.performance_module.update_model_performance(
                model_name, prediction, actual, confidence
            )
        except Exception as e:
            self.logger.error(f"更新模型性能失败: {e}")
            return False
    
    def get_performance_ranking(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        获取性能排行榜
        
        Args:
            days: 统计天数
            
        Returns:
            性能排行榜
        """
        try:
            return self.performance_module.get_performance_ranking(days)
        except Exception as e:
            self.logger.error(f"获取性能排行榜失败: {e}")
            return []
    
    def validate_markov_model(self) -> Dict[str, Any]:
        """验证马尔可夫模型"""
        try:
            return self.validation_module.validate_markov_model()
        except Exception as e:
            self.logger.error(f"验证马尔可夫模型失败: {e}")
            return {"error": str(e)}
    
    def get_validation_status(self) -> Dict[str, Any]:
        """获取验证状态"""
        try:
            return self.validation_module.get_validation_status()
        except Exception as e:
            self.logger.error(f"获取验证状态失败: {e}")
            return {"error": str(e)}
    
    def _assess_system_health(self, modules_status: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """评估系统健康状态"""
        try:
            ready_modules = sum(1 for status in modules_status.values() if status.get('ready', False))
            total_modules = len(modules_status)
            
            health_score = ready_modules / total_modules if total_modules > 0 else 0
            
            if health_score >= 0.9:
                health_status = "优秀"
            elif health_score >= 0.7:
                health_status = "良好"
            elif health_score >= 0.5:
                health_status = "一般"
            else:
                health_status = "需要关注"
            
            return {
                "health_score": round(health_score, 2),
                "health_status": health_status,
                "ready_modules": ready_modules,
                "total_modules": total_modules,
                "issues": [name for name, status in modules_status.items() if not status.get('ready', False)]
            }
            
        except Exception as e:
            self.logger.warning(f"评估系统健康状态失败: {e}")
            return {"health_status": "未知", "error": str(e)}
    
    def cleanup(self):
        """清理资源"""
        try:
            modules = [
                self.trend_module,
                self.pattern_module,
                self.lstm_module,
                self.fusion_module,
                self.performance_module,
                self.validation_module
            ]
            
            for module in modules:
                module.cleanup()
            
            self._initialized = False
            self.logger.info("控制器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"控制器清理失败: {e}")
