"""
数据流调试监控面板
用于诊断Bug检测系统的数据流问题
"""

import json
import time
from datetime import datetime
from typing import Any, Dict, Optional

import streamlit as st
import streamlit.components.v1 as components

# 导入数据流追踪器
from src.bug_detection.monitoring.data_flow_tracer import (FlowStage,
                                                           FlowStatus,
                                                           data_flow_tracer,
                                                           get_trace_report)


def show_debug_data_flow():
    """显示数据流调试监控面板"""
    st.title("🔍 数据流调试监控面板")
    st.markdown("---")
    
    # 创建两列布局
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📊 实时追踪概览")
        
        # 获取所有追踪的摘要
        summary = get_trace_report()
        
        # 显示基本统计
        metrics_col1, metrics_col2, metrics_col3, metrics_col4 = st.columns(4)
        
        with metrics_col1:
            st.metric("活跃追踪", summary.get("active_traces", 0))
        
        with metrics_col2:
            st.metric("已完成追踪", summary.get("completed_traces", 0))
        
        with metrics_col3:
            st.metric("总追踪数", summary.get("total_traces", 0))
        
        with metrics_col4:
            if summary.get("stage_statistics"):
                avg_success_rate = sum(
                    stats.get("success_rate", 0) 
                    for stats in summary["stage_statistics"].values()
                ) / len(summary["stage_statistics"])
                st.metric("平均成功率", f"{avg_success_rate:.1f}%")
            else:
                st.metric("平均成功率", "0%")
    
    with col2:
        st.subheader("🎛️ 控制面板")
        
        # 刷新按钮
        if st.button("🔄 刷新数据", use_container_width=True):
            st.rerun()
        
        # 清理按钮
        if st.button("🧹 清理旧数据", use_container_width=True):
            data_flow_tracer._cleanup_old_traces()
            st.success("已清理旧数据")
            st.rerun()
        
        # 测试按钮
        if st.button("🧪 触发测试错误", use_container_width=True):
            # 触发一个测试错误来验证追踪系统
            test_error_data = {
                "type": "test_error",
                "message": "数据流调试测试错误",
                "timestamp": time.time(),
                "url": "debug_panel",
                "session_id": st.session_state.get("session_id", "debug")
            }

            from src.bug_detection.monitoring.data_flow_tracer import \
                start_error_trace
            trace_id = start_error_trace(test_error_data)

            # 同时触发一个真实的JavaScript错误来测试完整的错误处理流程
            test_js_error_script = """
            <script>
            // 触发一个测试JavaScript错误
            setTimeout(function() {
                try {
                    // 故意触发一个错误
                    throw new Error("调试页面测试错误 - " + new Date().toISOString());
                } catch (e) {
                    console.error("测试错误已触发:", e);
                    // 错误会被全局错误监控器捕获
                }
            }, 100);
            </script>
            """
            components.html(test_js_error_script, height=0)

            st.success(f"已触发测试错误，追踪ID: {trace_id[:8]}")
            st.rerun()

    # 添加JavaScript监控测试
    st.subheader("🔧 JavaScript监控测试")
    if st.button("🧪 测试JavaScript监控", use_container_width=True):
        test_monitoring_script = """
        <script>
        console.log('🔧 JavaScript监控测试开始');

        // 检查是否存在Bug检测系统
        if (window.bugDetector) {
            console.log('✅ Bug检测系统已存在');
            console.log('Bug检测系统状态:', window.bugDetector.getErrorStats());
        } else {
            console.log('❌ Bug检测系统不存在');
        }

        // 手动触发一个测试错误
        setTimeout(function() {
            try {
                throw new Error('手动测试错误 - ' + new Date().toISOString());
            } catch (e) {
                console.error('手动测试错误已触发:', e);
            }
        }, 100);
        </script>
        """
        components.html(test_monitoring_script, height=0)
        st.success("JavaScript监控测试已触发，请检查控制台")
    
    # 阶段统计表格
    if summary.get("stage_statistics"):
        st.subheader("📈 各阶段统计")
        
        stage_data = []
        for stage, stats in summary["stage_statistics"].items():
            stage_data.append({
                "阶段": stage,
                "总数": stats["total"],
                "成功": stats["success"],
                "失败": stats["failed"],
                "成功率": f"{stats['success_rate']:.1f}%"
            })
        
        st.dataframe(stage_data, use_container_width=True)
    
    # 最近追踪记录
    st.subheader("📋 最近追踪记录")
    
    recent_traces = summary.get("recent_traces", [])
    if recent_traces:
        # 创建选择框来查看具体追踪
        selected_trace = st.selectbox(
            "选择追踪记录查看详情:",
            options=[""] + recent_traces,
            format_func=lambda x: f"追踪 {x[:8]}..." if x else "请选择追踪记录"
        )
        
        if selected_trace:
            # 显示选中追踪的详细信息
            trace_detail = get_trace_report(selected_trace)
            
            if "error" not in trace_detail:
                st.subheader(f"🔍 追踪详情: {selected_trace[:8]}")
                
                # 基本信息
                detail_col1, detail_col2, detail_col3 = st.columns(3)
                
                with detail_col1:
                    st.metric("总阶段数", trace_detail["total_stages"])
                
                with detail_col2:
                    st.metric("成功阶段", trace_detail["success_count"])
                
                with detail_col3:
                    st.metric("失败阶段", trace_detail["failed_count"])
                
                # 总耗时
                if trace_detail.get("total_duration_ms"):
                    st.metric("总耗时", f"{trace_detail['total_duration_ms']:.2f}ms")
                
                # 阶段流程图
                st.subheader("🔄 数据流程")
                stages_completed = trace_detail.get("stages_completed", [])
                
                # 创建流程可视化
                flow_text = " → ".join(stages_completed)
                st.text(flow_text)
                
                # 详细追踪记录
                st.subheader("📝 详细记录")
                
                traces = trace_detail.get("traces", [])
                for i, trace in enumerate(traces):
                    with st.expander(f"阶段 {i+1}: {trace['stage']} - {trace['status']}"):
                        col_a, col_b = st.columns(2)
                        
                        with col_a:
                            st.write("**时间戳:**", datetime.fromtimestamp(trace['timestamp']).strftime("%H:%M:%S.%f")[:-3])
                            st.write("**状态:**", trace['status'])
                            if trace.get('duration_ms'):
                                st.write("**耗时:**", f"{trace['duration_ms']:.2f}ms")
                        
                        with col_b:
                            if trace.get('error_message'):
                                st.error(f"错误: {trace['error_message']}")
                            
                            if trace.get('data'):
                                st.write("**数据:**")
                                st.json(trace['data'])
                            
                            if trace.get('metadata'):
                                st.write("**元数据:**")
                                st.json(trace['metadata'])
            else:
                st.error(f"获取追踪详情失败: {trace_detail['error']}")
    else:
        st.info("暂无追踪记录")
    
    # WebSocket连接状态检查
    st.subheader("🔌 WebSocket连接状态")
    
    # 添加JavaScript来检查WebSocket状态
    websocket_status_script = """
    <script>
    function checkWebSocketStatus() {
        const statusDiv = document.getElementById('websocket-status');
        
        // 安全的DOM操作函数
        function safeSetHTML(element, html) {
            if (element) {
                element.innerHTML = html;
                return true;
            } else {
                console.warn('元素不存在');
                return false;
            }
        }

        try {
            // 尝试连接WebSocket
            const ws = new WebSocket('ws://127.0.0.1:8888/ws/bug-detection');

            ws.onopen = function() {
                safeSetHTML(statusDiv, '<div style="color: green;">✅ WebSocket连接正常</div>');
                ws.close();
            };

            ws.onerror = function() {
                safeSetHTML(statusDiv, '<div style="color: red;">❌ WebSocket连接失败</div>');
            };

            ws.onclose = function(event) {
                if (event.code !== 1000) {
                    safeSetHTML(statusDiv, '<div style="color: orange;">⚠️ WebSocket连接异常关闭</div>');
                }
            };
            
            // 5秒超时
            setTimeout(() => {
                if (ws.readyState === WebSocket.CONNECTING) {
                    statusDiv.innerHTML = '<div style="color: red;">❌ WebSocket连接超时</div>';
                    ws.close();
                }
            }, 5000);
            
        } catch (error) {
            statusDiv.innerHTML = '<div style="color: red;">❌ WebSocket创建失败: ' + error.message + '</div>';
        }
    }
    
    // 页面加载后检查状态
    document.addEventListener('DOMContentLoaded', checkWebSocketStatus);
    checkWebSocketStatus();
    </script>
    
    <div id="websocket-status">
        <div style="color: gray;">🔄 正在检查WebSocket连接...</div>
    </div>
    """
    
    components.html(websocket_status_script, height=100)
    
    # 自动刷新选项
    st.subheader("⚙️ 设置")
    
    auto_refresh = st.checkbox("启用自动刷新 (每5秒)", value=False)
    
    if auto_refresh:
        time.sleep(5)
        st.rerun()

if __name__ == "__main__":
    show_debug_data_flow()
