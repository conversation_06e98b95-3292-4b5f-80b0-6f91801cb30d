"""
异常处理中间件

为API和UI提供统一的异常处理机制。
"""

import logging
import traceback
from typing import Any, Dict, Optional

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from .base import ErrorCode, ErrorSeverity, LotterySystemException

logger = logging.getLogger(__name__)


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """异常处理中间件"""
    
    def __init__(self, app, debug: bool = False):
        super().__init__(app)
        self.debug = debug
    
    async def dispatch(self, request: Request, call_next):
        """处理请求和异常"""
        try:
            response = await call_next(request)
            return response
        except Exception as exc:
            return await self.handle_exception(request, exc)
    
    async def handle_exception(self, request: Request, exc: Exception) -> Response:
        """处理异常并返回适当的响应"""
        
        # 记录异常信息
        self._log_exception(request, exc)
        
        # 处理不同类型的异常
        if isinstance(exc, LotterySystemException):
            return self._handle_lottery_exception(exc)
        elif isinstance(exc, HTTPException):
            return self._handle_http_exception(exc)
        elif isinstance(exc, ValueError):
            return self._handle_value_error(exc)
        elif isinstance(exc, KeyError):
            return self._handle_key_error(exc)
        elif isinstance(exc, FileNotFoundError):
            return self._handle_file_not_found_error(exc)
        else:
            return self._handle_unknown_exception(exc)
    
    def _handle_lottery_exception(self, exc: LotterySystemException) -> JSONResponse:
        """处理系统自定义异常"""
        status_code = self._get_status_code_from_error_code(exc.code)
        
        response_data = {
            "success": False,
            "error": {
                "code": exc.code.value,
                "message": exc.to_user_message(),
                "severity": exc.severity.value,
                "timestamp": exc.timestamp.isoformat()
            }
        }
        
        # 在调试模式下包含详细信息
        if self.debug:
            response_data["error"]["details"] = exc.details
            response_data["error"]["traceback"] = exc.traceback_info
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    def _handle_http_exception(self, exc: HTTPException) -> JSONResponse:
        """处理HTTP异常"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "code": f"HTTP{exc.status_code}",
                    "message": exc.detail,
                    "severity": "medium"
                }
            }
        )
    
    def _handle_value_error(self, exc: ValueError) -> JSONResponse:
        """处理值错误"""
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "error": {
                    "code": ErrorCode.INVALID_PARAMETER.value,
                    "message": "输入参数无效",
                    "severity": ErrorSeverity.LOW.value,
                    "details": {"original_error": str(exc)} if self.debug else {}
                }
            }
        )
    
    def _handle_key_error(self, exc: KeyError) -> JSONResponse:
        """处理键错误"""
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "error": {
                    "code": ErrorCode.MISSING_PARAMETER.value,
                    "message": "缺少必要参数",
                    "severity": ErrorSeverity.LOW.value,
                    "details": {"missing_key": str(exc)} if self.debug else {}
                }
            }
        )
    
    def _handle_file_not_found_error(self, exc: FileNotFoundError) -> JSONResponse:
        """处理文件未找到错误"""
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "error": {
                    "code": ErrorCode.FILE_NOT_FOUND.value,
                    "message": "请求的文件不存在",
                    "severity": ErrorSeverity.MEDIUM.value,
                    "details": {"file_path": str(exc)} if self.debug else {}
                }
            }
        )
    
    def _handle_unknown_exception(self, exc: Exception) -> JSONResponse:
        """处理未知异常"""
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": {
                    "code": ErrorCode.UNKNOWN_ERROR.value,
                    "message": "系统发生未知错误，请稍后重试",
                    "severity": ErrorSeverity.HIGH.value,
                    "details": {
                        "exception_type": type(exc).__name__,
                        "exception_message": str(exc)
                    } if self.debug else {}
                }
            }
        )
    
    def _get_status_code_from_error_code(self, error_code: ErrorCode) -> int:
        """根据错误代码获取HTTP状态码"""
        status_code_mapping = {
            # 客户端错误 (400-499)
            ErrorCode.INVALID_PARAMETER: 400,
            ErrorCode.MISSING_PARAMETER: 400,
            ErrorCode.DATA_VALIDATION_ERROR: 400,
            ErrorCode.DATA_FORMAT_ERROR: 400,
            ErrorCode.PERMISSION_DENIED: 403,
            ErrorCode.RESOURCE_NOT_FOUND: 404,
            ErrorCode.DATA_NOT_FOUND: 404,
            ErrorCode.FILE_NOT_FOUND: 404,
            ErrorCode.API_RATE_LIMIT: 429,
            
            # 服务器错误 (500-599)
            ErrorCode.UNKNOWN_ERROR: 500,
            ErrorCode.PREDICTION_FAILED: 500,
            ErrorCode.MODEL_NOT_READY: 503,
            ErrorCode.MODEL_LOAD_ERROR: 500,
            ErrorCode.DATABASE_CONNECTION_ERROR: 500,
            ErrorCode.DATABASE_QUERY_ERROR: 500,
            ErrorCode.DATABASE_TRANSACTION_ERROR: 500,
            ErrorCode.DATABASE_INTEGRITY_ERROR: 500,
            ErrorCode.NETWORK_CONNECTION_ERROR: 502,
            ErrorCode.EXTERNAL_SERVICE_ERROR: 502,
            ErrorCode.FILE_PERMISSION_ERROR: 500,
            ErrorCode.DISK_SPACE_ERROR: 507,
            ErrorCode.CACHE_CONNECTION_ERROR: 500,
            ErrorCode.CONFIG_LOAD_ERROR: 500,
            ErrorCode.CONFIG_VALIDATION_ERROR: 500,
            ErrorCode.CONFIG_MISSING_ERROR: 500,
            
            # 超时错误
            ErrorCode.OPERATION_TIMEOUT: 408,
            ErrorCode.NETWORK_TIMEOUT: 408,
            ErrorCode.PREDICTION_TIMEOUT: 408,
        }
        
        return status_code_mapping.get(error_code, 500)
    
    def _log_exception(self, request: Request, exc: Exception):
        """记录异常信息"""
        try:
            # 构建日志信息
            log_data = {
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else "unknown",
                "exception_type": type(exc).__name__,
                "exception_message": str(exc)
            }
            
            # 如果是系统自定义异常，记录更多信息
            if isinstance(exc, LotterySystemException):
                log_data.update({
                    "error_code": exc.code.value,
                    "severity": exc.severity.value,
                    "details": exc.details
                })
                
                # 根据严重程度选择日志级别
                if exc.severity == ErrorSeverity.CRITICAL:
                    logger.critical("Critical system exception", extra=log_data)
                elif exc.severity == ErrorSeverity.HIGH:
                    logger.error("High severity exception", extra=log_data)
                elif exc.severity == ErrorSeverity.MEDIUM:
                    logger.warning("Medium severity exception", extra=log_data)
                else:
                    logger.info("Low severity exception", extra=log_data)
            else:
                # 记录未处理的异常
                logger.error("Unhandled exception", extra=log_data, exc_info=True)
                
        except Exception as log_exc:
            # 如果日志记录失败，至少打印基本信息
            logger.error(f"Failed to log exception: {log_exc}")
            logger.error(f"Original exception: {exc}")


def create_exception_handler_middleware(debug: bool = False):
    """创建异常处理中间件的工厂函数"""
    def middleware_factory(app):
        return ExceptionHandlerMiddleware(app, debug=debug)
    return middleware_factory


# Streamlit异常处理装饰器
def handle_streamlit_exceptions(func):
    """Streamlit异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except LotterySystemException as exc:
            import streamlit as st
            
            # 显示用户友好的错误消息
            if exc.severity == ErrorSeverity.CRITICAL:
                st.error(f"🚨 严重错误: {exc.to_user_message()}")
            elif exc.severity == ErrorSeverity.HIGH:
                st.error(f"❌ 错误: {exc.to_user_message()}")
            elif exc.severity == ErrorSeverity.MEDIUM:
                st.warning(f"⚠️ 警告: {exc.to_user_message()}")
            else:
                st.info(f"ℹ️ 提示: {exc.to_user_message()}")
            
            # 在调试模式下显示详细信息
            if st.session_state.get('debug_mode', False):
                with st.expander("详细错误信息"):
                    st.json(exc.to_dict())
            
            # 记录异常
            logger.error(f"Streamlit exception: {exc}")
            
        except Exception as exc:
            import streamlit as st
            
            # 处理未知异常
            st.error("🚨 系统发生未知错误，请稍后重试")
            
            # 在调试模式下显示详细信息
            if st.session_state.get('debug_mode', False):
                with st.expander("详细错误信息"):
                    st.exception(exc)
            
            # 记录异常
            logger.error(f"Unhandled Streamlit exception: {exc}", exc_info=True)
    
    return wrapper
