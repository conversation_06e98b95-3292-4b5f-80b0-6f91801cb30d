"""
缓存接口定义

定义缓存系统的统一接口。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union


class CacheInterface(ABC):
    """缓存接口基类"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间(秒)，None表示使用默认TTL
            
        Returns:
            是否设置成功
        """
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """
        清空所有缓存
        
        Returns:
            是否清空成功
        """
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            统计信息字典
        """
        pass
    
    # 可选的批量操作方法
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """
        批量获取缓存值
        
        Args:
            keys: 缓存键列表
            
        Returns:
            键值对字典
        """
        result = {}
        for key in keys:
            value = await self.get(key)
            if value is not None:
                result[key] = value
        return result
    
    async def set_many(
        self, 
        mapping: Dict[str, Any], 
        ttl: Optional[int] = None
    ) -> bool:
        """
        批量设置缓存值
        
        Args:
            mapping: 键值对字典
            ttl: 过期时间(秒)
            
        Returns:
            是否全部设置成功
        """
        success_count = 0
        for key, value in mapping.items():
            if await self.set(key, value, ttl):
                success_count += 1
        return success_count == len(mapping)
    
    async def delete_many(self, keys: List[str]) -> int:
        """
        批量删除缓存
        
        Args:
            keys: 缓存键列表
            
        Returns:
            成功删除的数量
        """
        success_count = 0
        for key in keys:
            if await self.delete(key):
                success_count += 1
        return success_count
    
    # 模式匹配方法
    async def keys(self, pattern: str = "*") -> List[str]:
        """
        获取匹配模式的所有键
        
        Args:
            pattern: 匹配模式
            
        Returns:
            匹配的键列表
        """
        # 默认实现，子类可以重写以提供更高效的实现
        return []
    
    async def delete_pattern(self, pattern: str) -> int:
        """
        删除匹配模式的所有缓存
        
        Args:
            pattern: 匹配模式
            
        Returns:
            删除的数量
        """
        keys = await self.keys(pattern)
        return await self.delete_many(keys)
    
    # TTL相关方法
    async def expire(self, key: str, ttl: int) -> bool:
        """
        设置缓存过期时间
        
        Args:
            key: 缓存键
            ttl: 过期时间(秒)
            
        Returns:
            是否设置成功
        """
        # 默认实现：重新获取值并设置TTL
        value = await self.get(key)
        if value is not None:
            return await self.set(key, value, ttl)
        return False
    
    async def ttl(self, key: str) -> Optional[int]:
        """
        获取缓存剩余过期时间
        
        Args:
            key: 缓存键
            
        Returns:
            剩余时间(秒)，None表示不存在或无过期时间
        """
        # 默认实现，子类应该重写
        return None
    
    # 原子操作方法
    async def increment(self, key: str, delta: Union[int, float] = 1) -> Optional[Union[int, float]]:
        """
        原子递增操作
        
        Args:
            key: 缓存键
            delta: 递增量
            
        Returns:
            递增后的值，如果键不存在则返回None
        """
        # 默认实现，子类可以重写以提供原子操作
        value = await self.get(key)
        if value is not None and isinstance(value, (int, float)):
            new_value = value + delta
            if await self.set(key, new_value):
                return new_value
        return None
    
    async def decrement(self, key: str, delta: Union[int, float] = 1) -> Optional[Union[int, float]]:
        """
        原子递减操作
        
        Args:
            key: 缓存键
            delta: 递减量
            
        Returns:
            递减后的值，如果键不存在则返回None
        """
        return await self.increment(key, -delta)
    
    # 生命周期方法
    async def connect(self) -> bool:
        """
        连接到缓存后端
        
        Returns:
            是否连接成功
        """
        return True
    
    async def disconnect(self) -> bool:
        """
        断开缓存后端连接
        
        Returns:
            是否断开成功
        """
        return True
    
    async def ping(self) -> bool:
        """
        检查缓存后端连接状态
        
        Returns:
            是否连接正常
        """
        return True
    
    # 属性方法
    @property
    def name(self) -> str:
        """缓存后端名称"""
        return self.__class__.__name__
    
    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return True
