"""
缓存装饰器

提供方便的缓存装饰器功能。
"""

import asyncio
import functools
import hashlib
import json
from typing import Any, Callable, Optional

from .manager import CacheManager


# 全局缓存管理器实例
_cache_manager: Optional[CacheManager] = None


def set_cache_manager(manager: CacheManager):
    """设置全局缓存管理器"""
    global _cache_manager
    _cache_manager = manager


def get_cache_manager() -> Optional[CacheManager]:
    """获取全局缓存管理器"""
    return _cache_manager


def _generate_cache_key(func_name: str, args: tuple, kwargs: dict, key_prefix: str = "") -> str:
    """生成缓存键"""
    # 创建参数的哈希值
    args_str = json.dumps(args, sort_keys=True, default=str)
    kwargs_str = json.dumps(kwargs, sort_keys=True, default=str)
    
    combined = f"{func_name}:{args_str}:{kwargs_str}"
    key_hash = hashlib.md5(combined.encode()).hexdigest()
    
    if key_prefix:
        return f"{key_prefix}:{key_hash}"
    return f"func:{key_hash}"


def cached(
    ttl: Optional[int] = None,
    key_prefix: str = "",
    key_func: Optional[Callable] = None
):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存过期时间(秒)
        key_prefix: 缓存键前缀
        key_func: 自定义键生成函数
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            manager = get_cache_manager()
            if not manager:
                # 如果没有缓存管理器，直接执行函数
                return await func(*args, **kwargs)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = _generate_cache_key(func.__name__, args, kwargs, key_prefix)
            
            # 尝试从缓存获取
            cached_result = await manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await manager.set(cache_key, result, ttl)
            
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            manager = get_cache_manager()
            if not manager:
                # 如果没有缓存管理器，直接执行函数
                return func(*args, **kwargs)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = _generate_cache_key(func.__name__, args, kwargs, key_prefix)
            
            # 在同步函数中使用异步缓存
            loop = None
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 如果没有事件循环，直接执行函数
                return func(*args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = loop.run_until_complete(manager.get(cache_key))
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            loop.run_until_complete(manager.set(cache_key, result, ttl))
            
            return result
        
        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def cache_result(
    ttl: Optional[int] = None,
    key: Optional[str] = None
):
    """
    简单的结果缓存装饰器
    
    Args:
        ttl: 缓存过期时间(秒)
        key: 固定的缓存键
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            manager = get_cache_manager()
            if not manager:
                return await func(*args, **kwargs)
            
            # 使用固定键或生成键
            cache_key = key or f"result:{func.__name__}"
            
            # 尝试从缓存获取
            cached_result = await manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await manager.set(cache_key, result, ttl)
            
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            manager = get_cache_manager()
            if not manager:
                return func(*args, **kwargs)
            
            # 使用固定键或生成键
            cache_key = key or f"result:{func.__name__}"
            
            # 在同步函数中使用异步缓存
            loop = None
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                return func(*args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = loop.run_until_complete(manager.get(cache_key))
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            loop.run_until_complete(manager.set(cache_key, result, ttl))
            
            return result
        
        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 便捷的缓存操作函数
async def cache_get(key: str) -> Any:
    """获取缓存值"""
    manager = get_cache_manager()
    if manager:
        return await manager.get(key)
    return None


async def cache_set(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """设置缓存值"""
    manager = get_cache_manager()
    if manager:
        return await manager.set(key, value, ttl)
    return False


async def cache_delete(key: str) -> bool:
    """删除缓存值"""
    manager = get_cache_manager()
    if manager:
        return await manager.delete(key)
    return False


async def cache_clear() -> bool:
    """清空所有缓存"""
    manager = get_cache_manager()
    if manager:
        return await manager.clear()
    return False
