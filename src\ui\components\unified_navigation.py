"""
统一导航组件

整合NavigationComponent、EnhancedNavigationComponent和SmartNavigationComponent的优势，
提供一致的导航体验。
"""

import streamlit as st
import logging
from typing import Dict, List, Optional, Any
from enum import Enum

logger = logging.getLogger(__name__)


class NavigationStyle(Enum):
    """导航样式枚举"""
    STANDARD = "standard"
    ENHANCED = "enhanced"
    SMART = "smart"


class UnifiedNavigationComponent:
    """统一导航组件"""
    
    def __init__(self, style: NavigationStyle = NavigationStyle.ENHANCED):
        """
        初始化统一导航组件
        
        Args:
            style: 导航样式
        """
        self.style = style
        self.logger = logger
        
        # 页面配置
        self.pages = {
            "数据概览": {
                "icon": "📊",
                "description": "查看数据统计和概览信息",
                "category": "数据分析"
            },
            "预测分析": {
                "icon": "🎯",
                "description": "智能预测和分析功能",
                "category": "核心功能"
            },
            "历史查询": {
                "icon": "🔍",
                "description": "历史数据查询和分析",
                "category": "数据分析"
            },
            "数据管理": {
                "icon": "🔄",
                "description": "数据更新和管理功能",
                "category": "系统管理"
            },
            "模型训练": {
                "icon": "🤖",
                "description": "AI模型训练和优化",
                "category": "核心功能"
            },
            "系统监控": {
                "icon": "📈",
                "description": "系统性能和状态监控",
                "category": "系统管理"
            },
            "用户设置": {
                "icon": "⚙️",
                "description": "个人偏好和系统设置",
                "category": "系统管理"
            },
            "帮助文档": {
                "icon": "📚",
                "description": "使用帮助和文档",
                "category": "帮助支持"
            }
        }
        
        # 初始化session状态
        self._initialize_session_state()
    
    def _initialize_session_state(self):
        """初始化session状态"""
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "数据概览"
        if 'navigation_history' not in st.session_state:
            st.session_state.navigation_history = []
        if 'navigation_style' not in st.session_state:
            st.session_state.navigation_style = self.style.value
    
    def render(self) -> str:
        """
        渲染导航组件
        
        Returns:
            当前选中的页面
        """
        try:
            if self.style == NavigationStyle.STANDARD:
                return self._render_standard_navigation()
            elif self.style == NavigationStyle.ENHANCED:
                return self._render_enhanced_navigation()
            elif self.style == NavigationStyle.SMART:
                return self._render_smart_navigation()
            else:
                return self._render_enhanced_navigation()  # 默认使用增强版
                
        except Exception as e:
            self.logger.error(f"导航渲染失败: {e}")
            return self._render_fallback_navigation()
    
    def _render_standard_navigation(self) -> str:
        """渲染标准导航"""
        with st.sidebar:
            st.markdown("### 🧭 导航菜单")
            
            # 简单的页面选择
            page_names = list(self.pages.keys())
            current_index = page_names.index(st.session_state.current_page) if st.session_state.current_page in page_names else 0
            
            selected_page = st.selectbox(
                "选择页面",
                page_names,
                index=current_index,
                format_func=lambda x: f"{self.pages[x]['icon']} {x}"
            )
            
            if selected_page != st.session_state.current_page:
                self._update_navigation_history(selected_page)
                st.session_state.current_page = selected_page
            
            return selected_page
    
    def _render_enhanced_navigation(self) -> str:
        """渲染增强导航"""
        with st.sidebar:
            st.markdown("### 🧭 智能导航")
            
            # 按分类组织页面
            categories = {}
            for page, config in self.pages.items():
                category = config.get('category', '其他')
                if category not in categories:
                    categories[category] = []
                categories[category].append(page)
            
            selected_page = st.session_state.current_page
            
            # 渲染分类导航
            for category, pages in categories.items():
                with st.expander(f"📁 {category}", expanded=(category in ["核心功能", "数据分析"])):
                    for page in pages:
                        config = self.pages[page]
                        
                        # 创建按钮样式
                        is_current = (page == st.session_state.current_page)
                        button_style = "🔹" if is_current else "▫️"
                        
                        if st.button(
                            f"{button_style} {config['icon']} {page}",
                            key=f"nav_{page}",
                            help=config['description'],
                            use_container_width=True
                        ):
                            if page != st.session_state.current_page:
                                self._update_navigation_history(page)
                                st.session_state.current_page = page
                                selected_page = page
                                st.experimental_rerun()
            
            # 显示导航历史
            self._render_navigation_history()
            
            return selected_page
    
    def _render_smart_navigation(self) -> str:
        """渲染智能导航"""
        with st.sidebar:
            st.markdown("### 🧭 智能导航")
            
            # 智能推荐
            recommended_pages = self._get_recommended_pages()
            if recommended_pages:
                st.markdown("#### 🌟 推荐页面")
                for page in recommended_pages[:3]:
                    config = self.pages[page]
                    if st.button(
                        f"⭐ {config['icon']} {page}",
                        key=f"rec_{page}",
                        help=f"推荐: {config['description']}",
                        use_container_width=True
                    ):
                        self._update_navigation_history(page)
                        st.session_state.current_page = page
                        st.experimental_rerun()
            
            st.markdown("---")
            
            # 快速访问
            st.markdown("#### ⚡ 快速访问")
            quick_access = ["数据概览", "预测分析", "历史查询"]
            
            cols = st.columns(len(quick_access))
            for i, page in enumerate(quick_access):
                with cols[i]:
                    config = self.pages[page]
                    if st.button(
                        config['icon'],
                        key=f"quick_{page}",
                        help=page,
                        use_container_width=True
                    ):
                        self._update_navigation_history(page)
                        st.session_state.current_page = page
                        st.experimental_rerun()
            
            st.markdown("---")
            
            # 完整页面列表
            st.markdown("#### 📋 所有页面")
            for page, config in self.pages.items():
                is_current = (page == st.session_state.current_page)
                button_style = "🔹" if is_current else "▫️"
                
                if st.button(
                    f"{button_style} {config['icon']} {page}",
                    key=f"all_{page}",
                    help=config['description'],
                    use_container_width=True
                ):
                    if page != st.session_state.current_page:
                        self._update_navigation_history(page)
                        st.session_state.current_page = page
                        st.experimental_rerun()
            
            # 显示导航历史
            self._render_navigation_history()
            
            return st.session_state.current_page
    
    def _render_fallback_navigation(self) -> str:
        """渲染回退导航"""
        with st.sidebar:
            st.markdown("### 🧭 导航菜单")
            st.warning("⚠️ 导航组件加载失败，使用简化版本")
            
            # 最简单的导航
            pages = ["数据概览", "预测分析", "数据管理", "系统设置"]
            selected = st.radio("选择页面", pages)
            
            if selected != st.session_state.current_page:
                st.session_state.current_page = selected
            
            return selected
    
    def _update_navigation_history(self, new_page: str):
        """更新导航历史"""
        try:
            history = st.session_state.navigation_history
            
            # 避免重复记录相同页面
            if not history or history[-1] != new_page:
                history.append(new_page)
                
                # 限制历史记录长度
                if len(history) > 10:
                    history.pop(0)
                
                st.session_state.navigation_history = history
                
        except Exception as e:
            self.logger.warning(f"更新导航历史失败: {e}")
    
    def _render_navigation_history(self):
        """渲染导航历史"""
        try:
            history = st.session_state.navigation_history
            if len(history) > 1:
                st.markdown("#### 📚 最近访问")
                
                # 显示最近的3个页面（除了当前页面）
                recent_pages = [p for p in reversed(history[-4:]) if p != st.session_state.current_page]
                
                for page in recent_pages[:3]:
                    if page in self.pages:
                        config = self.pages[page]
                        if st.button(
                            f"🕒 {config['icon']} {page}",
                            key=f"hist_{page}",
                            help=f"返回到: {page}",
                            use_container_width=True
                        ):
                            self._update_navigation_history(page)
                            st.session_state.current_page = page
                            st.experimental_rerun()
                            
        except Exception as e:
            self.logger.warning(f"渲染导航历史失败: {e}")
    
    def _get_recommended_pages(self) -> List[str]:
        """获取推荐页面"""
        try:
            current_page = st.session_state.current_page
            history = st.session_state.navigation_history
            
            # 基于当前页面的推荐逻辑
            recommendations = []
            
            if current_page == "数据概览":
                recommendations = ["预测分析", "历史查询", "数据管理"]
            elif current_page == "预测分析":
                recommendations = ["模型训练", "系统监控", "数据概览"]
            elif current_page == "历史查询":
                recommendations = ["数据概览", "预测分析", "数据管理"]
            elif current_page == "数据管理":
                recommendations = ["数据概览", "系统监控", "用户设置"]
            else:
                # 基于访问历史的推荐
                if history:
                    # 获取最常访问的页面
                    page_counts = {}
                    for page in history:
                        page_counts[page] = page_counts.get(page, 0) + 1
                    
                    recommendations = sorted(page_counts.keys(), key=lambda x: page_counts[x], reverse=True)
                else:
                    recommendations = ["数据概览", "预测分析", "历史查询"]
            
            # 过滤掉当前页面
            return [p for p in recommendations if p != current_page and p in self.pages]
            
        except Exception as e:
            self.logger.warning(f"获取推荐页面失败: {e}")
            return ["数据概览", "预测分析", "历史查询"]
    
    def switch_style(self, new_style: NavigationStyle):
        """切换导航样式"""
        self.style = new_style
        st.session_state.navigation_style = new_style.value
        self.logger.info(f"导航样式切换为: {new_style.value}")
    
    def get_current_page(self) -> str:
        """获取当前页面"""
        return st.session_state.current_page
    
    def get_page_config(self, page: str) -> Dict[str, Any]:
        """获取页面配置"""
        return self.pages.get(page, {})
    
    def add_custom_page(self, name: str, icon: str, description: str, category: str = "自定义"):
        """添加自定义页面"""
        self.pages[name] = {
            "icon": icon,
            "description": description,
            "category": category
        }
        self.logger.info(f"添加自定义页面: {name}")


# 便捷函数
def create_unified_navigation(style: str = "enhanced") -> UnifiedNavigationComponent:
    """
    创建统一导航组件
    
    Args:
        style: 导航样式 ("standard", "enhanced", "smart")
        
    Returns:
        统一导航组件实例
    """
    try:
        nav_style = NavigationStyle(style)
    except ValueError:
        nav_style = NavigationStyle.ENHANCED
        
    return UnifiedNavigationComponent(nav_style)
