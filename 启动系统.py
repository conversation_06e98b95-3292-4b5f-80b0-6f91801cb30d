#!/usr/bin/env python3
"""
福彩3D预测系统 - 简化一键启动脚本
"""

import os
import platform
import subprocess
import sys
import time
from pathlib import Path


def print_banner():
    """打印启动横幅"""
    print("\n" + "=" * 50)
    print("    福彩3D预测分析工具 - 一键启动")
    print("=" * 50)
    print("\n🚀 正在启动系统...\n")


def check_files():
    """检查必要文件"""
    print("🔍 检查必要文件...")
    
    # 检查虚拟环境
    if platform.system() == "Windows":
        python_exe = Path("venv/Scripts/python.exe")
    else:
        python_exe = Path("venv/bin/python")
    
    if not python_exe.exists():
        print("❌ 错误：虚拟环境不存在！")
        print("请先创建虚拟环境：python -m venv venv")
        return False
    
    # 检查启动脚本
    if not Path("start_production_api.py").exists():
        print("❌ 错误：API启动脚本不存在！")
        return False
    
    if not Path("start_streamlit.py").exists():
        print("❌ 错误：Streamlit启动脚本不存在！")
        return False
    
    # 检查数据库
    if not Path("data/lottery.db").exists():
        print("❌ 错误：数据库文件不存在！")
        return False
    
    print("✅ 文件检查通过")
    return True


def start_services():
    """启动服务"""
    print("\n📋 启动步骤：")
    print("1. 启动API服务（后台运行）")
    print("2. 等待5秒确保API服务启动完成")
    print("3. 启动Streamlit界面\n")
    
    # 第一步：启动API服务
    print("🔧 步骤1: 启动API服务...")
    if platform.system() == "Windows":
        # Windows下启动API服务
        subprocess.Popen([
            "cmd", "/c", "start", "cmd", "/k",
            "venv\\Scripts\\activate && python start_production_api.py"
        ], text=True, encoding='utf-8')
    else:
        # Linux/macOS下启动API服务
        subprocess.Popen([
            "bash", "-c",
            "source venv/bin/activate && python start_production_api.py"
        ], text=True, encoding='utf-8')
    
    print("✅ API服务启动命令已执行")
    
    # 第二步：等待
    print("⏳ 步骤2: 等待API服务启动（5秒）...")
    time.sleep(5)
    
    # 第三步：显示访问信息
    print("\n✅ 系统启动完成！\n")
    print("📱 访问地址：")
    print("   - Streamlit界面: http://127.0.0.1:8501")
    print("   - API服务: http://127.0.0.1:8888")
    print("   - API文档: http://127.0.0.1:8888/docs\n")
    print("💡 提示：")
    print("   - 关闭此窗口将停止Streamlit界面")
    print("   - API服务在独立窗口中运行")
    print("   - 如需停止所有服务，请关闭所有相关窗口\n")
    
    # 第三步：启动Streamlit
    print("🎨 步骤3: 启动Streamlit界面...")
    
    try:
        if platform.system() == "Windows":
            # Windows下启动Streamlit
            subprocess.run([
                "cmd", "/c", 
                venv\Scripts\activate && python -m streamlit run src/ui/main.py
            ], shell=True)
        else:
            # Linux/macOS下启动Streamlit
            subprocess.run([
                "bash", "-c",
                source venv/bin/activate && python -m streamlit run src/ui/main.py
            ])
    except KeyboardInterrupt:
        print("\n👋 Streamlit界面已停止")
    except Exception as e:
        print(f"❌ Streamlit启动失败：{e}")


def main():
    """主函数"""
    try:
        print_banner()
        
        if not check_files():
            input("\n按回车键退出...")
            return
        
        start_services()
        
        print("\n👋 系统已停止")
        print("💡 API服务可能仍在后台运行，请手动关闭API服务窗口")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，系统退出")
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误：{e}")
    finally:
        input("\n按回车键退出...")


if __name__ == "__main__":
    main()
