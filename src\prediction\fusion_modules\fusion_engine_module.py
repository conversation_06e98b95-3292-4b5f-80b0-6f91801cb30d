"""
融合引擎模块

负责处理多模型融合和权重计算功能。
"""

import time
import json
from typing import Any, Dict, List, Optional, Tuple

from .base_module import BaseFusionModule


class FusionEngineModule(BaseFusionModule):
    """融合引擎模块"""
    
    def __init__(self, context):
        super().__init__(context, "FusionEngine")
        
    def _do_initialize(self):
        """初始化融合引擎模块"""
        if not self.context.fusion_system:
            raise ValueError("融合系统未设置")
        self.logger.info("融合引擎模块初始化完成")
    
    def generate_fusion_prediction(self, 
                                 trend_result: Dict[str, Any],
                                 pattern_result: Dict[str, Any],
                                 lstm_result: Dict[str, Any],
                                 **kwargs) -> Dict[str, Any]:
        """
        生成融合预测
        
        Args:
            trend_result: 趋势预测结果
            pattern_result: 模式预测结果
            lstm_result: LSTM预测结果
            **kwargs: 其他参数
            
        Returns:
            融合预测结果
        """
        if not self.is_ready():
            raise RuntimeError("融合引擎模块未就绪")
            
        start_time = time.time()
        
        try:
            # 计算动态权重
            weights = self.calculate_dynamic_weights()
            
            # 收集所有预测结果
            all_predictions = {
                'trend': trend_result,
                'pattern': pattern_result,
                'lstm': lstm_result
            }
            
            # 执行融合算法
            fusion_result = self._execute_fusion_algorithm(all_predictions, weights)
            
            # 应用稳定性约束
            stable_result = self._apply_stability_constraint(fusion_result)
            
            # 应用周期敏感性
            final_result = self._apply_period_sensitivity(stable_result)
            
            # 保存当前预测
            self._save_current_prediction(final_result)
            
            result = {
                "fusion_prediction": final_result,
                "weights": weights,
                "input_predictions": all_predictions,
                "timestamp": time.time()
            }
            
            duration = time.time() - start_time
            self._log_performance("融合预测", duration)
            
            return result
            
        except Exception as e:
            self._handle_error("融合预测", e)
            raise
    
    def calculate_dynamic_weights(self) -> Dict[str, float]:
        """
        计算动态权重
        
        Returns:
            各模型的权重
        """
        try:
            # 获取历史性能
            performance = self._get_historical_performance()
            
            # 计算基础权重
            base_weights = {
                'trend': 0.3,
                'pattern': 0.3,
                'lstm': 0.2,
                'fusion': 0.2
            }
            
            # 基于性能调整权重
            if performance:
                for model, perf in performance.items():
                    if model in base_weights and perf > 0:
                        # 性能好的模型增加权重
                        base_weights[model] *= (1 + perf * 0.5)
            
            # 归一化权重
            total_weight = sum(base_weights.values())
            normalized_weights = {k: v / total_weight for k, v in base_weights.items()}
            
            return normalized_weights
            
        except Exception as e:
            self.logger.warning(f"计算动态权重失败: {e}")
            return {'trend': 0.25, 'pattern': 0.25, 'lstm': 0.25, 'fusion': 0.25}
    
    def _execute_fusion_algorithm(self, predictions: Dict[str, Any], weights: Dict[str, float]) -> Dict[str, Any]:
        """
        执行融合算法
        
        Args:
            predictions: 各模型预测结果
            weights: 权重
            
        Returns:
            融合结果
        """
        try:
            # 提取候选号码
            all_candidates = []
            
            for model_name, result in predictions.items():
                if result and 'predictions' in result:
                    model_predictions = result['predictions']
                    if isinstance(model_predictions, list):
                        all_candidates.extend(model_predictions)
                    elif isinstance(model_predictions, dict) and 'candidates' in model_predictions:
                        all_candidates.extend(model_predictions['candidates'])
            
            # 计算候选号码的综合得分
            candidate_scores = self._calculate_candidate_scores(all_candidates, predictions, weights)
            
            # 选择最佳候选
            best_candidates = sorted(candidate_scores.items(), key=lambda x: x[1], reverse=True)[:10]
            
            fusion_result = {
                "best_prediction": best_candidates[0][0] if best_candidates else "000",
                "candidates": [item[0] for item in best_candidates],
                "scores": dict(best_candidates),
                "confidence": self._calculate_fusion_confidence(candidate_scores)
            }
            
            return fusion_result
            
        except Exception as e:
            self.logger.warning(f"执行融合算法失败: {e}")
            return {"best_prediction": "000", "candidates": ["000"], "confidence": 0.5}
    
    def _calculate_candidate_scores(self, candidates: List[str], predictions: Dict[str, Any], weights: Dict[str, float]) -> Dict[str, float]:
        """计算候选号码得分"""
        try:
            scores = {}
            
            for candidate in candidates:
                if not candidate or len(str(candidate)) != 3:
                    continue
                    
                score = 0.0
                
                # 基于各模型的支持度计算得分
                for model_name, weight in weights.items():
                    if model_name in predictions and predictions[model_name]:
                        model_support = self._get_model_support(candidate, predictions[model_name])
                        score += weight * model_support
                
                scores[str(candidate)] = score
            
            return scores
            
        except Exception as e:
            self.logger.warning(f"计算候选得分失败: {e}")
            return {}
    
    def _get_model_support(self, candidate: str, model_result: Dict[str, Any]) -> float:
        """获取模型对候选的支持度"""
        try:
            if not model_result or 'predictions' not in model_result:
                return 0.0
            
            predictions = model_result['predictions']
            
            # 检查候选是否在预测结果中
            if isinstance(predictions, list) and candidate in predictions:
                return 1.0
            elif isinstance(predictions, dict):
                if 'candidates' in predictions and candidate in predictions['candidates']:
                    return 1.0
                elif candidate == predictions.get('best_prediction'):
                    return 1.0
            
            return 0.0
            
        except:
            return 0.0
    
    def _calculate_fusion_confidence(self, candidate_scores: Dict[str, float]) -> float:
        """计算融合置信度"""
        try:
            if not candidate_scores:
                return 0.5
            
            scores = list(candidate_scores.values())
            if not scores:
                return 0.5
            
            # 基于最高得分和得分分布计算置信度
            max_score = max(scores)
            avg_score = sum(scores) / len(scores)
            
            # 得分差异越大，置信度越高
            confidence = min(max_score, 1.0)
            if len(scores) > 1:
                score_std = (sum((s - avg_score) ** 2 for s in scores) / len(scores)) ** 0.5
                confidence += score_std * 0.1
            
            return min(max(confidence, 0.0), 1.0)
            
        except:
            return 0.5
    
    def _apply_stability_constraint(self, fusion_result: Dict[str, Any]) -> Dict[str, Any]:
        """应用稳定性约束"""
        try:
            # 加载上一次预测
            previous_prediction = self._load_previous_prediction()
            
            if not previous_prediction:
                return fusion_result
            
            # 计算稳定性损失
            stability_loss = self.calculate_stability_loss(fusion_result, previous_prediction)
            
            # 如果稳定性损失过大，调整预测结果
            if stability_loss > 0.8:
                # 降低置信度
                fusion_result['confidence'] *= 0.8
                self.logger.info(f"应用稳定性约束，稳定性损失: {stability_loss:.3f}")
            
            return fusion_result
            
        except Exception as e:
            self.logger.warning(f"应用稳定性约束失败: {e}")
            return fusion_result
    
    def _apply_period_sensitivity(self, fusion_result: Dict[str, Any]) -> Dict[str, Any]:
        """应用周期敏感性"""
        try:
            # 计算周期敏感性
            sensitivity = self._calculate_period_sensitivity()
            
            # 基于敏感性调整置信度
            if sensitivity > 0.7:
                fusion_result['confidence'] *= 1.1  # 高敏感性时提高置信度
            elif sensitivity < 0.3:
                fusion_result['confidence'] *= 0.9  # 低敏感性时降低置信度
            
            # 确保置信度在合理范围内
            fusion_result['confidence'] = min(max(fusion_result['confidence'], 0.0), 1.0)
            
            return fusion_result
            
        except Exception as e:
            self.logger.warning(f"应用周期敏感性失败: {e}")
            return fusion_result
    
    def calculate_stability_loss(self, current_result: Dict[str, Any], previous_result: Dict[str, Any]) -> float:
        """计算稳定性损失"""
        try:
            if not current_result or not previous_result:
                return 0.0
            
            current_pred = current_result.get('best_prediction', '')
            previous_pred = previous_result.get('best_prediction', '')
            
            if not current_pred or not previous_pred:
                return 0.0
            
            # 简单的差异计算
            if current_pred == previous_pred:
                return 0.0
            else:
                # 计算数字差异
                diff_count = sum(1 for a, b in zip(str(current_pred), str(previous_pred)) if a != b)
                return diff_count / 3.0  # 归一化到0-1
            
        except:
            return 0.0
    
    def _calculate_period_sensitivity(self) -> float:
        """计算周期敏感性"""
        try:
            # 简化的周期敏感性计算
            # 实际实现中可能基于历史数据的周期性分析
            return 0.5
        except:
            return 0.5
    
    def _get_historical_performance(self) -> Dict[str, float]:
        """获取历史性能"""
        try:
            # 简化的性能数据
            return {
                'trend': 0.6,
                'pattern': 0.5,
                'lstm': 0.7,
                'fusion': 0.65
            }
        except:
            return {}
    
    def _load_previous_prediction(self) -> Optional[Dict[str, Any]]:
        """加载上一次预测"""
        try:
            cache_file = self.context.get_cache_path("last_prediction.json")
            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except:
            return None
    
    def _save_current_prediction(self, prediction: Dict[str, Any]):
        """保存当前预测"""
        try:
            cache_file = self.context.get_cache_path("last_prediction.json")
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(prediction, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.warning(f"保存预测失败: {e}")
