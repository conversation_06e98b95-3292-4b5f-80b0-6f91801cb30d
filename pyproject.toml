[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "lottery-3d-predictor"
version = "2025.1.0"
description = "福彩3D智能预测分析工具"
authors = [{name = "福彩3D预测工具开发团队", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.11,<3.12"
license = {text = "MIT"}
keywords = ["福彩3D", "预测", "数据分析", "机器学习"]

classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Office/Business :: Financial",
]

dependencies = [
    # Web框架 (Python 3.11兼容版本)
    "streamlit>=1.28.0,<1.32.0",
    "fastapi[websockets]>=0.104.0,<0.111.0",
    "uvicorn[standard]>=0.24.0,<0.28.0",

    # WebSocket支持
    "websockets>=11.0.3,<12.0.0",
    "redis>=5.0.1,<6.0.0",
    "aioredis>=2.0.1,<3.0.0",
    "ujson>=5.8.0,<6.0.0",
    "orjson>=3.9.0,<4.0.0",
    
    # 数据处理 (Python 3.11兼容版本)
    "polars>=0.19.0,<0.21.0",
    "pandas>=2.1.0,<2.3.0",
    "numpy>=1.24.0,<2.0.0",
    
    # 机器学习 (Python 3.11兼容版本)
    "torch>=2.1.0,<2.3.0",
    "scikit-learn>=1.3.0,<1.5.0",
    
    # 数据采集
    "httpx>=0.25.0,<0.27.0",
    "beautifulsoup4>=4.12.0,<4.13.0",
    "apscheduler>=3.10.0,<3.11.0",
    
    # 数据验证
    "pydantic>=2.4.0,<2.6.0",
    
    # 可视化
    "plotly>=5.17.0,<5.19.0",
    "altair>=5.1.0,<5.3.0",
    
    # 工具库
    "loguru>=0.7.0,<0.8.0",
    "typer>=0.9.0,<0.10.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.1.0",
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pre-commit>=3.5.0",
    "mypy>=1.6.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
]

[project.urls]
Homepage = "https://github.com/your-org/lottery-3d-predictor"
Repository = "https://github.com/your-org/lottery-3d-predictor"
Documentation = "https://lottery-3d-predictor.readthedocs.io"
"Bug Tracker" = "https://github.com/your-org/lottery-3d-predictor/issues"

[project.scripts]
"3d-predictor" = "src.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.json", "*.yaml", "*.yml"]

[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "F", "I", "N", "W", "UP"]
ignore = ["E501"]

[tool.ruff.isort]
known-first-party = ["src"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=src --cov-report=html --cov-report=term-missing"

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
