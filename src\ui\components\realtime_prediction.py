#!/usr/bin/env python3
"""
实时预测结果展示组件
显示实时预测进度和结果
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

def display_realtime_prediction_dashboard():
    """显示实时预测仪表盘"""
    st.subheader("🔄 实时预测监控")
    
    # 创建三列布局
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col1:
        display_prediction_status()
    
    with col2:
        display_prediction_progress()
    
    with col3:
        display_prediction_stats()
    
    # 显示预测结果
    display_prediction_results()
    
    # 显示预测历史
    display_prediction_history()

def display_prediction_status():
    """显示预测状态"""
    st.markdown("### 📊 预测状态")
    
    # 获取最新的预测消息
    prediction_messages = st.session_state.get('prediction_messages', [])
    prediction_progress = st.session_state.get('prediction_progress', {})
    prediction_results = st.session_state.get('prediction_results', [])
    prediction_errors = st.session_state.get('prediction_errors', [])
    
    # 状态指示器
    if prediction_errors:
        st.error("❌ 预测失败")
        latest_error = prediction_errors[-1]
        st.write(f"错误: {latest_error['data'].get('data', {}).get('error_message', '未知错误')}")
    elif prediction_results:
        st.success("✅ 预测完成")
        latest_result = prediction_results[-1]
        completion_time = latest_result.get('timestamp', time.time())
        st.write(f"完成时间: {datetime.fromtimestamp(completion_time).strftime('%H:%M:%S')}")
    elif prediction_progress:
        st.info("🔄 预测进行中")
        # 显示最新进度
        latest_progress = list(prediction_progress.values())[-1] if prediction_progress else None
        if latest_progress:
            progress_data = latest_progress.get('data', {})
            stage = progress_data.get('stage', '未知阶段')
            st.write(f"当前阶段: {stage}")
    elif prediction_messages:
        st.info("🚀 预测已开始")
    else:
        st.info("⏳ 等待预测")
    
    # 统计信息
    st.metric("总预测次数", len(prediction_results))
    st.metric("错误次数", len(prediction_errors))

def display_prediction_progress():
    """显示预测进度"""
    st.markdown("### 📈 预测进度")
    
    prediction_progress = st.session_state.get('prediction_progress', {})
    
    if not prediction_progress:
        st.info("暂无进度信息")
        return
    
    # 获取最新的进度信息
    latest_progress = list(prediction_progress.values())[-1]
    progress_data = latest_progress.get('data', {})
    
    # 进度条
    progress = progress_data.get('progress', 0.0)
    stage = progress_data.get('stage', '未知阶段')
    
    st.progress(progress)
    st.write(f"**{stage}** ({progress:.1%})")
    
    # 详细信息
    details = progress_data.get('details', {})
    if details:
        with st.expander("详细信息"):
            st.json(details)
    
    # 进度历史图表
    if len(prediction_progress) > 1:
        progress_history = []
        time_history = []
        stage_history = []
        
        for progress_info in prediction_progress.values():
            data = progress_info.get('data', {})
            progress_history.append(data.get('progress', 0.0))
            time_history.append(data.get('timestamp', time.time()))
            stage_history.append(data.get('stage', '未知'))
        
        # 创建进度图表
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=[datetime.fromtimestamp(t) for t in time_history],
            y=[p * 100 for p in progress_history],
            mode='lines+markers',
            name='预测进度',
            text=stage_history,
            hovertemplate='<b>%{text}</b><br>进度: %{y:.1f}%<br>时间: %{x}<extra></extra>'
        ))
        
        fig.update_layout(
            title="预测进度历史",
            xaxis_title="时间",
            yaxis_title="进度 (%)",
            height=300,
            showlegend=False
        )
        
        st.plotly_chart(fig, use_container_width=True)

def display_prediction_stats():
    """显示预测统计"""
    st.markdown("### 📊 统计信息")
    
    prediction_results = st.session_state.get('prediction_results', [])
    
    if not prediction_results:
        st.info("暂无统计数据")
        return
    
    # 计算统计信息
    total_predictions = len(prediction_results)
    
    # 响应时间统计
    response_times = []
    cache_hits = 0
    
    for result in prediction_results:
        data = result.get('data', {})
        result_data = data.get('data', {})
        
        if 'response_time' in result_data:
            response_times.append(result_data['response_time'])
        
        if result_data.get('cache_hit', False):
            cache_hits += 1
    
    # 显示指标
    if response_times:
        avg_response_time = sum(response_times) / len(response_times)
        st.metric("平均响应时间", f"{avg_response_time:.3f}秒")
    
    cache_hit_rate = (cache_hits / total_predictions * 100) if total_predictions > 0 else 0
    st.metric("缓存命中率", f"{cache_hit_rate:.1f}%")
    
    # 响应时间分布图
    if len(response_times) > 1:
        fig = px.histogram(
            x=response_times,
            nbins=10,
            title="响应时间分布",
            labels={'x': '响应时间 (秒)', 'y': '频次'}
        )
        fig.update_layout(height=200, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)

def display_prediction_results():
    """显示预测结果"""
    st.markdown("### 🎯 最新预测结果")
    
    prediction_results = st.session_state.get('prediction_results', [])
    
    if not prediction_results:
        st.info("暂无预测结果")
        return
    
    # 显示最新结果
    latest_result = prediction_results[-1]
    result_data = latest_result['data'].get('data', {})
    
    # 预测结果展示
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### 🎲 预测号码")
        best_prediction = result_data.get('result', {}).get('predictions', ['000'])[0] if result_data.get('result', {}).get('predictions') else '000'
        st.markdown(f"<h2 style='text-align: center; color: #ff6b6b;'>{best_prediction}</h2>", unsafe_allow_html=True)
    
    with col2:
        st.markdown("#### 📊 置信度")
        confidence = result_data.get('result', {}).get('confidence_scores', {})
        if isinstance(confidence, dict) and confidence:
            avg_confidence = sum(confidence.values()) / len(confidence)
        elif isinstance(confidence, list) and confidence:
            avg_confidence = sum(confidence) / len(confidence)
        else:
            avg_confidence = 0.5
        
        st.metric("平均置信度", f"{avg_confidence:.3f}")
        
        # 置信度进度条
        st.progress(avg_confidence)
    
    with col3:
        st.markdown("#### ⏱️ 性能指标")
        response_time = result_data.get('response_time', 0)
        st.metric("响应时间", f"{response_time:.3f}秒")
        
        cache_hit = result_data.get('cache_hit', False)
        st.metric("缓存命中", "是" if cache_hit else "否")
    
    # 候选号码列表
    st.markdown("#### 📋 候选号码")
    predictions = result_data.get('result', {}).get('predictions', [])
    confidence_scores = result_data.get('result', {}).get('confidence_scores', {})
    
    if predictions:
        candidates_data = []
        for i, pred in enumerate(predictions[:10]):  # 显示前10个
            conf = confidence_scores.get(str(i), 0.5) if isinstance(confidence_scores, dict) else (confidence_scores[i] if i < len(confidence_scores) else 0.5)
            candidates_data.append({
                '排名': i + 1,
                '号码': pred,
                '置信度': f"{conf:.3f}",
                '置信度条': conf
            })
        
        # 创建候选号码表格
        import pandas as pd
        df = pd.DataFrame(candidates_data)
        
        # 使用st.dataframe显示带进度条的表格
        st.dataframe(
            df[['排名', '号码', '置信度']],
            column_config={
                '置信度': st.column_config.ProgressColumn(
                    '置信度',
                    help='预测置信度',
                    min_value=0,
                    max_value=1,
                ),
            },
            hide_index=True,
            use_container_width=True
        )

def display_prediction_history():
    """显示预测历史"""
    st.markdown("### 📚 预测历史")
    
    prediction_results = st.session_state.get('prediction_results', [])
    
    if len(prediction_results) < 2:
        st.info("历史记录不足")
        return
    
    # 历史数据处理
    history_data = []
    for i, result in enumerate(prediction_results[-10:]):  # 显示最近10次
        data = result['data'].get('data', {})
        result_data = data.get('result', {})
        
        timestamp = result.get('timestamp', time.time())
        predictions = result_data.get('predictions', ['000'])
        best_prediction = predictions[0] if predictions else '000'
        
        confidence_scores = result_data.get('confidence_scores', {})
        if isinstance(confidence_scores, dict) and confidence_scores:
            avg_confidence = sum(confidence_scores.values()) / len(confidence_scores)
        elif isinstance(confidence_scores, list) and confidence_scores:
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
        else:
            avg_confidence = 0.5
        
        response_time = data.get('response_time', 0)
        cache_hit = data.get('cache_hit', False)
        
        history_data.append({
            '序号': len(prediction_results) - 10 + i + 1,
            '时间': datetime.fromtimestamp(timestamp).strftime('%H:%M:%S'),
            '预测号码': best_prediction,
            '置信度': f"{avg_confidence:.3f}",
            '响应时间': f"{response_time:.3f}s",
            '缓存命中': "✅" if cache_hit else "❌"
        })
    
    if history_data:
        import pandas as pd
        df = pd.DataFrame(history_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    
    # 清理历史数据按钮
    if st.button("🗑️ 清理历史数据"):
        st.session_state.prediction_results = []
        st.session_state.prediction_messages = []
        st.session_state.prediction_progress = {}
        st.session_state.prediction_errors = []
        st.success("历史数据已清理")
        st.rerun()

def display_prediction_errors():
    """显示预测错误"""
    prediction_errors = st.session_state.get('prediction_errors', [])
    
    if not prediction_errors:
        return
    
    st.markdown("### ⚠️ 预测错误")
    
    for i, error in enumerate(prediction_errors[-5:]):  # 显示最近5个错误
        error_data = error['data'].get('data', {})
        timestamp = error.get('timestamp', time.time())
        
        with st.expander(f"错误 {i+1} - {datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')}"):
            st.error(f"错误消息: {error_data.get('error_message', '未知错误')}")
            
            error_details = error_data.get('error_details', {})
            if error_details:
                st.json(error_details)
