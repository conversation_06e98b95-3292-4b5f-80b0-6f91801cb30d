#!/usr/bin/env python3
"""
加载状态组件
创建日期: 2025年7月31日
用途: 提供统一的加载状态管理、进度指示器和加载动画效果
"""

import time
import uuid
from contextlib import contextmanager
from typing import Optional, Union, Any, Callable
import streamlit as st
import streamlit.components.v1 as components


class LoadingManager:
    """统一的加载状态管理器"""
    
    def __init__(self):
        self.loading_id = str(uuid.uuid4())
        self.is_loading = False
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'loading_states' not in st.session_state:
            st.session_state.loading_states = {}
        
        if 'loading_history' not in st.session_state:
            st.session_state.loading_history = []
    
    @contextmanager
    def show_loading(self, message: str = "加载中...", progress: Optional[float] = None):
        """显示加载状态的上下文管理器
        
        Args:
            message: 加载提示信息
            progress: 进度值 (0.0-1.0)，None表示不显示进度条
        """
        loading_container = st.empty()
        
        try:
            self.is_loading = True
            st.session_state.loading_states[self.loading_id] = {
                'message': message,
                'progress': progress,
                'start_time': time.time()
            }
            
            # 显示加载指示器
            with loading_container.container():
                if progress is not None:
                    st.progress(progress, text=message)
                else:
                    self._show_spinner(message)
            
            yield loading_container
            
        finally:
            self.is_loading = False
            if self.loading_id in st.session_state.loading_states:
                end_time = time.time()
                duration = end_time - st.session_state.loading_states[self.loading_id]['start_time']
                
                # 记录加载历史
                st.session_state.loading_history.append({
                    'message': message,
                    'duration': duration,
                    'timestamp': end_time
                })
                
                # 清理状态
                del st.session_state.loading_states[self.loading_id]
            
            loading_container.empty()
    
    def _show_spinner(self, message: str):
        """显示旋转加载指示器"""
        spinner_html = f"""
        <div style="display: flex; align-items: center; justify-content: center; padding: 20px;">
            <div style="margin-right: 10px;">
                <div class="loading-spinner"></div>
            </div>
            <span style="color: #666; font-size: 14px;">{message}</span>
        </div>
        
        <style>
        .loading-spinner {{
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1f77b4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }}
        
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        </style>
        """
        st.markdown(spinner_html, unsafe_allow_html=True)
    
    def show_progress_bar(self, progress: float, message: str = "处理中..."):
        """显示进度条
        
        Args:
            progress: 进度值 (0.0-1.0)
            message: 进度提示信息
        """
        st.progress(progress, text=f"{message} ({progress*100:.1f}%)")
    
    def show_skeleton_loader(self, lines: int = 3, height: int = 20):
        """显示骨架屏加载效果
        
        Args:
            lines: 骨架行数
            height: 每行高度
        """
        skeleton_html = f"""
        <div class="skeleton-container">
            {''.join([f'<div class="skeleton-line" style="height: {height}px; margin-bottom: 10px;"></div>' for _ in range(lines)])}
        </div>
        
        <style>
        .skeleton-container {{
            padding: 20px 0;
        }}
        
        .skeleton-line {{
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }}
        
        @keyframes loading {{
            0% {{ background-position: 200% 0; }}
            100% {{ background-position: -200% 0; }}
        }}
        </style>
        """
        st.markdown(skeleton_html, unsafe_allow_html=True)
    
    def create_loading_placeholder(self, key: Optional[str] = None) -> Any:
        """创建加载占位符容器
        
        Args:
            key: 容器的唯一键
            
        Returns:
            Streamlit容器对象
        """
        return st.empty() if key is None else st.empty()
    
    def show_loading_overlay(self, message: str = "加载中...", show_progress: bool = False):
        """显示全屏加载遮罩
        
        Args:
            message: 加载提示信息
            show_progress: 是否显示进度条
        """
        overlay_html = f"""
        <div id="loading-overlay" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        ">
            <div class="loading-spinner-large"></div>
            <p style="margin-top: 20px; color: #666; font-size: 16px;">{message}</p>
            {'<div class="progress-bar-container"><div class="progress-bar"></div></div>' if show_progress else ''}
        </div>
        
        <style>
        .loading-spinner-large {{
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1f77b4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }}
        
        .progress-bar-container {{
            width: 200px;
            height: 4px;
            background-color: #f3f3f3;
            border-radius: 2px;
            margin-top: 10px;
            overflow: hidden;
        }}
        
        .progress-bar {{
            height: 100%;
            background-color: #1f77b4;
            animation: progress 2s ease-in-out infinite;
        }}
        
        @keyframes progress {{
            0% {{ width: 0%; }}
            50% {{ width: 70%; }}
            100% {{ width: 100%; }}
        }}
        
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        </style>
        
        <script>
        // 自动移除遮罩（防止卡住）
        setTimeout(function() {{
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {{
                overlay.style.display = 'none';
            }}
        }}, 30000); // 30秒后自动移除
        </script>
        """
        components.html(overlay_html, height=0)
    
    def hide_loading_overlay(self):
        """隐藏加载遮罩"""
        hide_script = """
        <script>
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
        </script>
        """
        components.html(hide_script, height=0)
    
    def get_loading_stats(self) -> dict:
        """获取加载统计信息"""
        history = st.session_state.get('loading_history', [])
        if not history:
            return {
                'total_loads': 0,
                'average_duration': 0,
                'last_load_duration': 0
            }
        
        durations = [item['duration'] for item in history]
        return {
            'total_loads': len(history),
            'average_duration': sum(durations) / len(durations),
            'last_load_duration': durations[-1] if durations else 0,
            'max_duration': max(durations),
            'min_duration': min(durations)
        }


# 便捷函数
def show_loading_spinner(message: str = "加载中..."):
    """显示简单的加载旋转器"""
    manager = LoadingManager()
    manager._show_spinner(message)

def show_progress(progress: float, message: str = "处理中..."):
    """显示进度条"""
    st.progress(progress, text=f"{message} ({progress*100:.1f}%)")

def create_loading_context(message: str = "加载中...", progress: Optional[float] = None):
    """创建加载上下文管理器"""
    manager = LoadingManager()
    return manager.show_loading(message, progress)

# 全局加载管理器实例
global_loading_manager = LoadingManager()
