"""
内存缓存实现

基于LRU算法的内存缓存实现。
"""

import asyncio
import time
from collections import OrderedDict
from typing import Any, Dict, List, Optional
import threading
import fnmatch

from .interface import CacheInterface


class MemoryCache(CacheInterface):
    """内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        """
        初始化内存缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认TTL(秒)
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        
        # 使用OrderedDict实现LRU
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0,
            'expired': 0
        }
        
        # 启动清理任务
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动定期清理过期缓存的任务"""
        async def cleanup_expired():
            while True:
                await asyncio.sleep(60)  # 每分钟清理一次
                await self._cleanup_expired()
        
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(cleanup_expired())
        except RuntimeError:
            # 如果没有事件循环，跳过清理任务
            pass
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if self._is_expired(entry):
                del self._cache[key]
                self._stats['expired'] += 1
                self._stats['misses'] += 1
                return None
            
            # 移动到末尾(LRU)
            self._cache.move_to_end(key)
            self._stats['hits'] += 1
            
            return entry['value']
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            if ttl is None:
                ttl = self.default_ttl
            
            # 计算过期时间
            expire_time = time.time() + ttl if ttl > 0 else None
            
            # 创建缓存条目
            entry = {
                'value': value,
                'expire_time': expire_time,
                'created_time': time.time()
            }
            
            # 如果键已存在，更新它
            if key in self._cache:
                self._cache[key] = entry
                self._cache.move_to_end(key)
            else:
                # 检查是否需要驱逐
                if len(self._cache) >= self.max_size:
                    self._evict_lru()
                
                self._cache[key] = entry
            
            self._stats['sets'] += 1
            return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats['deletes'] += 1
                return True
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        with self._lock:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            if self._is_expired(entry):
                del self._cache[key]
                self._stats['expired'] += 1
                return False
            
            return True
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
            return True
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'backend': 'memory',
                'size': len(self._cache),
                'max_size': self.max_size,
                'hit_rate': round(hit_rate, 4),
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'sets': self._stats['sets'],
                'deletes': self._stats['deletes'],
                'evictions': self._stats['evictions'],
                'expired': self._stats['expired']
            }
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的所有键"""
        with self._lock:
            if pattern == "*":
                return list(self._cache.keys())
            
            # 使用fnmatch进行模式匹配
            return [key for key in self._cache.keys() if fnmatch.fnmatch(key, pattern)]
    
    async def ttl(self, key: str) -> Optional[int]:
        """获取缓存剩余过期时间"""
        with self._lock:
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            if entry['expire_time'] is None:
                return -1  # 永不过期
            
            remaining = entry['expire_time'] - time.time()
            return max(0, int(remaining))
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        with self._lock:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            entry['expire_time'] = time.time() + ttl if ttl > 0 else None
            return True
    
    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """检查缓存条目是否过期"""
        if entry['expire_time'] is None:
            return False
        return time.time() > entry['expire_time']
    
    def _evict_lru(self):
        """驱逐最近最少使用的缓存条目"""
        if self._cache:
            self._cache.popitem(last=False)  # 移除最旧的条目
            self._stats['evictions'] += 1
    
    async def _cleanup_expired(self):
        """清理过期的缓存条目"""
        with self._lock:
            current_time = time.time()
            expired_keys = []
            
            for key, entry in self._cache.items():
                if (entry['expire_time'] is not None and 
                    current_time > entry['expire_time']):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self._stats['expired'] += 1
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存值"""
        result = {}
        for key in keys:
            value = await self.get(key)
            if value is not None:
                result[key] = value
        return result
    
    async def set_many(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """批量设置缓存值"""
        success_count = 0
        for key, value in mapping.items():
            if await self.set(key, value, ttl):
                success_count += 1
        return success_count == len(mapping)
    
    def __del__(self):
        """析构函数，取消清理任务"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
