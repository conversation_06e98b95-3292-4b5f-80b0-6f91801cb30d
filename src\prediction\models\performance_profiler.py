"""
性能分析器

提供模型性能分析、内存使用监控、推理速度测试等功能。
"""

import torch
import torch.nn as nn
import torch.profiler as profiler
from typing import Dict, Any, Optional, List, Tuple
import numpy as np
import logging
import time
import psutil
import gc
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, model: nn.Module, device: str = 'cpu'):
        self.model = model
        self.device = device
        self.profiling_results = {}
        self.benchmark_results = {}
        
        # 移动模型到指定设备
        self.model = self.model.to(device)
        
        logger.info(f"性能分析器初始化完成，设备: {device}")
    
    def profile_inference(
        self,
        input_data: torch.Tensor,
        num_runs: int = 100,
        warmup_runs: int = 10,
        use_profiler: bool = True
    ) -> Dict[str, Any]:
        """
        推理性能分析
        
        Args:
            input_data: 输入数据
            num_runs: 运行次数
            warmup_runs: 预热次数
            use_profiler: 是否使用PyTorch Profiler
            
        Returns:
            性能分析结果
        """
        logger.info(f"开始推理性能分析，运行次数: {num_runs}")
        
        input_data = input_data.to(self.device)
        self.model.eval()
        
        # 预热
        with torch.no_grad():
            for _ in range(warmup_runs):
                _ = self.model(input_data)
        
        # 同步GPU（如果使用）
        if self.device != 'cpu':
            torch.cuda.synchronize()
        
        # 性能测试
        inference_times = []
        memory_usage = []
        
        if use_profiler:
            with profiler.profile(
                activities=[
                    profiler.ProfilerActivity.CPU,
                    profiler.ProfilerActivity.CUDA if self.device != 'cpu' else None
                ],
                record_shapes=True,
                profile_memory=True,
                with_stack=True
            ) as prof:
                with torch.no_grad():
                    for i in range(num_runs):
                        start_time = time.perf_counter()
                        
                        # 记录内存使用
                        if self.device != 'cpu':
                            torch.cuda.reset_peak_memory_stats()
                            memory_before = torch.cuda.memory_allocated()
                        else:
                            memory_before = psutil.Process().memory_info().rss
                        
                        # 推理
                        output = self.model(input_data)
                        
                        # 同步
                        if self.device != 'cpu':
                            torch.cuda.synchronize()
                        
                        end_time = time.perf_counter()
                        inference_times.append(end_time - start_time)
                        
                        # 记录内存使用
                        if self.device != 'cpu':
                            memory_after = torch.cuda.max_memory_allocated()
                            memory_usage.append(memory_after - memory_before)
                        else:
                            memory_after = psutil.Process().memory_info().rss
                            memory_usage.append(memory_after - memory_before)
            
            # 保存profiler结果
            self.profiling_results['profiler_trace'] = prof.key_averages().table(
                sort_by="cuda_time_total" if self.device != 'cpu' else "cpu_time_total"
            )
        else:
            with torch.no_grad():
                for i in range(num_runs):
                    start_time = time.perf_counter()
                    
                    if self.device != 'cpu':
                        memory_before = torch.cuda.memory_allocated()
                    else:
                        memory_before = psutil.Process().memory_info().rss
                    
                    output = self.model(input_data)
                    
                    if self.device != 'cpu':
                        torch.cuda.synchronize()
                    
                    end_time = time.perf_counter()
                    inference_times.append(end_time - start_time)
                    
                    if self.device != 'cpu':
                        memory_after = torch.cuda.memory_allocated()
                        memory_usage.append(memory_after - memory_before)
                    else:
                        memory_after = psutil.Process().memory_info().rss
                        memory_usage.append(memory_after - memory_before)
        
        # 计算统计信息
        inference_times = np.array(inference_times)
        memory_usage = np.array(memory_usage)
        
        results = {
            'num_runs': num_runs,
            'input_shape': list(input_data.shape),
            'device': self.device,
            'inference_time': {
                'mean': float(np.mean(inference_times)),
                'std': float(np.std(inference_times)),
                'min': float(np.min(inference_times)),
                'max': float(np.max(inference_times)),
                'median': float(np.median(inference_times)),
                'p95': float(np.percentile(inference_times, 95)),
                'p99': float(np.percentile(inference_times, 99))
            },
            'memory_usage': {
                'mean_mb': float(np.mean(memory_usage)) / (1024 * 1024),
                'max_mb': float(np.max(memory_usage)) / (1024 * 1024),
                'min_mb': float(np.min(memory_usage)) / (1024 * 1024)
            },
            'throughput': {
                'samples_per_sec': input_data.size(0) / np.mean(inference_times),
                'batches_per_sec': 1.0 / np.mean(inference_times)
            }
        }
        
        self.profiling_results['inference'] = results
        
        logger.info(f"推理性能分析完成，平均时间: {results['inference_time']['mean']:.4f}s")
        return results
    
    def benchmark_batch_sizes(
        self,
        input_shape: Tuple[int, ...],
        batch_sizes: List[int],
        num_runs: int = 50
    ) -> Dict[str, Any]:
        """
        批处理大小基准测试
        
        Args:
            input_shape: 输入形状（不包括batch维度）
            batch_sizes: 要测试的批处理大小列表
            num_runs: 每个批处理大小的运行次数
            
        Returns:
            基准测试结果
        """
        logger.info(f"开始批处理大小基准测试: {batch_sizes}")
        
        results = {}
        
        for batch_size in batch_sizes:
            logger.info(f"测试批处理大小: {batch_size}")
            
            # 创建测试数据
            test_input = torch.randn(batch_size, *input_shape).to(self.device)
            
            try:
                # 性能测试
                perf_results = self.profile_inference(
                    test_input, 
                    num_runs=num_runs, 
                    use_profiler=False
                )
                
                results[batch_size] = {
                    'inference_time_mean': perf_results['inference_time']['mean'],
                    'memory_usage_mb': perf_results['memory_usage']['mean_mb'],
                    'throughput_samples_per_sec': perf_results['throughput']['samples_per_sec'],
                    'time_per_sample': perf_results['inference_time']['mean'] / batch_size,
                    'success': True
                }
                
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    logger.warning(f"批处理大小 {batch_size} 内存不足")
                    results[batch_size] = {
                        'error': 'OOM',
                        'success': False
                    }
                    # 清理内存
                    if self.device != 'cpu':
                        torch.cuda.empty_cache()
                    gc.collect()
                else:
                    raise e
        
        self.benchmark_results['batch_sizes'] = results
        
        # 找到最优批处理大小
        successful_results = {k: v for k, v in results.items() if v.get('success', False)}
        if successful_results:
            optimal_batch_size = max(
                successful_results.keys(),
                key=lambda x: successful_results[x]['throughput_samples_per_sec']
            )
            results['optimal_batch_size'] = optimal_batch_size
        
        logger.info("批处理大小基准测试完成")
        return results
    
    def analyze_model_complexity(self) -> Dict[str, Any]:
        """分析模型复杂度"""
        logger.info("开始模型复杂度分析")
        
        total_params = 0
        trainable_params = 0
        layer_info = []
        
        for name, module in self.model.named_modules():
            if len(list(module.children())) == 0:  # 叶子节点
                num_params = sum(p.numel() for p in module.parameters())
                num_trainable = sum(p.numel() for p in module.parameters() if p.requires_grad)
                
                layer_info.append({
                    'name': name,
                    'type': type(module).__name__,
                    'parameters': num_params,
                    'trainable_parameters': num_trainable
                })
                
                total_params += num_params
                trainable_params += num_trainable
        
        # 估算FLOPs（简化版本）
        sample_input = torch.randn(1, 10, 3).to(self.device)
        flops = self._estimate_flops(sample_input)
        
        results = {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'non_trainable_parameters': total_params - trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # 假设float32
            'estimated_flops': flops,
            'layer_details': layer_info
        }
        
        self.profiling_results['complexity'] = results
        
        logger.info(f"模型复杂度分析完成，总参数: {total_params:,}")
        return results
    
    def _estimate_flops(self, input_tensor: torch.Tensor) -> int:
        """估算FLOPs（简化版本）"""
        flops = 0
        
        def flop_count_hook(module, input, output):
            nonlocal flops
            if isinstance(module, nn.Linear):
                flops += module.in_features * module.out_features
            elif isinstance(module, nn.Conv1d):
                flops += (module.in_channels * module.out_channels * 
                         module.kernel_size[0] * output.shape[-1])
            elif isinstance(module, (nn.LSTM, nn.GRU)):
                # 简化的RNN FLOPs计算
                hidden_size = module.hidden_size
                input_size = module.input_size
                seq_len = input[0].shape[1] if len(input) > 0 else 1
                flops += seq_len * (4 * hidden_size * (input_size + hidden_size))
        
        # 注册钩子
        hooks = []
        for module in self.model.modules():
            if isinstance(module, (nn.Linear, nn.Conv1d, nn.LSTM, nn.GRU)):
                hooks.append(module.register_forward_hook(flop_count_hook))
        
        # 前向传播
        with torch.no_grad():
            self.model(input_tensor)
        
        # 移除钩子
        for hook in hooks:
            hook.remove()
        
        return flops
    
    def compare_models(
        self,
        other_models: Dict[str, nn.Module],
        input_data: torch.Tensor,
        num_runs: int = 50
    ) -> Dict[str, Any]:
        """比较多个模型的性能"""
        logger.info(f"开始比较 {len(other_models) + 1} 个模型的性能")
        
        results = {}
        
        # 测试当前模型
        current_results = self.profile_inference(input_data, num_runs, use_profiler=False)
        results['current_model'] = current_results
        
        # 测试其他模型
        for name, model in other_models.items():
            logger.info(f"测试模型: {name}")
            
            # 创建临时profiler
            temp_profiler = PerformanceProfiler(model, self.device)
            model_results = temp_profiler.profile_inference(input_data, num_runs, use_profiler=False)
            results[name] = model_results
        
        # 生成比较报告
        comparison = self._generate_comparison_report(results)
        
        self.benchmark_results['model_comparison'] = {
            'results': results,
            'comparison': comparison
        }
        
        logger.info("模型性能比较完成")
        return results
    
    def _generate_comparison_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成比较报告"""
        comparison = {}
        
        # 提取关键指标
        for model_name, result in results.items():
            comparison[model_name] = {
                'avg_inference_time': result['inference_time']['mean'],
                'throughput': result['throughput']['samples_per_sec'],
                'memory_usage': result['memory_usage']['mean_mb']
            }
        
        # 找到最佳模型
        best_speed = min(comparison.keys(), 
                        key=lambda x: comparison[x]['avg_inference_time'])
        best_throughput = max(comparison.keys(), 
                             key=lambda x: comparison[x]['throughput'])
        best_memory = min(comparison.keys(), 
                         key=lambda x: comparison[x]['memory_usage'])
        
        comparison['best_models'] = {
            'fastest': best_speed,
            'highest_throughput': best_throughput,
            'lowest_memory': best_memory
        }
        
        return comparison
    
    def generate_report(self, save_path: Optional[str] = None) -> str:
        """生成性能报告"""
        report = []
        report.append("=" * 60)
        report.append("模型性能分析报告")
        report.append("=" * 60)
        
        # 推理性能
        if 'inference' in self.profiling_results:
            inf_results = self.profiling_results['inference']
            report.append("\n📊 推理性能:")
            report.append(f"  平均推理时间: {inf_results['inference_time']['mean']:.4f}s")
            report.append(f"  推理时间标准差: {inf_results['inference_time']['std']:.4f}s")
            report.append(f"  吞吐量: {inf_results['throughput']['samples_per_sec']:.2f} samples/s")
            report.append(f"  内存使用: {inf_results['memory_usage']['mean_mb']:.2f} MB")
        
        # 模型复杂度
        if 'complexity' in self.profiling_results:
            comp_results = self.profiling_results['complexity']
            report.append("\n🔧 模型复杂度:")
            report.append(f"  总参数量: {comp_results['total_parameters']:,}")
            report.append(f"  可训练参数: {comp_results['trainable_parameters']:,}")
            report.append(f"  模型大小: {comp_results['model_size_mb']:.2f} MB")
            report.append(f"  估算FLOPs: {comp_results['estimated_flops']:,}")
        
        # 批处理基准
        if 'batch_sizes' in self.benchmark_results:
            batch_results = self.benchmark_results['batch_sizes']
            report.append("\n📈 批处理基准:")
            if 'optimal_batch_size' in batch_results:
                report.append(f"  最优批处理大小: {batch_results['optimal_batch_size']}")
        
        report_text = "\n".join(report)
        
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(report_text)
            logger.info(f"性能报告已保存到: {save_path}")
        
        return report_text
    
    def get_profiling_results(self) -> Dict[str, Any]:
        """获取所有分析结果"""
        return {
            'profiling_results': self.profiling_results,
            'benchmark_results': self.benchmark_results
        }


if __name__ == "__main__":
    # 测试性能分析器
    from optimized_cnn_lstm import create_optimized_model
    
    config = {'input_size': 3, 'hidden_size': 64, 'num_classes': 1000}
    model = create_optimized_model(config)
    
    profiler = PerformanceProfiler(model, device='cpu')
    
    # 测试推理性能
    test_input = torch.randn(8, 10, 3)
    results = profiler.profile_inference(test_input, num_runs=20)
    
    # 分析模型复杂度
    complexity = profiler.analyze_model_complexity()
    
    # 生成报告
    report = profiler.generate_report()
    print(report)
