#!/usr/bin/env python3
"""
实时WebSocket客户端组件
用于Streamlit界面的WebSocket连接和消息处理
"""

import asyncio
import json
import logging
import time
from typing import Any, Callable, Dict, List, Optional

import streamlit as st

# 配置日志
logger = logging.getLogger(__name__)

class RealtimeWebSocketClient:
    """实时WebSocket客户端"""
    
    def __init__(self, websocket_url: str = "ws://127.0.0.1:8888/ws/prediction-results"):
        self.websocket_url = websocket_url
        self.websocket = None
        self.is_connected = False
        self.message_handlers = {}
        self.connection_callbacks = []
        self.error_callbacks = []
        self.stats = {
            'messages_received': 0,
            'connection_attempts': 0,
            'last_message_time': None,
            'connection_start_time': None
        }
    
    def add_message_handler(self, message_type: str, handler: Callable[[Dict[str, Any]], None]):
        """添加消息处理器"""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)
    
    def add_connection_callback(self, callback: Callable[[bool], None]):
        """添加连接状态回调"""
        self.connection_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str], None]):
        """添加错误回调"""
        self.error_callbacks.append(callback)
    
    async def connect(self) -> bool:
        """建立WebSocket连接"""
        try:
            import websockets
            
            self.stats['connection_attempts'] += 1
            self.stats['connection_start_time'] = time.time()
            
            # 建立WebSocket连接
            self.websocket = await websockets.connect(
                self.websocket_url,
                ping_interval=30,
                ping_timeout=10,
                close_timeout=10
            )
            
            self.is_connected = True
            
            # 通知连接状态变化
            for callback in self.connection_callbacks:
                try:
                    callback(True)
                except Exception as e:
                    logger.error(f"连接回调执行失败: {e}")
            
            logger.info(f"WebSocket连接成功: {self.websocket_url}")
            return True
            
        except Exception as e:
            self.is_connected = False
            error_msg = f"WebSocket连接失败: {e}"
            logger.error(error_msg)
            
            # 通知错误
            for callback in self.error_callbacks:
                try:
                    callback(error_msg)
                except Exception as e:
                    logger.error(f"错误回调执行失败: {e}")
            
            return False
    
    async def disconnect(self):
        """断开WebSocket连接"""
        if self.websocket and self.is_connected:
            try:
                await self.websocket.close()
                self.is_connected = False
                
                # 通知连接状态变化
                for callback in self.connection_callbacks:
                    try:
                        callback(False)
                    except Exception as e:
                        logger.error(f"断开连接回调执行失败: {e}")
                
                logger.info("WebSocket连接已断开")
            except Exception as e:
                logger.error(f"断开WebSocket连接失败: {e}")
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """发送消息"""
        if not self.is_connected or not self.websocket:
            logger.warning("WebSocket未连接，无法发送消息")
            return False
        
        try:
            await self.websocket.send(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            return False
    
    async def listen_for_messages(self):
        """监听WebSocket消息"""
        if not self.is_connected or not self.websocket:
            return
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    self.stats['messages_received'] += 1
                    self.stats['last_message_time'] = time.time()
                    
                    # 处理消息
                    await self._handle_message(data)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                except Exception as e:
                    logger.error(f"处理消息失败: {e}")
                    
        except Exception as e:
            logger.error(f"监听WebSocket消息失败: {e}")
            self.is_connected = False
            
            # 通知连接断开
            for callback in self.connection_callbacks:
                try:
                    callback(False)
                except Exception as e:
                    logger.error(f"连接断开回调执行失败: {e}")
    
    async def _handle_message(self, data: Dict[str, Any]):
        """处理接收到的消息"""
        message_type = data.get('type', 'unknown')
        
        # 调用对应的消息处理器
        if message_type in self.message_handlers:
            for handler in self.message_handlers[message_type]:
                try:
                    handler(data)
                except Exception as e:
                    logger.error(f"消息处理器执行失败: {e}")
        else:
            logger.debug(f"未处理的消息类型: {message_type}")
    
    async def ping(self) -> bool:
        """发送心跳"""
        return await self.send_message({
            'type': 'ping',
            'timestamp': time.time()
        })
    
    async def subscribe_to_topics(self, topics: List[str]) -> bool:
        """订阅主题"""
        return await self.send_message({
            'type': 'subscribe',
            'topics': topics,
            'timestamp': time.time()
        })
    
    async def unsubscribe_from_topics(self, topics: List[str]) -> bool:
        """取消订阅主题"""
        return await self.send_message({
            'type': 'unsubscribe',
            'topics': topics,
            'timestamp': time.time()
        })
    
    async def get_stats(self) -> bool:
        """获取统计信息"""
        return await self.send_message({
            'type': 'get_stats',
            'timestamp': time.time()
        })
    
    def get_client_stats(self) -> Dict[str, Any]:
        """获取客户端统计信息"""
        current_time = time.time()
        connection_duration = 0
        
        if self.stats['connection_start_time']:
            connection_duration = current_time - self.stats['connection_start_time']
        
        return {
            'is_connected': self.is_connected,
            'websocket_url': self.websocket_url,
            'messages_received': self.stats['messages_received'],
            'connection_attempts': self.stats['connection_attempts'],
            'connection_duration': connection_duration,
            'last_message_time': self.stats['last_message_time'],
            'message_handlers_count': sum(len(handlers) for handlers in self.message_handlers.values()),
            'subscribed_message_types': list(self.message_handlers.keys())
        }

# Streamlit集成辅助函数
def create_websocket_client(websocket_url: str = "ws://127.0.0.1:8888/ws/prediction-results") -> RealtimeWebSocketClient:
    """创建WebSocket客户端实例"""
    if 'websocket_client' not in st.session_state:
        st.session_state.websocket_client = RealtimeWebSocketClient(websocket_url)
    
    return st.session_state.websocket_client

def get_websocket_client() -> Optional[RealtimeWebSocketClient]:
    """获取当前的WebSocket客户端实例"""
    return st.session_state.get('websocket_client')

def display_connection_status():
    """显示WebSocket连接状态"""
    client = get_websocket_client()
    
    if client:
        stats = client.get_client_stats()
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if stats['is_connected']:
                st.success("🟢 WebSocket已连接")
            else:
                st.error("🔴 WebSocket未连接")
        
        with col2:
            st.metric("接收消息数", stats['messages_received'])
        
        with col3:
            if stats['connection_duration'] > 0:
                st.metric("连接时长", f"{stats['connection_duration']:.1f}秒")
            else:
                st.metric("连接时长", "未连接")
        
        # 详细统计信息
        with st.expander("详细连接信息"):
            st.json(stats)
    else:
        st.warning("WebSocket客户端未初始化")

def setup_prediction_handlers(client: RealtimeWebSocketClient):
    """设置预测相关的消息处理器"""
    
    def handle_prediction_start(data: Dict[str, Any]):
        """处理预测开始消息"""
        if 'prediction_messages' not in st.session_state:
            st.session_state.prediction_messages = []
        
        st.session_state.prediction_messages.append({
            'type': 'start',
            'data': data,
            'timestamp': time.time()
        })
        
        # 触发界面更新
        st.rerun()
    
    def handle_prediction_progress(data: Dict[str, Any]):
        """处理预测进度消息"""
        if 'prediction_progress' not in st.session_state:
            st.session_state.prediction_progress = {}
        
        prediction_id = data.get('data', {}).get('prediction_id', 'unknown')
        st.session_state.prediction_progress[prediction_id] = data
        
        # 触发界面更新
        st.rerun()
    
    def handle_prediction_result(data: Dict[str, Any]):
        """处理预测结果消息"""
        if 'prediction_results' not in st.session_state:
            st.session_state.prediction_results = []
        
        st.session_state.prediction_results.append({
            'data': data,
            'timestamp': time.time()
        })
        
        # 触发界面更新
        st.rerun()
    
    def handle_prediction_error(data: Dict[str, Any]):
        """处理预测错误消息"""
        if 'prediction_errors' not in st.session_state:
            st.session_state.prediction_errors = []
        
        st.session_state.prediction_errors.append({
            'data': data,
            'timestamp': time.time()
        })
        
        # 触发界面更新
        st.rerun()
    
    # 注册消息处理器
    client.add_message_handler('prediction_start', handle_prediction_start)
    client.add_message_handler('prediction_progress', handle_prediction_progress)
    client.add_message_handler('prediction_result', handle_prediction_result)
    client.add_message_handler('prediction_error', handle_prediction_error)
