# serena MCP自动启动配置指南

## 🎯 目标
确保Cursor重启后，serena MCP服务器能够自动启动并正常工作，仪表板 `http://127.0.0.1:24282/dashboard/index.html` 可正常访问。

## ✅ 当前状态
- ✅ serena MCP服务器已配置并正常运行
- ✅ Pyright语言服务器依赖已安装
- ✅ Web仪表板可正常访问
- ✅ 所有26个serena工具正常可用
- ✅ 自动启动脚本已创建
- ✅ 健康检查脚本已创建
- ✅ 配置备份已完成

## 🚀 自动启动方案

### 方案1: 一键启动批处理脚本（推荐）
```batch
# 双击运行
scripts\start_serena_mcp.bat
```

**功能特性:**
- 自动检查serena可执行文件
- 终止现有进程避免冲突
- 启动serena MCP服务器
- 自动健康检查
- 可选打开仪表板

### 方案2: PowerShell脚本
```powershell
# 基本启动
scripts\start_serena_mcp.ps1

# 自动打开仪表板
scripts\start_serena_mcp.ps1 -AutoOpen

# 跳过健康检查
scripts\start_serena_mcp.ps1 -SkipHealthCheck
```

### 方案3: Python自动修复脚本
```bash
# 完整自动修复流程
python scripts/serena_auto_fix.py

# 仅启动服务器
python scripts/serena_auto_fix.py --start-only
```

## 🔍 健康检查工具

### 快速状态检查
```bash
python scripts/serena_health_check.py --quick
```

### 完整健康检查
```bash
python scripts/serena_health_check.py
```

**检查项目:**
- ✅ serena可执行文件存在
- ✅ pyright依赖可用
- ✅ serena进程运行状态
- ✅ 仪表板可访问性
- ✅ serena工具可用性

## 🔧 Cursor重启后的操作流程

### 标准流程
1. **启动serena服务器**
   ```batch
   # 运行一键启动脚本
   scripts\start_serena_mcp.bat
   ```

2. **重启Cursor IDE**
   - 完全关闭Cursor
   - 重新启动Cursor
   - 等待Augment加载完成

3. **验证MCP连接**
   - 打开Augment MCP设置
   - 检查serena连接状态（应为绿色）
   - 测试serena工具功能

4. **访问仪表板**
   - 打开浏览器访问: http://127.0.0.1:24282/dashboard/index.html
   - 验证所有26个工具可用

### 快速流程（如果serena已运行）
1. **检查状态**
   ```bash
   python scripts/serena_health_check.py --quick
   ```

2. **如果状态正常**
   - 直接重启Cursor
   - 验证MCP连接

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. Language server failed to start
**症状:** serena工具无法使用，语言服务器启动失败
**解决方案:**
```bash
# 安装pyright到系统Python
"C:\Program Files\Python311\python.exe" -m pip install pyright

# 重启serena服务器
python scripts/serena_auto_fix.py
```

#### 2. Dashboard not accessible
**症状:** 无法访问 http://127.0.0.1:24282/dashboard/index.html
**解决方案:**
```bash
# 检查serena进程
tasklist | findstr serena-mcp-server

# 重启serena服务器
scripts\start_serena_mcp.bat
```

#### 3. MCP connection failed
**症状:** Augment中serena显示红色/断开状态
**解决方案:**
1. 确保serena服务器运行
2. 重启Cursor IDE
3. 重新配置MCP连接

#### 4. Port 24282 already in use
**症状:** 端口被占用，无法启动
**解决方案:**
```bash
# 终止占用进程
taskkill /F /IM serena-mcp-server.exe

# 重新启动
scripts\start_serena_mcp.bat
```

### 紧急重置流程
如果遇到严重问题：
```bash
# 1. 运行自动修复
python scripts/serena_auto_fix.py

# 2. 重启Cursor
# 3. 重新配置MCP连接

# 4. 验证状态
python scripts/serena_health_check.py
```

## 📁 配置文件位置

### 重要文件
- **serena可执行文件**: `d:/github/3dyuce/venv/Scripts/serena-mcp-server.exe`
- **项目根目录**: `d:/github/3dyuce`
- **仪表板URL**: `http://127.0.0.1:24282/dashboard/index.html`
- **配置备份**: `config/serena_backups/`

### Augment MCP配置
- **服务器名称**: serena
- **可执行文件**: `d:/github/3dyuce/venv/Scripts/serena-mcp-server.exe`
- **参数**: `--project d:/github/3dyuce`
- **工作目录**: `d:/github/3dyuce`

## 🎯 验收标准

### 成功标志
- ✅ serena MCP服务器自动启动
- ✅ 仪表板 http://127.0.0.1:24282/dashboard/index.html 可访问
- ✅ Augment中serena连接状态为绿色
- ✅ 所有26个serena工具正常工作
- ✅ Pyright语言服务器正常运行

### 性能指标
- 🚀 启动时间: <30秒
- 🔗 连接成功率: >95%
- 🛠️ 工具可用率: 100%
- 📊 仪表板响应时间: <3秒

## 📞 支持信息

### 自动化脚本
- `scripts/start_serena_mcp.bat` - Windows批处理启动脚本
- `scripts/start_serena_mcp.ps1` - PowerShell启动脚本  
- `scripts/serena_auto_fix.py` - 自动诊断修复脚本
- `scripts/serena_health_check.py` - 健康检查脚本
- `scripts/backup_serena_config.py` - 配置备份脚本

### 配置模板
- `config/serena_backups/serena_mcp_config_template.json` - 配置模板
- `config/serena_backups/startup_instructions.md` - 详细启动说明

---

**最后更新**: 2025-08-01  
**状态**: ✅ 配置完成，测试通过
