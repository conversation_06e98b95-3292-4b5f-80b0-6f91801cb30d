"""
模型量化

提供动态量化、静态量化、QAT等量化技术。
"""

import copy
import logging
from typing import Any, Callable, Dict, List, Optional

import numpy as np
import torch
import torch.nn as nn
import torch.quantization as quant
from torch.quantization import DeQuantStub, QuantStub

logger = logging.getLogger(__name__)


class QuantizableModel(nn.Module):
    """可量化的模型包装器"""
    
    def __init__(self, model: nn.Module):
        super(QuantizableModel, self).__init__()
        self.quant = QuantStub()
        self.model = model
        self.dequant = DeQuantStub()
    
    def forward(self, x):
        x = self.quant(x)
        x = self.model(x)
        x = self.dequant(x)
        return x


class ModelQuantization:
    """模型量化器"""
    
    def __init__(self, model: nn.Module):
        self.original_model = model
        self.quantized_model = None
        self.quantization_stats = {}
    
    def dynamic_quantization(
        self,
        qconfig_spec: Optional[Dict] = None,
        dtype: torch.dtype = torch.qint8
    ) -> nn.Module:
        """
        动态量化
        
        Args:
            qconfig_spec: 量化配置
            dtype: 量化数据类型
            
        Returns:
            量化后的模型
        """
        logger.info("开始动态量化")
        
        # 默认量化配置
        if qconfig_spec is None:
            qconfig_spec = {
                nn.Linear: torch.quantization.default_dynamic_qconfig,
                nn.LSTM: torch.quantization.default_dynamic_qconfig,
                nn.GRU: torch.quantization.default_dynamic_qconfig,
            }
        
        # 执行动态量化
        self.quantized_model = torch.quantization.quantize_dynamic(
            self.original_model,
            qconfig_spec,
            dtype=dtype
        )
        
        # 记录量化统计
        self._update_quantization_stats('dynamic', dtype)
        
        logger.info("动态量化完成")
        return self.quantized_model
    
    def static_quantization(
        self,
        calibration_loader: torch.utils.data.DataLoader,
        qconfig: Optional[torch.quantization.QConfig] = None
    ) -> nn.Module:
        """
        静态量化
        
        Args:
            calibration_loader: 校准数据加载器
            qconfig: 量化配置
            
        Returns:
            量化后的模型
        """
        logger.info("开始静态量化")
        
        # 创建可量化模型
        quantizable_model = QuantizableModel(self.original_model)
        
        # 设置量化配置
        if qconfig is None:
            qconfig = torch.quantization.get_default_qconfig('fbgemm')
        
        quantizable_model.qconfig = qconfig
        
        # 准备量化
        torch.quantization.prepare(quantizable_model, inplace=True)
        
        # 校准
        quantizable_model.eval()
        with torch.no_grad():
            for data, _ in calibration_loader:
                quantizable_model(data)
        
        # 转换为量化模型
        self.quantized_model = torch.quantization.convert(quantizable_model, inplace=False)
        
        # 记录量化统计
        self._update_quantization_stats('static', torch.qint8)
        
        logger.info("静态量化完成")
        return self.quantized_model
    
    def quantization_aware_training(
        self,
        train_loader: torch.utils.data.DataLoader,
        val_loader: torch.utils.data.DataLoader,
        num_epochs: int = 10,
        qconfig: Optional[torch.quantization.QConfig] = None
    ) -> nn.Module:
        """
        量化感知训练 (QAT)
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            num_epochs: 训练轮数
            qconfig: 量化配置
            
        Returns:
            量化后的模型
        """
        logger.info("开始量化感知训练")
        
        # 创建可量化模型
        quantizable_model = QuantizableModel(self.original_model)
        
        # 设置量化配置
        if qconfig is None:
            qconfig = torch.quantization.get_default_qat_qconfig('fbgemm')
        
        quantizable_model.qconfig = qconfig
        
        # 准备QAT
        torch.quantization.prepare_qat(quantizable_model, inplace=True)
        
        # 训练
        optimizer = torch.optim.Adam(quantizable_model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        for epoch in range(num_epochs):
            # 训练阶段
            quantizable_model.train()
            train_loss = 0.0
            
            for batch_idx, (data, target) in enumerate(train_loader):
                optimizer.zero_grad()
                output = quantizable_model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
                train_loss += loss.item()
            
            # 验证阶段
            val_loss, val_acc = self._validate_qat(quantizable_model, val_loader, criterion)
            
            logger.info(f"Epoch {epoch+1}/{num_epochs}, "
                       f"Train Loss: {train_loss/len(train_loader):.4f}, "
                       f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # 转换为量化模型
        quantizable_model.eval()
        self.quantized_model = torch.quantization.convert(quantizable_model, inplace=False)
        
        # 记录量化统计
        self._update_quantization_stats('qat', torch.qint8)
        
        logger.info("量化感知训练完成")
        return self.quantized_model
    
    def mixed_precision_quantization(
        self,
        sensitive_layers: Optional[List[str]] = None
    ) -> nn.Module:
        """
        混合精度量化
        
        Args:
            sensitive_layers: 敏感层列表（保持FP32）
            
        Returns:
            量化后的模型
        """
        logger.info("开始混合精度量化")
        
        if sensitive_layers is None:
            sensitive_layers = []
        
        # 创建量化配置映射
        qconfig_dict = {}
        
        for name, module in self.original_model.named_modules():
            if any(sensitive in name for sensitive in sensitive_layers):
                # 敏感层保持FP32
                qconfig_dict[name] = None
            elif isinstance(module, (nn.Linear, nn.Conv1d)):
                # 其他层使用量化
                qconfig_dict[name] = torch.quantization.default_dynamic_qconfig
        
        # 执行量化
        self.quantized_model = torch.quantization.quantize_dynamic(
            self.original_model,
            qconfig_dict,
            dtype=torch.qint8
        )
        
        # 记录量化统计
        self._update_quantization_stats('mixed_precision', torch.qint8)
        
        logger.info("混合精度量化完成")
        return self.quantized_model
    
    def custom_quantization(
        self,
        bit_width: int = 8,
        quantization_scheme: str = 'symmetric'
    ) -> nn.Module:
        """
        自定义量化
        
        Args:
            bit_width: 量化位宽
            quantization_scheme: 量化方案
            
        Returns:
            量化后的模型
        """
        logger.info(f"开始自定义量化，位宽: {bit_width}, 方案: {quantization_scheme}")
        
        # 创建模型副本用于量化
        quantized_model = type(self.original_model)(**self.original_model.__dict__.get('config', {}))
        quantized_model.load_state_dict(self.original_model.state_dict())
        
        # 自定义量化逻辑
        for name, module in quantized_model.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv1d)):
                # 量化权重
                weight = module.weight.data
                quantized_weight = self._quantize_tensor(
                    weight, bit_width, quantization_scheme
                )
                module.weight.data = quantized_weight
                
                # 量化偏置（如果存在）
                if module.bias is not None:
                    bias = module.bias.data
                    quantized_bias = self._quantize_tensor(
                        bias, bit_width, quantization_scheme
                    )
                    module.bias.data = quantized_bias
        
        self.quantized_model = quantized_model
        
        # 记录量化统计
        self._update_quantization_stats('custom', f'{bit_width}bit')
        
        logger.info("自定义量化完成")
        return self.quantized_model
    
    def _quantize_tensor(
        self,
        tensor: torch.Tensor,
        bit_width: int,
        scheme: str
    ) -> torch.Tensor:
        """量化张量"""
        if scheme == 'symmetric':
            # 对称量化
            max_val = torch.max(torch.abs(tensor))
            scale = max_val / (2 ** (bit_width - 1) - 1)
            quantized = torch.round(tensor / scale)
            quantized = torch.clamp(quantized, -(2 ** (bit_width - 1)), 2 ** (bit_width - 1) - 1)
            return quantized * scale
        else:
            # 非对称量化
            min_val = torch.min(tensor)
            max_val = torch.max(tensor)
            scale = (max_val - min_val) / (2 ** bit_width - 1)
            zero_point = torch.round(-min_val / scale)
            quantized = torch.round(tensor / scale + zero_point)
            quantized = torch.clamp(quantized, 0, 2 ** bit_width - 1)
            return (quantized - zero_point) * scale
    
    def _validate_qat(
        self,
        model: nn.Module,
        val_loader: torch.utils.data.DataLoader,
        criterion: nn.Module
    ) -> tuple:
        """QAT验证"""
        model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                output = model(data)
                val_loss += criterion(output, target).item()
                
                if output.dim() > 1 and output.size(1) > 1:
                    pred = output.argmax(dim=1)
                    correct += pred.eq(target).sum().item()
                    total += target.size(0)
        
        val_loss /= len(val_loader)
        val_acc = correct / total if total > 0 else 0.0
        
        return val_loss, val_acc
    
    def _update_quantization_stats(self, method: str, dtype):
        """更新量化统计"""
        if self.quantized_model is None:
            return
        
        # 计算模型大小
        original_size = sum(p.numel() * p.element_size() for p in self.original_model.parameters())
        quantized_size = sum(p.numel() * p.element_size() for p in self.quantized_model.parameters())
        
        compression_ratio = 1 - (quantized_size / original_size)
        
        self.quantization_stats[method] = {
            'dtype': str(dtype),
            'original_size_mb': original_size / (1024 * 1024),
            'quantized_size_mb': quantized_size / (1024 * 1024),
            'compression_ratio': compression_ratio,
            'size_reduction': f"{compression_ratio * 100:.2f}%"
        }
    
    def compare_models(
        self,
        test_loader: torch.utils.data.DataLoader,
        metric_fn: Callable = None
    ) -> Dict[str, Any]:
        """比较原始模型和量化模型"""
        if self.quantized_model is None:
            raise ValueError("尚未进行量化")
        
        if metric_fn is None:
            metric_fn = lambda pred, target: (pred.argmax(dim=1) == target).float().mean()
        
        # 测试原始模型
        original_metric = self._evaluate_model(self.original_model, test_loader, metric_fn)
        
        # 测试量化模型
        quantized_metric = self._evaluate_model(self.quantized_model, test_loader, metric_fn)
        
        return {
            'original_metric': original_metric,
            'quantized_metric': quantized_metric,
            'metric_degradation': original_metric - quantized_metric,
            'relative_degradation': (original_metric - quantized_metric) / original_metric * 100
        }
    
    def _evaluate_model(
        self,
        model: nn.Module,
        test_loader: torch.utils.data.DataLoader,
        metric_fn: Callable
    ) -> float:
        """评估模型"""
        model.eval()
        total_metric = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                output = model(data)
                metric = metric_fn(output, target)
                total_metric += metric.item()
                num_batches += 1
        
        return total_metric / num_batches if num_batches > 0 else 0.0
    
    def get_quantization_stats(self) -> Dict[str, Any]:
        """获取量化统计"""
        return self.quantization_stats
    
    def save_quantized_model(self, path: str):
        """保存量化模型"""
        if self.quantized_model is None:
            raise ValueError("尚未进行量化")
        
        torch.save({
            'model_state_dict': self.quantized_model.state_dict(),
            'quantization_stats': self.quantization_stats
        }, path)
        logger.info(f"量化模型已保存到: {path}")


if __name__ == "__main__":
    # 测试模型量化
    from optimized_cnn_lstm import create_optimized_model
    
    config = {'input_size': 3, 'hidden_size': 64, 'num_classes': 1000}
    model = create_optimized_model(config)
    
    quantizer = ModelQuantization(model)
    
    # 测试动态量化
    quantized_model = quantizer.dynamic_quantization()
    
    print("量化统计:")
    for method, stats in quantizer.get_quantization_stats().items():
        print(f"{method}: {stats['size_reduction']} 减少")
