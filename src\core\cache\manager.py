"""
缓存管理器

协调多层缓存后端的统一管理器。
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

from .interface import CacheInterface
from .memory import MemoryCache
from .file import FileCache

logger = logging.getLogger(__name__)

# 尝试导入Redis缓存
try:
    from .redis import RedisCache
    HAS_REDIS = True
except ImportError:
    RedisCache = None
    HAS_REDIS = False


class CacheManager:
    """多层缓存管理器"""
    
    def __init__(self, backends: List[CacheInterface]):
        """
        初始化缓存管理器
        
        Args:
            backends: 缓存后端列表，按优先级排序
        """
        self.backends = backends
        self.logger = logger
        
        # 统计信息
        self._stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'backend_hits': {backend.name: 0 for backend in backends}
        }
    
    async def get(self, key: str) -> Optional[Any]:
        """从多层缓存中获取值"""
        self._stats['total_requests'] += 1
        
        for i, backend in enumerate(self.backends):
            try:
                value = await backend.get(key)
                if value is not None:
                    self._stats['cache_hits'] += 1
                    self._stats['backend_hits'][backend.name] += 1
                    
                    # 回填到更高优先级的缓存中
                    await self._backfill(key, value, i)
                    return value
            except Exception as e:
                self.logger.warning(f"Cache backend {backend.name} error: {e}")
                continue
        
        self._stats['cache_misses'] += 1
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置值到所有缓存后端"""
        success_count = 0
        
        for backend in self.backends:
            try:
                if await backend.set(key, value, ttl):
                    success_count += 1
            except Exception as e:
                self.logger.warning(f"Cache backend {backend.name} set error: {e}")
                continue
        
        return success_count > 0
    
    async def delete(self, key: str) -> bool:
        """从所有缓存后端删除值"""
        success_count = 0
        
        for backend in self.backends:
            try:
                if await backend.delete(key):
                    success_count += 1
            except Exception as e:
                self.logger.warning(f"Cache backend {backend.name} delete error: {e}")
                continue
        
        return success_count > 0
    
    async def clear(self) -> bool:
        """清空所有缓存后端"""
        success_count = 0
        
        for backend in self.backends:
            try:
                if await backend.clear():
                    success_count += 1
            except Exception as e:
                self.logger.warning(f"Cache backend {backend.name} clear error: {e}")
                continue
        
        return success_count == len(self.backends)
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        backend_stats = {}
        
        for backend in self.backends:
            try:
                backend_stats[backend.name] = await backend.get_stats()
            except Exception as e:
                self.logger.warning(f"Cache backend {backend.name} stats error: {e}")
                backend_stats[backend.name] = {"error": str(e)}
        
        total_requests = self._stats['total_requests']
        hit_rate = self._stats['cache_hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'manager_stats': {
                'total_requests': total_requests,
                'cache_hits': self._stats['cache_hits'],
                'cache_misses': self._stats['cache_misses'],
                'hit_rate': round(hit_rate, 4),
                'backend_hits': self._stats['backend_hits']
            },
            'backend_stats': backend_stats
        }
    
    async def _backfill(self, key: str, value: Any, start_index: int):
        """回填值到更高优先级的缓存"""
        for i in range(start_index):
            try:
                await self.backends[i].set(key, value)
            except Exception as e:
                self.logger.warning(f"Backfill to {self.backends[i].name} failed: {e}")
                continue


def create_cache_manager(config: Dict[str, Any]) -> CacheManager:
    """
    根据配置创建缓存管理器
    
    Args:
        config: 缓存配置
        
    Returns:
        缓存管理器实例
    """
    backends = []
    
    # 根据配置创建缓存后端
    enabled_backends = config.get('cache_levels', ['memory', 'file'])
    
    for backend_name in enabled_backends:
        if backend_name == 'memory' and config.get('memory_enabled', True):
            memory_config = config.get('memory', {})
            backends.append(MemoryCache(
                max_size=memory_config.get('max_size', 500),
                default_ttl=memory_config.get('ttl', 1800)
            ))
        
        elif backend_name == 'redis' and config.get('redis_enabled', False) and HAS_REDIS:
            redis_config = config.get('redis', {})
            backends.append(RedisCache(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                default_ttl=redis_config.get('ttl', 7200)
            ))
        
        elif backend_name == 'file' and config.get('file_enabled', True):
            file_config = config.get('file', {})
            backends.append(FileCache(
                cache_dir=file_config.get('cache_dir', 'data/cache'),
                default_ttl=file_config.get('ttl', 86400),
                max_size=file_config.get('max_size', 100)
            ))
    
    if not backends:
        # 如果没有配置任何后端，使用默认的内存缓存
        backends.append(MemoryCache())
    
    return CacheManager(backends)
