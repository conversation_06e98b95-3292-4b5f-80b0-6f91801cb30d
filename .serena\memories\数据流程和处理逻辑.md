# 福彩3D预测系统数据流程和处理逻辑

## 数据源和格式
### 主要数据源
- **URL**: https://data.17500.cn/3d_asc.txt
- **格式**: 文本文件，每行一条记录
- **字段数**: 13个字段
- **数据量**: 8,341条历史记录(2002-2025年)

### 数据字段结构
```
期号 | 日期 | 正式开奖号码(3位) | 试机号码(3位) | 开奖机器号 | 试机机器号 | 销售额 | 直选奖金 | 组三奖金 | 组六奖金 | 未知字段1 | 未知字段2 | 未知字段3
```

### 数据模型 (LotteryRecord)
```python
class LotteryRecord:
    period: str          # 期号 (如: 2025186)
    date: str           # 开奖日期
    winning_numbers: str # 正式开奖号码 (如: 123)
    trial_numbers: str  # 试机号码
    machine_id: str     # 开奖机器号
    trial_machine_id: str # 试机机器号
    sales_amount: float # 销售额
    direct_prize: int   # 直选奖金 (1040元)
    group3_prize: int   # 组三奖金 (346元)
    group6_prize: int   # 组六奖金 (173元)
    # 扩展字段支持完整13字段解析
```

## 数据采集流程
### 1. 自动采集 (LotteryDataCollector)
```python
# 定时任务: 每晚21:30自动执行
def collect_lottery_data():
    1. 发送HTTP请求到数据源
    2. 处理429错误(反爬虫机制)
    3. 实现重试和延迟策略
    4. 返回原始文本数据
```

### 2. 数据解析 (DataParser)
```python
def parse_lottery_data(raw_data):
    1. 按行分割文本数据
    2. 字段分割和类型转换
    3. 数据格式验证
    4. 生成LotteryRecord对象
    5. 返回解析结果和质量报告
```

### 3. 数据清洗 (DataCleaner)
```python
def clean_lottery_data(records):
    1. 去除重复记录
    2. 修正格式错误
    3. 填补缺失值
    4. 标准化数据格式
    5. 生成清洗统计报告
```

### 4. 质量检查 (QualityChecker)
```python
def check_data_quality(records):
    1. 完整性检查(字段完整性)
    2. 一致性检查(格式一致性)
    3. 准确性检查(数值范围)
    4. 时效性检查(数据新鲜度)
    5. 生成质量评分和报告
```

## 数据存储架构
### 主数据库 (SQLite)
```sql
-- 主表: lottery_records
CREATE TABLE lottery_records (
    id INTEGER PRIMARY KEY,
    period TEXT UNIQUE,
    date TEXT,
    winning_numbers TEXT,
    trial_numbers TEXT,
    machine_id TEXT,
    trial_machine_id TEXT,
    sales_amount REAL,
    direct_prize INTEGER,
    group3_prize INTEGER,
    group6_prize INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 预测记录表
CREATE TABLE prediction_records (
    id INTEGER PRIMARY KEY,
    period TEXT,
    model_name TEXT,
    prediction TEXT,
    confidence REAL,
    created_at TIMESTAMP
);
```

### 缓存系统 (Redis + JSON)
```python
# 缓存策略
cache_keys = {
    "latest_prediction": "cache:prediction:latest",
    "quality_history": "cache:quality:history", 
    "fusion_state": "cache:fusion:state"
}
```

## 数据处理引擎
### Polars引擎 (高性能)
```python
class PolarsEngine:
    def __init__(self):
        self.df = None  # Polars DataFrame
    
    def load_data(self):
        # 毫秒级数据加载
        
    def frequency_analysis(self):
        # 高速频率统计
        
    def trend_analysis(self):
        # 趋势分析计算
```

### 传统Pandas引擎 (兼容性)
```python
class DatabaseManager:
    def get_basic_stats(self):
        # 基础统计信息
        
    def get_frequency_analysis(self):
        # 频率分析
        
    def get_sum_distribution(self):
        # 和值分布分析
```

## 数据更新机制
### 增量更新 (IncrementalUpdater)
```python
def perform_update():
    1. 检查最新期号
    2. 对比本地数据
    3. 获取增量数据
    4. 验证数据质量
    5. 更新数据库
    6. 刷新缓存
    7. 触发模型重训练
```

### 更新策略
- **时间触发**: 每晚21:30自动更新
- **手动触发**: 界面手动刷新按钮
- **API触发**: RESTful API端点
- **增量更新**: 只更新新增数据
- **全量更新**: 重新获取所有数据

## 数据验证和一致性
### 一致性检查 (ConsistencyChecker)
```python
def check_consistency():
    1. 期号连续性检查
    2. 日期逻辑性检查
    3. 号码格式检查
    4. 奖金数额验证
    5. 跨表数据一致性
```

### 数据完整性保证
- **事务处理**: 原子性数据更新
- **备份机制**: 自动数据备份
- **回滚功能**: 错误数据回滚
- **版本控制**: 数据版本管理

## 性能优化策略
### 查询优化
- **索引策略**: 期号、日期字段索引
- **分页查询**: 大数据集分页处理
- **缓存机制**: 热点数据缓存
- **连接池**: 数据库连接复用

### 内存管理
- **懒加载**: 按需加载数据
- **数据分片**: 大数据集分片处理
- **垃圾回收**: 及时释放内存
- **批量处理**: 批量数据操作