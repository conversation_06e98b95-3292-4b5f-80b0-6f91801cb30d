#!/usr/bin/env python3
"""
配置系统集成测试
"""

from src.core.config.settings import Settings
from src.bug_detection.core.config import BugDetectionConfig

def test_config_integration():
    print("🔧 配置系统集成测试")
    
    # 测试主配置系统
    settings = Settings()
    print(f"✅ 主配置系统: {settings.project_name}")
    print(f"✅ 数据库URL: {settings.get_database_url()}")
    print(f"✅ API地址: {settings.api.host}:{settings.api.port}")
    
    # 测试Bug检测配置
    bug_config = BugDetectionConfig(settings)
    print(f"✅ Bug检测数据库: {bug_config.database['path']}")
    print(f"✅ 日志级别: {bug_config.logging['level']}")
    
    print("🎉 配置系统集成测试通过！")

if __name__ == "__main__":
    test_config_integration()
