"""
融合模块基类

定义所有融合模块的通用接口和基础功能。
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from .fusion_context import FusionContext

logger = logging.getLogger(__name__)


class BaseFusionModule(ABC):
    """融合模块基类"""
    
    def __init__(self, context: FusionContext, module_name: str):
        """
        初始化融合模块
        
        Args:
            context: 共享上下文
            module_name: 模块名称
        """
        self.context = context
        self.module_name = module_name
        self.logger = logging.getLogger(f"{__name__}.{module_name}")
        self._initialized = False
        
    def initialize(self) -> bool:
        """
        初始化模块
        
        Returns:
            是否初始化成功
        """
        try:
            if not self.context.is_ready():
                self.logger.warning("上下文未就绪，跳过初始化")
                return False
                
            self._do_initialize()
            self._initialized = True
            self.logger.info(f"{self.module_name} 模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"{self.module_name} 模块初始化失败: {e}")
            return False
    
    @abstractmethod
    def _do_initialize(self):
        """子类实现具体的初始化逻辑"""
        pass
    
    def is_ready(self) -> bool:
        """检查模块是否就绪"""
        return self._initialized and self.context.is_ready()
    
    def get_status(self) -> Dict[str, Any]:
        """获取模块状态信息"""
        return {
            "module_name": self.module_name,
            "initialized": self._initialized,
            "context_ready": self.context.is_ready(),
            "ready": self.is_ready()
        }
    
    def _validate_input(self, data: Any, required_fields: List[str] = None) -> bool:
        """
        验证输入数据
        
        Args:
            data: 输入数据
            required_fields: 必需字段列表
            
        Returns:
            是否验证通过
        """
        if data is None:
            self.logger.error("输入数据为空")
            return False
            
        if required_fields and isinstance(data, dict):
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                self.logger.error(f"缺少必需字段: {missing_fields}")
                return False
                
        return True
    
    def _handle_error(self, operation: str, error: Exception) -> None:
        """
        统一错误处理
        
        Args:
            operation: 操作名称
            error: 异常对象
        """
        self.logger.error(f"{self.module_name} 执行 {operation} 时发生错误: {error}")
        
    def _log_performance(self, operation: str, duration: float, details: Dict[str, Any] = None):
        """
        记录性能信息
        
        Args:
            operation: 操作名称
            duration: 执行时间(秒)
            details: 详细信息
        """
        details_str = f", 详情: {details}" if details else ""
        self.logger.info(f"{self.module_name} {operation} 耗时: {duration:.3f}秒{details_str}")
        
    def cleanup(self):
        """清理资源"""
        self._initialized = False
        self.logger.info(f"{self.module_name} 模块资源清理完成")
