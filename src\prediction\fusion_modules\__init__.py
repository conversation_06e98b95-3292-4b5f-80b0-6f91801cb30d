"""
智能融合系统模块化架构

将原有的巨型IntelligentFusionSystem类拆分为多个专门模块，
提高代码可维护性和可扩展性。
"""

from .fusion_context import FusionContext
from .base_module import BaseFusionModule
from .trend_analysis_module import TrendAnalysisModule
from .pattern_prediction_module import PatternPredictionModule
from .lstm_prediction_module import LSTMPredictionModule
from .fusion_engine_module import FusionEngineModule
from .performance_tracker_module import PerformanceTrackerModule
from .validation_module import ValidationModule
from .intelligent_fusion_controller import IntelligentFusionController

__all__ = [
    'FusionContext',
    'BaseFusionModule',
    'TrendAnalysisModule',
    'PatternPredictionModule', 
    'LSTMPredictionModule',
    'FusionEngineModule',
    'PerformanceTrackerModule',
    'ValidationModule',
    'IntelligentFusionController'
]
