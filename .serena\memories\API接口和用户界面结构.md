# 福彩3D预测系统API接口和用户界面结构

## API服务架构
### 主API服务 (production_main.py)
- **服务地址**: http://127.0.0.1:8888
- **框架**: FastAPI 0.104+
- **文档**: http://127.0.0.1:8888/docs (Swagger UI)
- **健康检查**: http://127.0.0.1:8888/health

### 核心API端点
#### 1. 健康检查和状态
```python
GET /health                    # 基础健康检查
GET /health/detailed          # 详细健康检查
GET /health/summary           # 健康状态摘要
GET /health/component/{name}  # 组件健康检查
```

#### 2. 数据查询API
```python
GET /basic-stats              # 基础统计信息
GET /frequency-analysis       # 频率分析
GET /sum-distribution         # 和值分布
GET /sales-analysis          # 销售分析
POST /query-data             # 自定义数据查询
GET /trends-analysis         # 趋势分析
```

#### 3. 预测相关API
```python
GET /predictions/single-best     # 单一最佳预测
GET /predictions/ranking         # 预测排名
GET /predictions/model-performance # 模型性能
POST /predictions/track-result   # 跟踪预测结果
POST /predictions/save          # 保存预测结果
GET /predictions/trend-analysis # 趋势预测分析
```

#### 4. 数据管理API
```python
GET /data/update-status       # 更新状态
POST /data/trigger-update     # 触发更新
GET /data/update-history      # 更新历史
GET /data/update-progress     # 更新进度
POST /data/refresh           # 刷新数据
GET /data/status             # 数据状态
```

#### 5. 模型库API
```python
GET /models                   # 获取模型列表
GET /models/{model_id}        # 获取模型详情
GET /models/{model_id}/status # 模型状态
POST /models/{model_id}/predict # 单模型预测
POST /models/predict-combined # 组合模型预测
GET /models/{model_id}/performance # 模型性能
```

#### 6. Bug检测API
```python
POST /bug-detection/js-error    # JavaScript错误报告
POST /bug-detection/batch-errors # 批量错误报告
GET /bug-detection/monitoring   # 监控状态
GET /bug-detection/statistics   # 统计信息
```

### WebSocket端点
```python
/ws/bug-detection           # Bug检测实时通信
/ws/realtime-stats         # 实时统计推送
/ws/training-monitor       # 训练监控
```

## 用户界面架构
### 主界面 (src/ui/main.py)
- **框架**: Streamlit 1.28+
- **访问地址**: http://127.0.0.1:8501
- **响应式设计**: 支持多设备访问
- **实时更新**: WebSocket实时数据推送

### 界面组件结构
#### 1. 导航组件 (components/navigation.py)
```python
class NavigationComponent:
    - 侧边栏导航菜单
    - 页面路由管理
    - 用户偏好设置
    - 快捷操作按钮
```

#### 2. 页面管理器 (components/page_manager.py)
```python
class PageManager:
    - 页面状态管理
    - 组件生命周期
    - 错误边界处理
    - 性能监控
```

#### 3. 错误处理组件
```python
# components/error_handler.py
class SmartErrorHandler:
    - 智能错误恢复
    - 用户友好提示
    - 错误日志记录
    - 降级策略

# components/fallback_manager.py  
class FallbackManager:
    - 服务降级管理
    - 离线模式支持
    - 缓存数据展示
```

### 功能页面详解
#### 1. 数据概览页面
```python
def show_overview_page():
    - 最新开奖结果横幅
    - 数据库统计信息
    - 数据质量指标
    - 系统健康状态
    - 快速操作面板
```

#### 2. 频率分析页面
```python
def show_frequency_page():
    - 号码出现频率统计
    - 位置频率分析
    - 热号冷号分析
    - 交互式图表展示
    - 数据导出功能
```

#### 3. 预测分析页面
```python
def show_prediction_page():
    - 预测控制面板
    - 单一最佳预测
    - 预测排名列表
    - 置信度分析
    - 历史预测对比
```

#### 4. 智能融合页面
```python
def show_intelligent_fusion_page():
    - 趋势分析标签页
    - 模式预测标签页
    - 自适应融合标签页
    - 性能指标展示
    - 模型权重可视化
```

#### 5. 数据管理页面
```python
def show_data_management_page():
    - 数据状态监控
    - 更新操作面板
    - 调度器控制
    - 更新历史记录
    - 数据源状态
```

#### 6. 模型库页面
```python
def show_model_library_page():
    - 模型概览
    - 模型详情
    - 预测执行
    - 性能分析
    - 模型管理
```

#### 7. Bug检测状态页面
```python
def show_bug_detection_status():
    - 系统概览
    - Bug统计
    - 性能监控
    - JavaScript监控
    - AI分析面板
```

### 交互式组件
#### 1. 图表组件 (components/interactive_charts.py)
```python
class InteractiveChartController:
    - Plotly交互式图表
    - 实时数据更新
    - 多维度分析
    - 导出功能

class RealTimeChart:
    - 实时数据流
    - 动态更新
    - 性能优化
```

#### 2. 预测组件 (components/prediction_components.py)
```python
def prediction_control_panel():    # 预测控制面板
def prediction_result_card():      # 预测结果卡片
def ranking_table():               # 排名表格
def model_performance_chart():     # 模型性能图表
def confidence_gauge():            # 置信度仪表盘
```

#### 3. WebSocket客户端 (components/websocket_client.py)
```python
class StreamlitWebSocketClient:
    - 实时连接管理
    - 消息处理
    - 状态同步
    - 错误恢复
```

### 界面特性
#### 1. 响应式设计
- **多列布局**: 自适应屏幕尺寸
- **移动友好**: 支持触摸操作
- **主题支持**: 明暗主题切换
- **国际化**: 中文界面优化

#### 2. 用户体验优化
- **加载状态**: 优雅的加载动画
- **错误提示**: 友好的错误信息
- **操作反馈**: 实时操作状态
- **快捷键**: 键盘快捷操作

#### 3. 性能优化
- **懒加载**: 按需加载组件
- **缓存策略**: 智能数据缓存
- **分页显示**: 大数据集分页
- **异步加载**: 非阻塞数据获取

### 启动方式
#### 1. 一键启动脚本
```bash
# 一键启动.bat
start cmd /k "python start_api.py"
timeout /t 5
streamlit run src/ui/main.py --server.port 8501
```

#### 2. 分别启动
```bash
# API服务
python start_api.py

# Streamlit界面  
streamlit run src/ui/main.py --server.port 8501
```

#### 3. 开发模式
```bash
# 开发环境启动
uvicorn src.api.production_main:app --host 127.0.0.1 --port 8888 --reload
```