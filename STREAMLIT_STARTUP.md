# Streamlit界面启动指南

## 🎯 标准启动方式 (推荐)

经过测试对比，确定使用以下标准方式启动Streamlit界面：

### 基本启动命令
```bash
python -m streamlit run src/ui/main.py
```

### 带环境变量的启动命令 (推荐)
```bash
# Windows
set PYTHONPATH=src && python -m streamlit run src/ui/main.py

# Linux/Mac
export PYTHONPATH=src && python -m streamlit run src/ui/main.py
```

## 📋 启动参数说明

| 参数 | 值 | 说明 |
|------|-----|------|
| `--server.port` | 8501 | Streamlit服务端口 |
| `--server.address` | 127.0.0.1 | 绑定到本地地址 |
| `--browser.gatherUsageStats` | false | 禁用使用统计收集 |
| `PYTHONPATH` | src | Python模块搜索路径 |

## 🔧 启动前准备

### 1. 确保虚拟环境已激活
```bash
# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 2. 确保API服务已启动
```bash
python start_production_api.py
```
API服务地址: http://127.0.0.1:8888

### 3. 检查依赖包
```bash
pip install streamlit plotly pandas numpy requests websockets
```

## 🌐 访问地址

启动成功后，访问以下地址：
- **Streamlit界面**: http://127.0.0.1:8501
- **API服务**: http://127.0.0.1:8888
- **API文档**: http://127.0.0.1:8888/docs

## ⚠️ 故障排除

### 问题1: 模块导入错误
**解决方案**: 确保设置了PYTHONPATH环境变量
```bash
set PYTHONPATH=src
```

### 问题2: 端口被占用
**解决方案**: 更改端口或停止占用进程
```bash
# 查看端口占用
netstat -an | findstr :8501

# 使用其他端口（如需要）
python -m streamlit run src/ui/main.py --server.port=8502
```

### 问题3: API连接失败
**解决方案**: 确保API服务正在运行
```bash
# 检查API服务状态
curl http://127.0.0.1:8888/health

# 启动API服务
python start_production_api.py
```

### 问题4: WebSocket连接失败
**解决方案**: 检查WebSocket端点
```bash
# WebSocket端点
ws://127.0.0.1:8888/ws/prediction-results
```

## 📝 启动脚本说明

### start_streamlit.py
- **状态**: 已修改为启动说明脚本
- **功能**: 显示启动说明和可选的自动启动
- **问题**: subprocess输出重定向问题导致日志不可见

### 直接命令启动
- **状态**: ✅ 推荐使用
- **优点**: 
  - 输出日志完全可见
  - 标准Streamlit启动方式
  - 易于调试和故障排除
  - 无中间层干扰

## 🎯 最终决定

**确定使用**: `python -m streamlit run src/ui/main.py` 方式启动

**原因**:
1. ✅ 测试验证有效
2. ✅ 官方标准方式
3. ✅ 输出日志可见
4. ✅ 易于调试
5. ✅ 无subprocess问题

**禁用**: `python start_streamlit.py` 作为主要启动方式
- 保留作为启动说明工具
- 不再作为主要启动方式推荐

## 🚀 快速启动

```bash
# 1. 激活虚拟环境
venv\Scripts\activate

# 2. 启动API服务 (新终端)
python start_production_api.py

# 3. 启动Streamlit界面 (新终端)
python -m streamlit run src/ui/main.py
```

## 📊 性能监控

启动后可以监控以下指标：
- Streamlit响应时间
- API调用延迟
- WebSocket连接状态
- 内存使用情况

---

**更新时间**: 2025-08-01  
**测试状态**: ✅ 已验证  
**推荐方式**: 直接命令启动
