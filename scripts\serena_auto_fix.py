#!/usr/bin/env python3
"""
serena MCP服务器自动修复脚本
用于自动诊断和修复serena MCP服务器的常见问题
"""

import subprocess
import time
import sys
import os
import requests
from pathlib import Path

class SerenaAutoFixer:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.venv_path = self.project_root / "venv"
        self.serena_exe = self.venv_path / "Scripts" / "serena-mcp-server.exe"
        self.dashboard_url = "http://127.0.0.1:24282/dashboard/index.html"
        
    def kill_existing_serena_processes(self):
        """终止现有的serena进程"""
        print("🔄 终止现有serena进程...")
        try:
            subprocess.run([
                "taskkill", "/F", "/IM", "serena-mcp-server.exe"
            ], capture_output=True)
            print("  ✅ 已终止现有serena进程")
            time.sleep(2)  # 等待进程完全终止
            return True
        except Exception as e:
            print(f"  ⚠️  终止进程时出错: {e}")
            return False
    
    def install_pyright_if_missing(self):
        """如果pyright缺失则安装"""
        print("🔄 检查并安装pyright依赖...")
        try:
            # 检查系统Python中是否有pyright
            result = subprocess.run([
                "C:\\Program Files\\Python311\\python.exe", 
                "-c", 
                "import pyright.langserver"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("  ✅ pyright已存在")
                return True
            else:
                print("  🔄 安装pyright到系统Python...")
                install_result = subprocess.run([
                    "C:\\Program Files\\Python311\\python.exe", 
                    "-m", "pip", "install", "pyright"
                ], capture_output=True, text=True)
                
                if install_result.returncode == 0:
                    print("  ✅ pyright安装成功")
                    return True
                else:
                    print(f"  ❌ pyright安装失败: {install_result.stderr}")
                    return False
        except Exception as e:
            print(f"  ❌ 安装pyright时出错: {e}")
            return False
    
    def start_serena_server(self):
        """启动serena MCP服务器"""
        print("🚀 启动serena MCP服务器...")
        try:
            # 使用绝对路径启动serena
            cmd = [
                str(self.serena_exe),
                "--project", str(self.project_root),
                "--enable-web-dashboard", "true",
                "--host", "127.0.0.1",
                "--port", "24282"
            ]
            
            print(f"  执行命令: {' '.join(cmd)}")
            
            # 在后台启动serena
            process = subprocess.Popen(
                cmd,
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            
            print(f"  ✅ serena服务器已启动 (PID: {process.pid})")
            
            # 等待服务器启动
            print("  ⏳ 等待服务器启动...")
            for i in range(30):  # 最多等待30秒
                time.sleep(1)
                try:
                    response = requests.get(self.dashboard_url, timeout=2)
                    if response.status_code == 200:
                        print(f"  ✅ serena仪表板可访问: {self.dashboard_url}")
                        return True
                except:
                    continue
                
                if i % 5 == 0:
                    print(f"    等待中... ({i+1}/30秒)")
            
            print("  ⚠️  服务器启动超时，但进程已创建")
            return False
            
        except Exception as e:
            print(f"  ❌ 启动serena服务器失败: {e}")
            return False
    
    def verify_augment_connection(self):
        """验证Augment连接（提供指导）"""
        print("🔗 验证Augment MCP连接...")
        print("  ℹ️  请在Augment中验证以下配置:")
        print("     - MCP服务器: serena")
        print("     - 命令: d:/github/3dyuce/venv/Scripts/serena-mcp-server.exe")
        print("     - 参数: --project d:/github/3dyuce")
        print("     - 状态: 应显示为绿色/连接")
        print("  ℹ️  如果连接失败，请重启Cursor并重新配置MCP")
        return True
    
    def open_dashboard(self):
        """打开serena仪表板"""
        print("🌐 打开serena仪表板...")
        try:
            subprocess.run(["start", self.dashboard_url], shell=True)
            print(f"  ✅ 已在浏览器中打开: {self.dashboard_url}")
            return True
        except Exception as e:
            print(f"  ❌ 打开仪表板失败: {e}")
            return False
    
    def run_auto_fix(self):
        """运行自动修复流程"""
        print("🔧 开始serena MCP服务器自动修复...")
        print("=" * 50)
        
        steps = [
            ("终止现有进程", self.kill_existing_serena_processes),
            ("安装pyright依赖", self.install_pyright_if_missing),
            ("启动serena服务器", self.start_serena_server),
            ("验证Augment连接", self.verify_augment_connection),
            ("打开仪表板", self.open_dashboard),
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 步骤: {step_name}")
            try:
                success = step_func()
                if not success and step_name in ["启动serena服务器"]:
                    print(f"  ⚠️  关键步骤失败: {step_name}")
                    break
            except Exception as e:
                print(f"  ❌ 步骤执行失败: {e}")
        
        print("\n" + "=" * 50)
        print("🎯 自动修复完成!")
        print("\n📋 后续步骤:")
        print("1. 重启Cursor IDE")
        print("2. 在Augment中检查serena MCP连接状态")
        print("3. 运行健康检查: python scripts/serena_health_check.py")
        print(f"4. 访问仪表板: {self.dashboard_url}")
        
        return True

def main():
    """主函数"""
    fixer = SerenaAutoFixer()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--start-only":
        fixer.start_serena_server()
    else:
        fixer.run_auto_fix()

if __name__ == "__main__":
    main()
