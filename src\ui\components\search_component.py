"""
搜索组件
提供全局搜索、智能建议和快速导航功能
"""

import streamlit as st
from typing import Dict, List, Optional, Tuple, Any
import re
from datetime import datetime
import json
from difflib import SequenceMatcher
from collections import defaultdict


class SearchComponent:
    """搜索组件"""
    
    def __init__(self):
        """初始化搜索组件"""
        self.search_index = self._build_search_index()
        self.search_history = self._load_search_history()
        self.popular_searches = self._get_popular_searches()
        self._inject_search_styles()
    
    def _inject_search_styles(self):
        """注入搜索组件样式"""
        search_css = """
        <style>
        /* 搜索容器 */
        .search-container {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            border-radius: 20px;
            padding: 24px;
            margin: 16px 0;
            border: 1px solid rgba(255,255,255,0.15);
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        /* 搜索框 */
        .search-input-container {
            position: relative;
            margin-bottom: 20px;
        }
        
        .search-input {
            width: 100%;
            padding: 16px 50px 16px 20px;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 16px;
            background: rgba(255,255,255,0.08);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .search-input:focus {
            outline: none;
            border-color: #4ade80;
            background: rgba(255,255,255,0.12);
            box-shadow: 0 0 0 4px rgba(74, 222, 128, 0.1);
            transform: translateY(-1px);
        }
        
        .search-input::placeholder {
            color: rgba(255,255,255,0.6);
        }
        
        .search-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255,255,255,0.6);
            font-size: 20px;
            pointer-events: none;
        }
        
        /* 搜索建议 */
        .search-suggestions {
            background: rgba(255,255,255,0.08);
            border: 1px solid rgba(255,255,255,0.15);
            border-radius: 12px;
            margin-top: 8px;
            max-height: 300px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }
        
        .suggestion-item {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .suggestion-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(4px);
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
        
        .suggestion-icon {
            font-size: 16px;
            min-width: 20px;
        }
        
        .suggestion-text {
            color: white;
            font-size: 14px;
            font-weight: 500;
            flex: 1;
        }
        
        .suggestion-category {
            color: rgba(255,255,255,0.6);
            font-size: 11px;
            background: rgba(255,255,255,0.1);
            padding: 2px 8px;
            border-radius: 8px;
        }
        
        .suggestion-match {
            background: rgba(74, 222, 128, 0.3);
            color: #4ade80;
            padding: 1px 3px;
            border-radius: 3px;
        }
        
        /* 搜索历史 */
        .search-history {
            margin-top: 16px;
        }
        
        .search-history-header {
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .history-item {
            display: inline-block;
            background: rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.8);
            padding: 6px 12px;
            margin: 4px 8px 4px 0;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .history-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        /* 热门搜索 */
        .popular-searches {
            margin-top: 16px;
        }
        
        .popular-item {
            display: inline-block;
            background: linear-gradient(135deg, rgba(74, 222, 128, 0.2), rgba(34, 197, 94, 0.2));
            color: #4ade80;
            padding: 8px 14px;
            margin: 4px 8px 4px 0;
            border-radius: 18px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(74, 222, 128, 0.3);
        }
        
        .popular-item:hover {
            background: linear-gradient(135deg, rgba(74, 222, 128, 0.3), rgba(34, 197, 94, 0.3));
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(74, 222, 128, 0.2);
        }
        
        /* 搜索结果 */
        .search-results {
            margin-top: 20px;
        }
        
        .result-item {
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 16px;
            margin: 12px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .result-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .result-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }
        
        .result-icon {
            font-size: 20px;
        }
        
        .result-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            flex: 1;
        }
        
        .result-category {
            background: rgba(74, 222, 128, 0.2);
            color: #4ade80;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .result-description {
            color: rgba(255,255,255,0.7);
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: rgba(255,255,255,0.5);
        }
        
        /* 快捷键提示 */
        .search-shortcuts {
            margin-top: 16px;
            padding: 12px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .shortcut-item {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 4px 12px 4px 0;
            color: rgba(255,255,255,0.7);
            font-size: 12px;
        }
        
        .shortcut-key {
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 10px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-container {
                padding: 16px;
                margin: 12px 0;
            }
            
            .search-input {
                padding: 14px 40px 14px 16px;
                font-size: 14px;
            }
            
            .result-item {
                padding: 12px;
            }
        }
        </style>
        """
        
        st.markdown(search_css, unsafe_allow_html=True)
    
    def _build_search_index(self) -> Dict[str, Any]:
        """构建搜索索引"""
        pages = {
            "📈 数据概览": {
                "category": "🏠 首页概览",
                "description": "查看福彩3D历史数据的总体概览和统计信息",
                "keywords": ["数据", "概览", "统计", "总览", "dashboard"],
                "url": "/data_overview"
            },
            "🎲 最新开奖": {
                "category": "🏠 首页概览", 
                "description": "查看最新的福彩3D开奖结果和试机号",
                "keywords": ["开奖", "最新", "结果", "试机号", "latest"],
                "url": "/latest_draw"
            },
            "📊 系统状态": {
                "category": "🏠 首页概览",
                "description": "监控系统运行状态和性能指标",
                "keywords": ["系统", "状态", "监控", "性能", "status"],
                "url": "/system_status"
            },
            "🤖 智能融合预测": {
                "category": "🎯 智能预测",
                "description": "使用多种算法融合进行智能预测分析",
                "keywords": ["智能", "融合", "预测", "算法", "AI"],
                "url": "/intelligent_fusion"
            },
            "📈 趋势分析预测": {
                "category": "🎯 智能预测",
                "description": "基于历史趋势进行预测分析",
                "keywords": ["趋势", "分析", "预测", "历史", "trend"],
                "url": "/trend_analysis"
            },
            "🎯 预测分析": {
                "category": "🎯 智能预测",
                "description": "核心预测功能和分析工具",
                "keywords": ["预测", "分析", "核心", "工具", "prediction"],
                "url": "/prediction_analysis"
            },
            "🏛️ 模型库管理": {
                "category": "🎯 智能预测",
                "description": "管理和配置预测模型",
                "keywords": ["模型", "管理", "配置", "库", "model"],
                "url": "/model_library"
            },
            "🔍 数据查询": {
                "category": "📊 数据分析",
                "description": "查询和筛选历史开奖数据",
                "keywords": ["查询", "筛选", "搜索", "数据", "query"],
                "url": "/data_query"
            },
            "📊 频率分析": {
                "category": "📊 数据分析",
                "description": "分析号码出现频率和规律",
                "keywords": ["频率", "分析", "号码", "规律", "frequency"],
                "url": "/frequency_analysis"
            },
            "💰 销售分析": {
                "category": "📊 数据分析",
                "description": "分析销售额和奖金分布",
                "keywords": ["销售", "分析", "奖金", "分布", "sales"],
                "url": "/sales_analysis"
            },
            "📈 和值分布": {
                "category": "📊 数据分析",
                "description": "分析和值的分布规律和趋势",
                "keywords": ["和值", "分布", "规律", "趋势", "sum"],
                "url": "/sum_distribution"
            },
            "🔄 数据更新": {
                "category": "🔧 系统管理",
                "description": "更新和同步最新的开奖数据",
                "keywords": ["更新", "同步", "数据", "刷新", "update"],
                "url": "/data_update"
            },
            "📊 性能监控": {
                "category": "🔧 系统管理",
                "description": "监控系统性能和运行状态",
                "keywords": ["性能", "监控", "系统", "运行", "performance"],
                "url": "/performance_monitoring"
            },
            "💡 优化建议": {
                "category": "🔧 系统管理",
                "description": "获取系统优化和改进建议",
                "keywords": ["优化", "建议", "改进", "提升", "optimization"],
                "url": "/optimization_suggestions"
            },
            "🤖 模型训练": {
                "category": "🔧 系统管理",
                "description": "训练和优化预测模型",
                "keywords": ["模型", "训练", "优化", "机器学习", "training"],
                "url": "/model_training"
            },
            "🔬 特征工程": {
                "category": "⚙️ 高级功能",
                "description": "进行特征提取和工程优化",
                "keywords": ["特征", "工程", "提取", "优化", "feature"],
                "url": "/feature_engineering"
            },
            "🧪 A/B测试": {
                "category": "⚙️ 高级功能",
                "description": "进行A/B测试和对比分析",
                "keywords": ["AB测试", "对比", "分析", "实验", "testing"],
                "url": "/ab_testing"
            },
            "📊 数据管理深度": {
                "category": "⚙️ 高级功能",
                "description": "深度数据管理和分析工具",
                "keywords": ["数据", "管理", "深度", "分析", "deep"],
                "url": "/data_management_deep"
            },
            "🔍 系统诊断": {
                "category": "⚙️ 高级功能",
                "description": "系统诊断和问题排查",
                "keywords": ["诊断", "排查", "问题", "调试", "debug"],
                "url": "/system_diagnosis"
            }
        }
        
        return pages
    
    def _load_search_history(self) -> List[str]:
        """加载搜索历史"""
        # 在实际应用中，这里应该从用户会话或数据库加载
        return ["数据概览", "预测分析", "频率分析", "开奖结果", "系统状态"]
    
    def _get_popular_searches(self) -> List[str]:
        """获取热门搜索"""
        return ["预测", "数据", "分析", "开奖", "趋势", "频率", "和值", "监控"]
    
    def search_pages(self, query: str) -> List[Dict[str, Any]]:
        """搜索页面"""
        if not query.strip():
            return []
        
        query_lower = query.lower().strip()
        results = []
        
        for page_name, page_info in self.search_index.items():
            score = 0
            
            # 页面名称匹配
            if query_lower in page_name.lower():
                score += 100
            
            # 描述匹配
            if query_lower in page_info['description'].lower():
                score += 50
            
            # 关键词匹配
            for keyword in page_info['keywords']:
                if query_lower in keyword.lower():
                    score += 30
            
            # 分类匹配
            if query_lower in page_info['category'].lower():
                score += 20
            
            # 模糊匹配
            for text in [page_name, page_info['description']] + page_info['keywords']:
                similarity = SequenceMatcher(None, query_lower, text.lower()).ratio()
                if similarity > 0.6:
                    score += int(similarity * 25)
            
            if score > 0:
                results.append({
                    'page': page_name,
                    'info': page_info,
                    'score': score,
                    'matched_text': self._highlight_matches(page_name + ' ' + page_info['description'], query)
                })
        
        # 按评分排序
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:10]  # 返回前10个结果
    
    def _highlight_matches(self, text: str, query: str) -> str:
        """高亮匹配的文本"""
        if not query.strip():
            return text
        
        # 简单的高亮实现
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        return pattern.sub(lambda m: f'<span class="suggestion-match">{m.group()}</span>', text)
    
    def get_search_suggestions(self, query: str) -> List[Dict[str, Any]]:
        """获取搜索建议"""
        if not query.strip():
            return []
        
        suggestions = []
        query_lower = query.lower().strip()
        
        # 页面名称建议
        for page_name, page_info in self.search_index.items():
            if query_lower in page_name.lower():
                suggestions.append({
                    'text': page_name,
                    'type': 'page',
                    'category': page_info['category'],
                    'icon': page_name.split(' ')[0] if ' ' in page_name else '📄'
                })
        
        # 关键词建议
        for page_name, page_info in self.search_index.items():
            for keyword in page_info['keywords']:
                if query_lower in keyword.lower() and keyword not in [s['text'] for s in suggestions]:
                    suggestions.append({
                        'text': keyword,
                        'type': 'keyword',
                        'category': page_info['category'],
                        'icon': '🔍'
                    })
        
        return suggestions[:8]  # 返回前8个建议
    
    def render_search_interface(self) -> Optional[str]:
        """渲染搜索界面"""
        search_html = '''
        <div class="search-container">
            <div class="search-input-container">
                <input type="text" class="search-input" placeholder="🔍 搜索功能、页面或关键词..." id="global-search">
                <span class="search-icon">⌕</span>
            </div>
            <div id="search-suggestions" class="search-suggestions" style="display: none;"></div>
            <div id="search-results" class="search-results" style="display: none;"></div>
        '''
        
        # 搜索历史
        if self.search_history:
            search_html += '''
            <div class="search-history">
                <div class="search-history-header">
                    <span>🕒</span>
                    <span>搜索历史</span>
                </div>
            '''
            for item in self.search_history[:5]:
                search_html += f'<span class="history-item" onclick="performSearch(\'{item}\')">{item}</span>'
            search_html += '</div>'
        
        # 热门搜索
        if self.popular_searches:
            search_html += '''
            <div class="popular-searches">
                <div class="search-history-header">
                    <span>🔥</span>
                    <span>热门搜索</span>
                </div>
            '''
            for item in self.popular_searches[:6]:
                search_html += f'<span class="popular-item" onclick="performSearch(\'{item}\')">{item}</span>'
            search_html += '</div>'
        
        # 快捷键提示
        search_html += '''
        <div class="search-shortcuts">
            <div class="shortcut-item">
                <span class="shortcut-key">Ctrl+K</span>
                <span>快速搜索</span>
            </div>
            <div class="shortcut-item">
                <span class="shortcut-key">Enter</span>
                <span>执行搜索</span>
            </div>
            <div class="shortcut-item">
                <span class="shortcut-key">Esc</span>
                <span>清空搜索</span>
            </div>
        </div>
        '''
        
        search_html += '</div>'
        
        # JavaScript功能
        search_html += '''
        <script>
        const searchInput = document.getElementById('global-search');
        const suggestionsDiv = document.getElementById('search-suggestions');
        const resultsDiv = document.getElementById('search-results');
        
        let searchTimeout;
        
        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();
            
            if (query.length === 0) {
                suggestionsDiv.style.display = 'none';
                resultsDiv.style.display = 'none';
                return;
            }
            
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        });
        
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch(e.target.value);
            } else if (e.key === 'Escape') {
                e.target.value = '';
                suggestionsDiv.style.display = 'none';
                resultsDiv.style.display = 'none';
            }
        });
        
        function performSearch(query) {
            if (!query.trim()) return;
            
            // 更新搜索框
            searchInput.value = query;
            
            // 模拟搜索结果
            showSearchResults(query);
            
            // 通知Streamlit
            window.parent.postMessage({
                type: 'streamlit:setComponentValue',
                value: {action: 'search', query: query}
            }, '*');
        }
        
        function showSearchResults(query) {
            // 这里应该调用实际的搜索API
            // 现在显示模拟结果
            resultsDiv.innerHTML = `
                <div class="result-item" onclick="navigateToPage('📈 数据概览')">
                    <div class="result-header">
                        <span class="result-icon">📈</span>
                        <span class="result-title">数据概览</span>
                        <span class="result-category">首页概览</span>
                    </div>
                    <div class="result-description">查看福彩3D历史数据的总体概览和统计信息</div>
                    <div class="result-meta">
                        <span>匹配度: 95%</span>
                        <span>最近访问: 2小时前</span>
                    </div>
                </div>
            `;
            resultsDiv.style.display = 'block';
            suggestionsDiv.style.display = 'none';
        }
        
        function navigateToPage(pageName) {
            window.parent.postMessage({
                type: 'streamlit:setComponentValue',
                value: {action: 'navigate', page: pageName}
            }, '*');
        }
        
        // 快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                searchInput.focus();
            }
        });
        </script>
        '''
        
        st.markdown(search_html, unsafe_allow_html=True)
        
        return None
    
    def add_to_search_history(self, query: str):
        """添加到搜索历史"""
        if query and query not in self.search_history:
            self.search_history.insert(0, query)
            self.search_history = self.search_history[:10]  # 保持最近10条
    
    def get_search_analytics(self) -> Dict[str, Any]:
        """获取搜索分析数据"""
        return {
            'total_searches': len(self.search_history),
            'popular_queries': self.popular_searches,
            'recent_queries': self.search_history[:5],
            'search_categories': list(set(info['category'] for info in self.search_index.values())),
            'total_pages': len(self.search_index)
        }
