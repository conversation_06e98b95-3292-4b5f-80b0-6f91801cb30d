#!/usr/bin/env python3
"""
增量预测更新系统
实现基于新数据的增量预测更新和智能预测刷新机制

创建日期: 2025年8月2日
作者: Augment Agent
版本: 1.0.0
"""

import asyncio
import logging
import threading
import time
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

# 导入现有系统组件
try:
    from ..core.database import DatabaseManager
    from ..data.incremental_updater import IncrementalUpdater
    from ..prediction.intelligent_fusion import IntelligentFusionSystem
    from ..services.data_update_service import DataUpdateService
except ImportError:
    # 兼容性导入
    import os
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        from core.database import DatabaseManager
        from data.incremental_updater import IncrementalUpdater
        from prediction.intelligent_fusion import IntelligentFusionSystem
        from services.data_update_service import DataUpdateService
    except ImportError as e:
        logging.warning(f"导入系统组件失败: {e}")
        IntelligentFusionSystem = None
        IncrementalUpdater = None
        DataUpdateService = None
        DatabaseManager = None

logger = logging.getLogger(__name__)

class PredictionRefreshStrategy:
    """预测刷新策略枚举"""
    FULL_REFRESH = "full_refresh"          # 完全刷新
    INCREMENTAL = "incremental"            # 增量更新
    SMART_REFRESH = "smart_refresh"        # 智能刷新
    MINIMAL_UPDATE = "minimal_update"      # 最小更新

class DataUpdateHandler:
    """数据更新事件处理器"""
    
    def __init__(self):
        self.listeners: List[Callable] = []
        self.last_check_time = datetime.now()
        self.check_interval = 300  # 5分钟检查一次
        
    def add_listener(self, callback: Callable):
        """添加数据变化监听器"""
        self.listeners.append(callback)
        
    def remove_listener(self, callback: Callable):
        """移除数据变化监听器"""
        if callback in self.listeners:
            self.listeners.remove(callback)
            
    async def notify_listeners(self, event_data: Dict[str, Any]):
        """通知所有监听器"""
        for listener in self.listeners:
            try:
                if asyncio.iscoroutinefunction(listener):
                    await listener(event_data)
                else:
                    listener(event_data)
            except Exception as e:
                logger.error(f"通知监听器失败: {e}")

class PredictionRefreshManager:
    """预测刷新管理器"""
    
    def __init__(self):
        self.refresh_history: List[Dict[str, Any]] = []
        self.last_refresh_time = None
        self.refresh_threshold = {
            'new_records': 1,      # 新记录数阈值
            'time_interval': 3600, # 时间间隔阈值（秒）
            'data_change_ratio': 0.01  # 数据变化比例阈值
        }
        
    def determine_refresh_strategy(self, change_info: Dict[str, Any]) -> str:
        """
        根据数据变化情况确定刷新策略
        
        Args:
            change_info: 数据变化信息
            
        Returns:
            刷新策略
        """
        new_records = change_info.get('new_records_count', 0)
        time_since_last = change_info.get('time_since_last_update', 0)
        data_change_ratio = change_info.get('data_change_ratio', 0)
        
        # 如果有新开奖数据，使用智能刷新
        if new_records > 0:
            if new_records >= 5:
                return PredictionRefreshStrategy.FULL_REFRESH
            else:
                return PredictionRefreshStrategy.SMART_REFRESH
                
        # 如果数据变化比例较大，使用完全刷新
        if data_change_ratio > 0.05:
            return PredictionRefreshStrategy.FULL_REFRESH
            
        # 如果时间间隔较长，使用增量更新
        if time_since_last > self.refresh_threshold['time_interval']:
            return PredictionRefreshStrategy.INCREMENTAL
            
        # 默认使用最小更新
        return PredictionRefreshStrategy.MINIMAL_UPDATE
        
    def record_refresh(self, strategy: str, result: Dict[str, Any]):
        """记录刷新历史"""
        refresh_record = {
            'timestamp': datetime.now().isoformat(),
            'strategy': strategy,
            'result': result,
            'duration': result.get('duration', 0)
        }
        
        self.refresh_history.append(refresh_record)
        self.last_refresh_time = datetime.now()
        
        # 保持历史记录数量限制
        if len(self.refresh_history) > 100:
            self.refresh_history = self.refresh_history[-50:]

class IncrementalPredictor:
    """增量预测更新系统"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化增量预测器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.is_running = False
        self.update_lock = threading.Lock()
        
        # 初始化组件
        self.data_handler = DataUpdateHandler()
        self.refresh_manager = PredictionRefreshManager()
        
        # 初始化系统组件
        self._init_system_components()
        
        # 设置监听器
        self.data_handler.add_listener(self._on_data_changed)
        
        # 状态跟踪
        self.last_update_time = None
        self.update_count = 0
        self.error_count = 0
        
        logger.info("增量预测器初始化完成")
        
    def _init_system_components(self):
        """初始化系统组件"""
        try:
            if IntelligentFusionSystem:
                self.fusion_system = IntelligentFusionSystem()
            else:
                self.fusion_system = None
                logger.warning("IntelligentFusionSystem不可用")
                
            if IncrementalUpdater:
                self.data_updater = IncrementalUpdater()
            else:
                self.data_updater = None
                logger.warning("IncrementalUpdater不可用")
                
            if DataUpdateService:
                self.update_service = DataUpdateService()
            else:
                self.update_service = None
                logger.warning("DataUpdateService不可用")
                
            if DatabaseManager:
                self.db_manager = DatabaseManager()
            else:
                self.db_manager = None
                logger.warning("DatabaseManager不可用")
                
        except Exception as e:
            logger.error(f"初始化系统组件失败: {e}")
            
    async def start_monitoring(self):
        """启动监控服务"""
        if self.is_running:
            logger.warning("监控服务已在运行")
            return
            
        self.is_running = True
        logger.info("启动增量预测监控服务")
        
        # 启动定时检查任务
        asyncio.create_task(self._periodic_check())
        
    async def stop_monitoring(self):
        """停止监控服务"""
        self.is_running = False
        logger.info("停止增量预测监控服务")
        
    async def _periodic_check(self):
        """定期检查数据变化"""
        while self.is_running:
            try:
                await self._check_for_updates()
                await asyncio.sleep(self.data_handler.check_interval)
            except Exception as e:
                logger.error(f"定期检查失败: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟再重试
                
    async def _check_for_updates(self):
        """检查是否需要更新"""
        try:
            # 检查数据变化
            change_info = await self._detect_data_changes()
            
            if change_info.get('has_changes', False):
                logger.info(f"检测到数据变化: {change_info}")
                await self.data_handler.notify_listeners(change_info)
                
        except Exception as e:
            logger.error(f"检查更新失败: {e}")
            
    async def _detect_data_changes(self) -> Dict[str, Any]:
        """检测数据变化"""
        try:
            # 使用现有的数据变化检测机制
            if self.fusion_system:
                has_changes = self.fusion_system._check_data_changed()
                
                if has_changes:
                    current_count = self.fusion_system._get_current_data_count()
                    last_count = getattr(self.fusion_system, 'training_data_count', 0)
                    
                    return {
                        'has_changes': True,
                        'new_records_count': current_count - last_count,
                        'current_count': current_count,
                        'last_count': last_count,
                        'time_since_last_update': time.time() - (self.last_update_time or 0),
                        'data_change_ratio': (current_count - last_count) / max(last_count, 1)
                    }
                    
            return {'has_changes': False}
            
        except Exception as e:
            logger.error(f"检测数据变化失败: {e}")
            return {'has_changes': False, 'error': str(e)}
            
    async def _on_data_changed(self, event_data: Dict[str, Any]):
        """数据变化事件处理器"""
        try:
            with self.update_lock:
                logger.info("处理数据变化事件")
                
                # 确定刷新策略
                strategy = self.refresh_manager.determine_refresh_strategy(event_data)
                logger.info(f"选择刷新策略: {strategy}")
                
                # 执行增量更新
                result = await self._perform_incremental_update(strategy, event_data)
                
                # 记录刷新历史
                self.refresh_manager.record_refresh(strategy, result)
                
                # 更新状态
                self.last_update_time = time.time()
                self.update_count += 1
                
                if result.get('success', False):
                    logger.info(f"增量更新完成: {result}")
                else:
                    self.error_count += 1
                    logger.error(f"增量更新失败: {result}")
                    
        except Exception as e:
            self.error_count += 1
            logger.error(f"处理数据变化事件失败: {e}")
            
    async def _perform_incremental_update(self, strategy: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行增量更新
        
        Args:
            strategy: 刷新策略
            event_data: 事件数据
            
        Returns:
            更新结果
        """
        start_time = time.time()
        
        try:
            if strategy == PredictionRefreshStrategy.FULL_REFRESH:
                result = await self._full_refresh_predictions()
            elif strategy == PredictionRefreshStrategy.SMART_REFRESH:
                result = await self._smart_refresh_predictions(event_data)
            elif strategy == PredictionRefreshStrategy.INCREMENTAL:
                result = await self._incremental_refresh_predictions(event_data)
            else:  # MINIMAL_UPDATE
                result = await self._minimal_update_predictions(event_data)
                
            duration = time.time() - start_time
            result['duration'] = duration
            result['strategy'] = strategy
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            return {
                'success': False,
                'error': str(e),
                'duration': duration,
                'strategy': strategy
            }

    async def _full_refresh_predictions(self) -> Dict[str, Any]:
        """完全刷新预测"""
        try:
            logger.info("执行完全刷新预测")

            # 触发模型重训练
            if self.fusion_system:
                train_result = self.fusion_system.train_all_models(force_retrain=True)

                # 生成新的预测结果
                prediction_result = self.fusion_system.generate_fusion_prediction()

                return {
                    'success': True,
                    'message': '完全刷新完成',
                    'train_result': train_result,
                    'prediction_result': prediction_result,
                    'updated_components': ['models', 'predictions', 'cache']
                }
            else:
                return {
                    'success': False,
                    'error': 'IntelligentFusionSystem不可用'
                }

        except Exception as e:
            logger.error(f"完全刷新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _smart_refresh_predictions(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """智能刷新预测"""
        try:
            logger.info("执行智能刷新预测")

            new_records = event_data.get('new_records_count', 0)

            # 根据新记录数量决定刷新程度
            if new_records >= 3:
                # 较多新数据，重训练关键模型
                if self.fusion_system:
                    # 只重训练部分模型以节省时间
                    train_result = self.fusion_system.train_all_models(force_retrain=False)
                    prediction_result = self.fusion_system.generate_fusion_prediction()

                    return {
                        'success': True,
                        'message': f'智能刷新完成，处理{new_records}条新记录',
                        'train_result': train_result,
                        'prediction_result': prediction_result,
                        'updated_components': ['selected_models', 'predictions']
                    }
            else:
                # 少量新数据，只更新预测缓存
                if self.fusion_system:
                    # 清除预测缓存，强制重新计算
                    if hasattr(self.fusion_system, 'clear_prediction_cache'):
                        self.fusion_system.clear_prediction_cache()

                    prediction_result = self.fusion_system.generate_fusion_prediction()

                    return {
                        'success': True,
                        'message': f'智能刷新完成，处理{new_records}条新记录',
                        'prediction_result': prediction_result,
                        'updated_components': ['predictions', 'cache']
                    }

            return {
                'success': False,
                'error': 'IntelligentFusionSystem不可用'
            }

        except Exception as e:
            logger.error(f"智能刷新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _incremental_refresh_predictions(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """增量刷新预测"""
        try:
            logger.info(f"执行增量刷新预测，事件数据: {event_data.get('new_records_count', 0)}条新记录")

            # 增量更新数据
            if self.data_updater:
                update_result = self.data_updater.perform_incremental_update()

                if update_result.get('success', False):
                    # 如果数据更新成功，重新生成预测
                    if self.fusion_system:
                        prediction_result = self.fusion_system.generate_fusion_prediction()

                        return {
                            'success': True,
                            'message': '增量刷新完成',
                            'update_result': update_result,
                            'prediction_result': prediction_result,
                            'updated_components': ['data', 'predictions']
                        }
                    else:
                        return {
                            'success': False,
                            'error': 'IntelligentFusionSystem不可用'
                        }
                else:
                    return {
                        'success': False,
                        'error': f"数据增量更新失败: {update_result.get('message', '未知错误')}"
                    }
            else:
                return {
                    'success': False,
                    'error': 'IncrementalUpdater不可用'
                }

        except Exception as e:
            logger.error(f"增量刷新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _minimal_update_predictions(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """最小更新预测"""
        try:
            logger.info(f"执行最小更新预测，事件数据: {event_data.get('new_records_count', 0)}条新记录")

            # 只更新预测缓存，不重训练模型
            if self.fusion_system:
                # 检查是否有缓存清理方法
                if hasattr(self.fusion_system, 'clear_prediction_cache'):
                    self.fusion_system.clear_prediction_cache()

                # 重新生成预测（使用现有模型）
                prediction_result = self.fusion_system.generate_fusion_prediction()

                return {
                    'success': True,
                    'message': '最小更新完成',
                    'prediction_result': prediction_result,
                    'updated_components': ['cache']
                }
            else:
                return {
                    'success': False,
                    'error': 'IntelligentFusionSystem不可用'
                }

        except Exception as e:
            logger.error(f"最小更新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        return {
            'is_running': self.is_running,
            'last_update_time': self.last_update_time,
            'update_count': self.update_count,
            'error_count': self.error_count,
            'refresh_history_count': len(self.refresh_manager.refresh_history),
            'last_refresh_time': self.refresh_manager.last_refresh_time.isoformat() if self.refresh_manager.last_refresh_time else None
        }

    def get_refresh_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取刷新历史"""
        return self.refresh_manager.refresh_history[-limit:]

    async def force_update(self, strategy: Optional[str] = None) -> Dict[str, Any]:
        """
        强制执行更新

        Args:
            strategy: 指定的刷新策略，如果为None则自动选择

        Returns:
            更新结果
        """
        try:
            logger.info("执行强制更新")

            # 检测当前数据状态
            change_info = await self._detect_data_changes()
            change_info['has_changes'] = True  # 强制认为有变化

            # 确定策略
            if strategy is None:
                strategy = self.refresh_manager.determine_refresh_strategy(change_info)

            # 执行更新
            result = await self._perform_incremental_update(strategy, change_info)

            # 记录历史
            self.refresh_manager.record_refresh(strategy, result)

            # 更新状态
            self.last_update_time = time.time()
            self.update_count += 1

            if not result.get('success', False):
                self.error_count += 1

            return result

        except Exception as e:
            self.error_count += 1
            logger.error(f"强制更新失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


# 便捷函数和单例实例
_incremental_predictor_instance = None

def get_incremental_predictor(config: Optional[Dict[str, Any]] = None) -> IncrementalPredictor:
    """获取增量预测器单例实例"""
    global _incremental_predictor_instance

    if _incremental_predictor_instance is None:
        _incremental_predictor_instance = IncrementalPredictor(config)

    return _incremental_predictor_instance

async def start_incremental_prediction_service(config: Optional[Dict[str, Any]] = None):
    """启动增量预测服务"""
    predictor = get_incremental_predictor(config)
    await predictor.start_monitoring()
    return predictor

async def stop_incremental_prediction_service():
    """停止增量预测服务"""
    global _incremental_predictor_instance

    if _incremental_predictor_instance:
        await _incremental_predictor_instance.stop_monitoring()

# 主函数用于测试
if __name__ == "__main__":
    async def main():
        """测试主函数"""
        print("测试增量预测器...")

        # 创建预测器
        predictor = IncrementalPredictor()

        # 获取状态
        status = predictor.get_update_status()
        print(f"初始状态: {status}")

        # 测试强制更新
        result = await predictor.force_update()
        print(f"强制更新结果: {result}")

        # 获取更新后状态
        status = predictor.get_update_status()
        print(f"更新后状态: {status}")

        print("测试完成")

    asyncio.run(main())
