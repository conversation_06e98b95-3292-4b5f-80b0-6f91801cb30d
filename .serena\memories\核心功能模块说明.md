# 福彩3D预测系统核心功能模块

## 1. API服务层 (src/api/)
### 主要文件
- `production_main.py`: 生产环境主API服务
- `prediction_api.py`: 预测相关API端点
- `model_library_api.py`: 模型库管理API
- `optimization_api.py`: 优化建议API
- `analysis_api.py`: 分析验证API

### 功能特性
- RESTful API设计
- WebSocket实时通信
- 健康检查和监控
- Bug检测集成
- 数据查询和分析接口

## 2. 核心业务逻辑 (src/core/)
### 关键组件
- `database.py`: 数据库管理器(支持Polars)
- `data_engine.py`: 高性能数据引擎
- `polars_engine.py`: Polars专用引擎
- `accuracy_focused_fusion.py`: 精度导向融合系统
- `model_performance_tracker.py`: 模型性能跟踪
- `number_ranking_system.py`: 号码排名系统

### 核心功能
- 高速数据处理(毫秒级响应)
- 多模型融合预测
- 性能监控和跟踪
- 数据一致性保证

## 3. 数据处理模块 (src/data/)
### 主要组件
- `collector.py`: 数据采集器
- `parser.py`: 数据解析器
- `cleaner.py`: 数据清洗器
- `quality_checker.py`: 数据质量检查
- `incremental_updater.py`: 增量更新器
- `models.py`: 数据模型定义

### 处理流程
1. 数据采集 → 2. 格式解析 → 3. 质量检查 → 4. 数据清洗 → 5. 存储入库

## 4. 预测算法模块 (src/prediction/)
### 算法实现
- `intelligent_fusion.py`: 智能融合系统
- `adaptive_fusion.py`: 自适应融合
- `markov_enhanced.py`: 增强马尔可夫模型
- `pattern_prediction.py`: 模式预测
- `trend_analysis.py`: 趋势分析
- `deep_learning/`: 深度学习模型(CNN-LSTM)

### 预测策略
- 统计学方法(频率分析、趋势分析)
- 机器学习(马尔可夫链、随机森林)
- 深度学习(CNN-LSTM、注意力机制)
- 智能融合(多模型自适应权重)

## 5. 用户界面 (src/ui/)
### 界面组件
- `main.py`: 主界面入口
- `components/`: 可复用组件
- `pages/`: 功能页面
- `intelligent_fusion_components.py`: 智能融合界面

### 功能页面
1. 数据概览页面
2. 频率分析页面
3. 预测分析页面
4. 智能融合页面
5. 数据管理页面
6. 模型库页面
7. Bug检测状态页面

## 6. 模型库系统 (src/model_library/)
### 核心组件
- `model_registry.py`: 模型注册中心
- `prediction_engine.py`: 预测引擎
- `performance_tracker.py`: 性能跟踪器
- `training/`: 训练相关模块
- `optimization/`: 优化框架

### 高级功能
- 特征工程和选择
- 超参数优化
- A/B测试框架
- 元学习系统
- 实时训练监控

## 7. Bug检测系统 (src/bug_detection/)
### AI驱动检测
- `ai/`: AI分析模块
- `algorithms/`: 检测算法
- `realtime/`: 实时分析
- `monitoring/`: 性能监控

### 检测能力
- JavaScript错误监控
- API性能分析
- 数据流追踪
- 智能错误分类
- 实时告警系统

## 8. 调度系统 (src/scheduler/)
### 任务管理
- `task_scheduler.py`: 任务调度器
- `simple_scheduler.py`: 简化调度器
- `scheduler_controller.py`: 调度控制器

### 调度功能
- 定时数据更新(每晚21:30)
- 日志清理任务
- 性能监控任务
- 错误处理和重试

## 9. 工具和服务 (src/services/, src/utils/)
### 服务组件
- `data_update_service.py`: 数据更新服务
- `error_handler.py`: 错误处理器

### 工具函数
- 数据迁移工具
- 性能优化工具
- 配置管理工具