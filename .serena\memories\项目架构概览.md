# 福彩3D预测系统项目架构概览

## 项目基本信息
- **项目名称**: 福彩3D智能预测分析工具
- **版本**: 2025.1.0
- **开发语言**: Python 3.11.9
- **项目类型**: 智能预测分析系统
- **许可证**: MIT

## 技术栈架构
### 前端技术
- **主界面**: Streamlit 1.28+ (现代化Web界面)
- **可视化**: Plotly 5.17+ + Altair 5.1+ (交互式图表)
- **实时通信**: WebSocket (状态推送、训练监控)

### 后端技术
- **API服务**: FastAPI 0.104+ (RESTful API)
- **Web服务器**: Uvicorn 0.24+ (ASGI服务器)
- **任务调度**: APScheduler 3.10+ (定时任务)

### 数据处理
- **高性能引擎**: Polars 0.19+ (毫秒级数据处理)
- **传统分析**: Pandas 2.1+ (兼容性支持)
- **数值计算**: NumPy 1.24+ (科学计算)

### 机器学习
- **深度学习**: PyTorch 2.1+ (CNN-LSTM模型)
- **传统ML**: Scikit-learn 1.3+ (统计模型)
- **数据验证**: Pydantic 2.4+ (类型验证)

### 数据存储
- **主数据库**: SQLite (轻量级关系数据库)
- **缓存系统**: Redis 5.0+ (高速缓存)
- **文件存储**: JSON/CSV/Excel (数据导出)

### 数据采集
- **HTTP客户端**: httpx 0.25+ (异步请求)
- **网页解析**: BeautifulSoup4 4.12+ (HTML解析)
- **数据源**: https://data.17500.cn/3d_asc.txt

## 项目结构
```
d:/github/3dyuce/
├── src/                    # 核心源代码
│   ├── api/               # API服务层
│   ├── core/              # 核心业务逻辑
│   ├── data/              # 数据处理模块
│   ├── prediction/        # 预测算法模块
│   ├── ui/                # 用户界面
│   └── utils/             # 工具函数
├── data/                  # 数据存储目录
├── config/                # 配置文件
├── tests/                 # 测试代码
├── docs/                  # 项目文档
└── scripts/               # 脚本工具
```

## 核心特性
1. **真实数据支持**: 8,341条历史数据(2002-2025)
2. **智能预测**: 多模型融合预测系统
3. **实时更新**: 自动数据采集和更新
4. **高性能**: Polars引擎毫秒级响应
5. **用户友好**: 现代化Web界面
6. **完整API**: RESTful API服务
7. **实时监控**: WebSocket实时通信
8. **智能Bug检测**: AI驱动的错误检测系统