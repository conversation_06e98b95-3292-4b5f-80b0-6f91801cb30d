"""
异常处理模块

提供统一的异常处理框架，包括业务异常和技术异常。
"""

from .base import (
    LotterySystemException,
    ErrorCode,
    ErrorSeverity
)
from .business import (
    PredictionException,
    DataValidationException,
    ModelException,
    ConfigurationException
)
from .technical import (
    DatabaseException,
    NetworkException,
    FileSystemException,
    CacheException
)

__all__ = [
    # 基础异常
    'LotterySystemException',
    'ErrorCode',
    'ErrorSeverity',
    
    # 业务异常
    'PredictionException',
    'DataValidationException',
    'ModelException',
    'ConfigurationException',
    
    # 技术异常
    'DatabaseException',
    'NetworkException',
    'FileSystemException',
    'CacheException',
]
