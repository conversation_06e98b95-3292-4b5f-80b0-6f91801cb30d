#!/usr/bin/env python3
"""
serena MCP配置备份脚本
用于备份和恢复serena MCP的配置信息
"""

import json
import shutil
import os
from datetime import datetime
from pathlib import Path

class SerenaConfigBackup:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backup_dir = self.project_root / "config" / "serena_backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Cursor配置路径
        self.cursor_user_dir = Path(os.environ["APPDATA"]) / "Cursor" / "User"
        self.settings_file = self.cursor_user_dir / "settings.json"
        
        # serena配置路径
        self.serena_config_dir = Path(os.environ["USERPROFILE"]) / ".serena"
        
    def backup_cursor_settings(self):
        """备份Cursor设置文件"""
        print("📁 备份Cursor设置文件...")
        
        if not self.settings_file.exists():
            print(f"  ⚠️  设置文件不存在: {self.settings_file}")
            return False
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"cursor_settings_{timestamp}.json"
        
        try:
            shutil.copy2(self.settings_file, backup_file)
            print(f"  ✅ 已备份到: {backup_file}")
            return True
        except Exception as e:
            print(f"  ❌ 备份失败: {e}")
            return False
    
    def backup_serena_config(self):
        """备份serena配置目录"""
        print("📁 备份serena配置...")
        
        if not self.serena_config_dir.exists():
            print(f"  ⚠️  serena配置目录不存在: {self.serena_config_dir}")
            return False
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = self.backup_dir / f"serena_config_{timestamp}"
        
        try:
            shutil.copytree(self.serena_config_dir, backup_dir)
            print(f"  ✅ 已备份到: {backup_dir}")
            return True
        except Exception as e:
            print(f"  ❌ 备份失败: {e}")
            return False
    
    def create_config_template(self):
        """创建serena MCP配置模板"""
        print("📝 创建serena MCP配置模板...")
        
        config_template = {
            "serena_mcp_config": {
                "executable": "d:/github/3dyuce/venv/Scripts/serena-mcp-server.exe",
                "args": [
                    "--project", "d:/github/3dyuce",
                    "--enable-web-dashboard", "true",
                    "--host", "127.0.0.1",
                    "--port", "24282"
                ],
                "working_directory": "d:/github/3dyuce",
                "dashboard_url": "http://127.0.0.1:24282/dashboard/index.html",
                "auto_start": True,
                "health_check_interval": 30
            },
            "dependencies": {
                "pyright": {
                    "required": True,
                    "install_command": "C:\\Program Files\\Python311\\python.exe -m pip install pyright",
                    "check_command": "C:\\Program Files\\Python311\\python.exe -c \"import pyright.langserver\""
                }
            },
            "troubleshooting": {
                "common_issues": [
                    {
                        "issue": "Language server failed to start",
                        "solution": "Install pyright in system Python"
                    },
                    {
                        "issue": "Dashboard not accessible",
                        "solution": "Check if serena process is running and port 24282 is available"
                    },
                    {
                        "issue": "MCP connection failed",
                        "solution": "Restart Cursor and reconfigure MCP server"
                    }
                ]
            }
        }
        
        template_file = self.backup_dir / "serena_mcp_config_template.json"
        
        try:
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(config_template, f, indent=2, ensure_ascii=False)
            print(f"  ✅ 配置模板已创建: {template_file}")
            return True
        except Exception as e:
            print(f"  ❌ 创建模板失败: {e}")
            return False
    
    def create_startup_instructions(self):
        """创建启动说明文档"""
        print("📝 创建启动说明文档...")
        
        instructions = """# serena MCP服务器启动说明

## 自动启动配置

### 方法1: 使用批处理脚本
```batch
# 运行一键启动脚本
scripts\\start_serena_mcp.bat
```

### 方法2: 使用PowerShell脚本
```powershell
# 运行PowerShell启动脚本
scripts\\start_serena_mcp.ps1

# 自动打开仪表板
scripts\\start_serena_mcp.ps1 -AutoOpen
```

### 方法3: 使用Python脚本
```bash
# 自动修复和启动
python scripts/serena_auto_fix.py

# 仅启动服务器
python scripts/serena_auto_fix.py --start-only
```

## 健康检查

### 快速检查
```bash
python scripts/serena_health_check.py --quick
```

### 完整检查
```bash
python scripts/serena_health_check.py
```

## Augment MCP配置

### 配置参数
- **服务器名称**: serena
- **可执行文件**: d:/github/3dyuce/venv/Scripts/serena-mcp-server.exe
- **参数**: --project d:/github/3dyuce
- **工作目录**: d:/github/3dyuce

### 验证步骤
1. 重启Cursor IDE
2. 打开Augment MCP设置
3. 检查serena连接状态（应为绿色）
4. 测试serena工具功能

## 故障排除

### 常见问题
1. **Language server failed to start**
   - 解决方案: 安装pyright到系统Python
   - 命令: `C:\\Program Files\\Python311\\python.exe -m pip install pyright`

2. **Dashboard not accessible**
   - 检查serena进程是否运行
   - 检查端口24282是否被占用
   - 重启serena服务器

3. **MCP connection failed**
   - 重启Cursor IDE
   - 重新配置MCP服务器
   - 检查可执行文件路径

### 重置配置
如果遇到严重问题，可以：
1. 运行自动修复脚本
2. 重启Cursor
3. 重新配置MCP连接

## 仪表板访问
- URL: http://127.0.0.1:24282/dashboard/index.html
- 功能: 查看serena状态、日志、工具列表

## 自动化脚本
- `start_serena_mcp.bat`: Windows批处理启动脚本
- `start_serena_mcp.ps1`: PowerShell启动脚本
- `serena_auto_fix.py`: 自动诊断和修复脚本
- `serena_health_check.py`: 健康检查脚本
"""
        
        instructions_file = self.backup_dir / "startup_instructions.md"
        
        try:
            with open(instructions_file, 'w', encoding='utf-8') as f:
                f.write(instructions)
            print(f"  ✅ 启动说明已创建: {instructions_file}")
            return True
        except Exception as e:
            print(f"  ❌ 创建说明失败: {e}")
            return False
    
    def run_backup(self):
        """运行完整备份"""
        print("🔄 开始serena配置备份...")
        print("=" * 50)
        
        tasks = [
            ("备份Cursor设置", self.backup_cursor_settings),
            ("备份serena配置", self.backup_serena_config),
            ("创建配置模板", self.create_config_template),
            ("创建启动说明", self.create_startup_instructions),
        ]
        
        results = []
        for task_name, task_func in tasks:
            try:
                success = task_func()
                results.append((task_name, success))
            except Exception as e:
                print(f"  ❌ {task_name}失败: {e}")
                results.append((task_name, False))
            print()
        
        # 生成报告
        print("📊 备份报告")
        print("=" * 50)
        
        for task_name, success in results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {task_name}: {status}")
        
        print(f"\n📁 备份目录: {self.backup_dir}")
        print("✅ 配置备份完成!")

def main():
    """主函数"""
    backup = SerenaConfigBackup()
    backup.run_backup()

if __name__ == "__main__":
    main()
