#!/usr/bin/env python3
"""
预测结果推送服务
监听预测完成事件，格式化并推送预测结果到WebSocket客户端
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

try:
    from .websocket_manager import (PredictionMessageType,
                                    get_prediction_websocket_manager)
except ImportError:
    from websocket_manager import (PredictionMessageType,
                                   get_prediction_websocket_manager)

# 配置日志
logger = logging.getLogger(__name__)

class PredictionPusher:
    """预测结果推送服务"""
    
    def __init__(self):
        self.websocket_manager = None
        self.is_running = False
        self.push_queue = asyncio.Queue()
        self.stats = {
            'predictions_pushed': 0,
            'errors': 0,
            'start_time': time.time(),
            'last_push_time': None,
            'average_push_delay': 0.0
        }
        self._background_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """启动推送服务"""
        if self.is_running:
            return
        
        self.websocket_manager = await get_prediction_websocket_manager()
        await self.websocket_manager.start()
        
        self.is_running = True
        self._background_task = asyncio.create_task(self._process_push_queue())
        
        logger.info("预测结果推送服务已启动")
    
    async def stop(self):
        """停止推送服务"""
        self.is_running = False
        
        if self._background_task:
            self._background_task.cancel()
            try:
                await self._background_task
            except asyncio.CancelledError:
                pass
        
        if self.websocket_manager:
            await self.websocket_manager.stop()
        
        logger.info("预测结果推送服务已停止")
    
    async def push_prediction_start(self, prediction_id: str, request_data: Dict[str, Any],
                                  target_session: Optional[str] = None):
        """推送预测开始事件"""
        start_time = time.time()
        
        message_data = {
            'prediction_id': prediction_id,
            'status': 'started',
            'request_data': request_data,
            'start_time': start_time,
            'estimated_duration': 2.0  # 预估2秒完成
        }
        
        await self._queue_push({
            'type': 'prediction_start',
            'data': message_data,
            'target_session': target_session,
            'timestamp': start_time
        })
        
        logger.info(f"预测开始事件已排队: {prediction_id}")
    
    async def push_prediction_progress(self, prediction_id: str, progress: float,
                                     stage: str, details: Optional[Dict[str, Any]] = None,
                                     target_session: Optional[str] = None):
        """推送预测进度事件"""
        progress_time = time.time()
        
        message_data = {
            'prediction_id': prediction_id,
            'progress': progress,
            'stage': stage,
            'details': details or {},
            'timestamp': progress_time
        }
        
        await self._queue_push({
            'type': 'prediction_progress',
            'data': message_data,
            'target_session': target_session,
            'timestamp': progress_time
        })
        
        logger.debug(f"预测进度事件已排队: {prediction_id} - {progress:.1%} - {stage}")
    
    async def push_prediction_result(self, prediction_id: str, result_data: Dict[str, Any],
                                   performance_metrics: Optional[Dict[str, Any]] = None,
                                   target_session: Optional[str] = None):
        """推送预测结果"""
        result_time = time.time()
        
        # 格式化预测结果
        formatted_result = self._format_prediction_result(result_data, performance_metrics)
        
        message_data = {
            'prediction_id': prediction_id,
            'status': 'completed',
            'result': formatted_result,
            'completion_time': result_time,
            'performance_metrics': performance_metrics or {}
        }
        
        await self._queue_push({
            'type': 'prediction_result',
            'data': message_data,
            'target_session': target_session,
            'timestamp': result_time
        })
        
        logger.info(f"预测结果已排队推送: {prediction_id}")
    
    async def push_prediction_error(self, prediction_id: str, error_message: str,
                                  error_details: Optional[Dict[str, Any]] = None,
                                  target_session: Optional[str] = None):
        """推送预测错误"""
        error_time = time.time()
        
        message_data = {
            'prediction_id': prediction_id,
            'status': 'error',
            'error_message': error_message,
            'error_details': error_details or {},
            'error_time': error_time
        }
        
        await self._queue_push({
            'type': 'prediction_error',
            'data': message_data,
            'target_session': target_session,
            'timestamp': error_time
        })
        
        logger.error(f"预测错误已排队推送: {prediction_id} - {error_message}")
    
    def _format_prediction_result(self, result_data: Dict[str, Any], 
                                performance_metrics: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """格式化预测结果"""
        formatted = {
            'predictions': result_data.get('predictions', []),
            'confidence_scores': result_data.get('confidence_scores', {}),
            'analysis': result_data.get('analysis', {}),
            'recommendations': result_data.get('recommendations', []),
            'metadata': {
                'model_version': result_data.get('model_version', 'unknown'),
                'prediction_method': result_data.get('prediction_method', 'intelligent_fusion'),
                'data_quality': result_data.get('data_quality', 'good'),
                'timestamp': time.time()
            }
        }
        
        # 添加性能指标
        if performance_metrics:
            formatted['performance'] = {
                'prediction_time': performance_metrics.get('prediction_time', 0),
                'model_accuracy': performance_metrics.get('model_accuracy', 0),
                'confidence_level': performance_metrics.get('confidence_level', 0),
                'cache_hit': performance_metrics.get('cache_hit', False)
            }
        
        return formatted
    
    async def _queue_push(self, push_data: Dict[str, Any]):
        """将推送数据加入队列"""
        try:
            await self.push_queue.put(push_data)
        except Exception as e:
            logger.error(f"推送数据入队失败: {e}")
            self.stats['errors'] += 1
    
    async def _process_push_queue(self):
        """处理推送队列"""
        while self.is_running:
            try:
                # 等待推送数据
                push_data = await asyncio.wait_for(
                    self.push_queue.get(), 
                    timeout=1.0
                )
                
                # 执行推送
                await self._execute_push(push_data)
                
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"处理推送队列失败: {e}")
                self.stats['errors'] += 1
                await asyncio.sleep(0.1)
    
    async def _execute_push(self, push_data: Dict[str, Any]):
        """执行实际的推送操作"""
        if not self.websocket_manager:
            logger.warning("WebSocket管理器未初始化，跳过推送")
            return
        
        push_type = push_data.get('type')
        data = push_data.get('data')
        target_session = push_data.get('target_session')
        push_timestamp = push_data.get('timestamp', time.time())
        
        try:
            # 计算推送延迟
            current_time = time.time()
            push_delay = current_time - push_timestamp
            
            # 根据推送类型选择方法
            if push_type == 'prediction_start':
                await self.websocket_manager.broadcast_to_topic('prediction_results', {
                    'type': PredictionMessageType.PREDICTION_START.value,
                    'data': data,
                    'timestamp': current_time
                })
            elif push_type == 'prediction_progress':
                if target_session:
                    await self.websocket_manager.send_prediction_progress(data, target_session)
                else:
                    await self.websocket_manager.broadcast_to_topic('prediction_progress', {
                        'type': PredictionMessageType.PREDICTION_PROGRESS.value,
                        'data': data,
                        'timestamp': current_time
                    })
            elif push_type == 'prediction_result':
                await self.websocket_manager.send_prediction_result(data, target_session)
            elif push_type == 'prediction_error':
                await self.websocket_manager.broadcast_to_topic('prediction_results', {
                    'type': PredictionMessageType.PREDICTION_ERROR.value,
                    'data': data,
                    'timestamp': current_time
                })
            
            # 更新统计信息
            self.stats['predictions_pushed'] += 1
            self.stats['last_push_time'] = current_time
            
            # 更新平均推送延迟
            if self.stats['predictions_pushed'] > 0:
                self.stats['average_push_delay'] = (
                    (self.stats['average_push_delay'] * (self.stats['predictions_pushed'] - 1) + push_delay) /
                    self.stats['predictions_pushed']
                )
            
            logger.debug(f"推送完成: {push_type}, 延迟: {push_delay:.3f}秒")
            
        except Exception as e:
            logger.error(f"执行推送失败: {e}")
            self.stats['errors'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取推送服务统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats['start_time']
        
        stats = {
            **self.stats,
            'uptime_seconds': uptime,
            'queue_size': self.push_queue.qsize(),
            'is_running': self.is_running
        }
        
        if self.websocket_manager:
            stats['websocket_stats'] = self.websocket_manager.get_stats()
        
        return stats

# 全局实例
prediction_pusher = PredictionPusher()

async def get_prediction_pusher() -> PredictionPusher:
    """获取预测推送服务实例"""
    return prediction_pusher
