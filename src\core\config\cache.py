"""
缓存配置

管理缓存系统相关的配置参数。
"""

from typing import Dict, List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class CacheConfig(BaseSettings):
    """缓存配置类"""
    
    # 缓存策略配置
    enabled: bool = Field(default=True, description="是否启用缓存")
    default_ttl: int = Field(default=3600, description="默认TTL(秒)")
    max_size: int = Field(default=1000, description="最大缓存条目数")
    
    # 内存缓存配置
    memory_enabled: bool = Field(default=True, description="是否启用内存缓存")
    memory_max_size: int = Field(default=500, description="内存缓存最大条目数")
    memory_ttl: int = Field(default=1800, description="内存缓存TTL(秒)")
    
    # Redis缓存配置
    redis_enabled: bool = Field(default=False, description="是否启用Redis缓存")
    redis_host: str = Field(default="localhost", description="Redis主机")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_db: int = Field(default=0, description="Redis数据库")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    redis_ttl: int = Field(default=7200, description="Redis缓存TTL(秒)")
    redis_max_connections: int = Field(default=10, description="Redis最大连接数")
    
    # 文件缓存配置
    file_enabled: bool = Field(default=True, description="是否启用文件缓存")
    file_dir: str = Field(default="data/cache", description="文件缓存目录")
    file_ttl: int = Field(default=86400, description="文件缓存TTL(秒)")
    file_max_size: int = Field(default=100, description="文件缓存最大条目数")
    file_cleanup_interval: int = Field(default=3600, description="文件清理间隔(秒)")
    
    # 缓存键配置
    key_prefix: str = Field(default="lottery:", description="缓存键前缀")
    key_separator: str = Field(default=":", description="缓存键分隔符")
    
    # 缓存层次配置
    cache_levels: List[str] = Field(
        default=["memory", "file"], 
        description="缓存层次顺序"
    )
    
    # 预热配置
    preload_enabled: bool = Field(default=False, description="是否启用预热")
    preload_keys: List[str] = Field(default_factory=list, description="预热键列表")
    
    # 监控配置
    monitoring_enabled: bool = Field(default=True, description="是否启用监控")
    stats_collection: bool = Field(default=True, description="是否收集统计信息")
    hit_rate_threshold: float = Field(default=0.8, description="命中率阈值")
    
    # 压缩配置
    compression_enabled: bool = Field(default=False, description="是否启用压缩")
    compression_threshold: int = Field(default=1024, description="压缩阈值(字节)")
    compression_level: int = Field(default=6, description="压缩级别")
    
    # 序列化配置
    serializer: str = Field(default="pickle", description="序列化器类型")
    serializer_options: Dict = Field(default_factory=dict, description="序列化器选项")
    
    class Config:
        env_prefix = "CACHE_"
        env_file = ".env"
        case_sensitive = False
    
    @validator('redis_port')
    def validate_redis_port(cls, v):
        """验证Redis端口"""
        if not 1 <= v <= 65535:
            raise ValueError('Redis端口必须在1-65535之间')
        return v
    
    @validator('hit_rate_threshold')
    def validate_hit_rate_threshold(cls, v):
        """验证命中率阈值"""
        if not 0 <= v <= 1:
            raise ValueError('命中率阈值必须在0-1之间')
        return v
    
    @validator('compression_level')
    def validate_compression_level(cls, v):
        """验证压缩级别"""
        if not 1 <= v <= 9:
            raise ValueError('压缩级别必须在1-9之间')
        return v
    
    @validator('cache_levels')
    def validate_cache_levels(cls, v):
        """验证缓存层次"""
        valid_levels = ['memory', 'redis', 'file']
        for level in v:
            if level not in valid_levels:
                raise ValueError(f'无效的缓存层次: {level}')
        return v
    
    def get_redis_url(self) -> str:
        """获取Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    def get_enabled_backends(self) -> List[str]:
        """获取启用的缓存后端"""
        backends = []
        
        if self.memory_enabled and "memory" in self.cache_levels:
            backends.append("memory")
        
        if self.redis_enabled and "redis" in self.cache_levels:
            backends.append("redis")
        
        if self.file_enabled and "file" in self.cache_levels:
            backends.append("file")
        
        return backends
    
    def get_backend_config(self, backend: str) -> dict:
        """获取指定后端的配置"""
        if backend == "memory":
            return {
                "max_size": self.memory_max_size,
                "ttl": self.memory_ttl,
            }
        elif backend == "redis":
            return {
                "host": self.redis_host,
                "port": self.redis_port,
                "db": self.redis_db,
                "password": self.redis_password,
                "ttl": self.redis_ttl,
                "max_connections": self.redis_max_connections,
            }
        elif backend == "file":
            return {
                "cache_dir": self.file_dir,
                "ttl": self.file_ttl,
                "max_size": self.file_max_size,
                "cleanup_interval": self.file_cleanup_interval,
            }
        else:
            return {}
    
    def build_cache_key(self, *parts: str) -> str:
        """构建缓存键"""
        key_parts = [self.key_prefix] + list(parts)
        return self.key_separator.join(key_parts)
