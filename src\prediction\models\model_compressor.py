"""
模型压缩工具

提供模型剪枝、量化、知识蒸馏等压缩技术。
"""

import copy
import logging
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.nn.utils.prune as prune

logger = logging.getLogger(__name__)


class ModelCompressor:
    """模型压缩器"""
    
    def __init__(self, model: nn.Module):
        self.model = model
        # 保存原始模型状态字典而不是深拷贝
        self.original_state_dict = model.state_dict()
        self.original_model_info = self._get_model_info(model)
        self.compression_stats = {}

    def _get_model_info(self, model: nn.Module) -> Dict[str, Any]:
        """获取模型信息"""
        total_params = sum(p.numel() for p in model.parameters())
        return {
            'total_parameters': total_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)
        }
    
    def structured_pruning(
        self, 
        pruning_ratio: float = 0.3,
        importance_metric: str = 'magnitude'
    ) -> nn.Module:
        """
        结构化剪枝
        
        Args:
            pruning_ratio: 剪枝比例
            importance_metric: 重要性度量方法
            
        Returns:
            剪枝后的模型
        """
        logger.info(f"开始结构化剪枝，剪枝比例: {pruning_ratio}")
        
        # 计算每层的重要性
        layer_importance = self._calculate_layer_importance(importance_metric)
        
        # 确定要剪枝的层
        layers_to_prune = self._select_layers_to_prune(layer_importance, pruning_ratio)
        
        # 执行剪枝
        for layer_name, prune_amount in layers_to_prune.items():
            layer = dict(self.model.named_modules())[layer_name]
            
            if isinstance(layer, nn.Linear):
                prune.ln_structured(layer, name='weight', amount=prune_amount, n=2, dim=0)
            elif isinstance(layer, nn.Conv1d):
                prune.ln_structured(layer, name='weight', amount=prune_amount, n=2, dim=0)
        
        # 移除剪枝掩码，永久删除权重
        for module in self.model.modules():
            if hasattr(module, 'weight_mask'):
                prune.remove(module, 'weight')
        
        # 记录压缩统计
        self._update_compression_stats('structured_pruning', pruning_ratio)
        
        logger.info("结构化剪枝完成")
        return self.model
    
    def unstructured_pruning(
        self, 
        pruning_ratio: float = 0.5,
        global_pruning: bool = True
    ) -> nn.Module:
        """
        非结构化剪枝
        
        Args:
            pruning_ratio: 剪枝比例
            global_pruning: 是否使用全局剪枝
            
        Returns:
            剪枝后的模型
        """
        logger.info(f"开始非结构化剪枝，剪枝比例: {pruning_ratio}")
        
        # 收集所有可剪枝的参数
        parameters_to_prune = []
        for name, module in self.model.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv1d)):
                parameters_to_prune.append((module, 'weight'))
        
        if global_pruning:
            # 全局剪枝
            prune.global_unstructured(
                parameters_to_prune,
                pruning_method=prune.L1Unstructured,
                amount=pruning_ratio,
            )
        else:
            # 逐层剪枝
            for module, param_name in parameters_to_prune:
                prune.l1_unstructured(module, name=param_name, amount=pruning_ratio)
        
        # 记录压缩统计
        self._update_compression_stats('unstructured_pruning', pruning_ratio)
        
        logger.info("非结构化剪枝完成")
        return self.model
    
    def knowledge_distillation(
        self,
        teacher_model: nn.Module,
        student_model: nn.Module,
        train_loader: torch.utils.data.DataLoader,
        temperature: float = 4.0,
        alpha: float = 0.7,
        epochs: int = 10
    ) -> nn.Module:
        """
        知识蒸馏
        
        Args:
            teacher_model: 教师模型
            student_model: 学生模型
            train_loader: 训练数据加载器
            temperature: 蒸馏温度
            alpha: 蒸馏损失权重
            epochs: 训练轮数
            
        Returns:
            蒸馏后的学生模型
        """
        logger.info("开始知识蒸馏训练")
        
        teacher_model.eval()
        student_model.train()
        
        optimizer = torch.optim.Adam(student_model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        for epoch in range(epochs):
            total_loss = 0
            for batch_idx, (data, target) in enumerate(train_loader):
                optimizer.zero_grad()
                
                # 教师模型预测
                with torch.no_grad():
                    teacher_output = teacher_model(data)
                
                # 学生模型预测
                student_output = student_model(data)
                
                # 计算蒸馏损失
                distillation_loss = self._distillation_loss(
                    student_output, teacher_output, temperature
                )
                
                # 计算标准损失
                student_loss = criterion(student_output, target)
                
                # 总损失
                loss = alpha * distillation_loss + (1 - alpha) * student_loss
                
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(train_loader)
            logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.4f}")
        
        # 记录压缩统计
        self._update_compression_stats('knowledge_distillation', 
                                     {'temperature': temperature, 'alpha': alpha})
        
        logger.info("知识蒸馏完成")
        return student_model
    
    def weight_sharing(self, sharing_ratio: float = 0.5) -> nn.Module:
        """
        权重共享压缩
        
        Args:
            sharing_ratio: 共享比例
            
        Returns:
            压缩后的模型
        """
        logger.info(f"开始权重共享，共享比例: {sharing_ratio}")
        
        for name, module in self.model.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv1d)):
                weight = module.weight.data
                
                # 计算需要保留的唯一权重数量
                total_weights = weight.numel()
                unique_weights = int(total_weights * (1 - sharing_ratio))
                
                # K-means聚类进行权重共享
                weight_flat = weight.flatten()
                centroids = self._kmeans_1d(weight_flat.cpu().numpy(), unique_weights)
                
                # 将权重替换为最近的聚类中心
                for i, centroid in enumerate(centroids):
                    mask = torch.abs(weight_flat - centroid) == torch.min(
                        torch.abs(weight_flat.unsqueeze(1) - torch.tensor(centroids)), dim=1
                    )[0]
                    weight_flat[mask] = centroid
                
                module.weight.data = weight_flat.reshape(weight.shape)
        
        # 记录压缩统计
        self._update_compression_stats('weight_sharing', sharing_ratio)
        
        logger.info("权重共享完成")
        return self.model
    
    def _calculate_layer_importance(self, metric: str) -> Dict[str, float]:
        """计算层重要性"""
        importance = {}
        
        for name, module in self.model.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv1d)):
                if metric == 'magnitude':
                    # 基于权重幅度的重要性
                    importance[name] = torch.norm(module.weight).item()
                elif metric == 'gradient':
                    # 基于梯度的重要性（需要在训练过程中计算）
                    if module.weight.grad is not None:
                        importance[name] = torch.norm(module.weight.grad).item()
                    else:
                        importance[name] = 0.0
        
        return importance
    
    def _select_layers_to_prune(
        self, 
        layer_importance: Dict[str, float], 
        pruning_ratio: float
    ) -> Dict[str, float]:
        """选择要剪枝的层"""
        # 按重要性排序
        sorted_layers = sorted(layer_importance.items(), key=lambda x: x[1])
        
        # 选择重要性最低的层进行剪枝
        num_layers_to_prune = int(len(sorted_layers) * pruning_ratio)
        layers_to_prune = {}
        
        for i in range(num_layers_to_prune):
            layer_name = sorted_layers[i][0]
            layers_to_prune[layer_name] = 0.3  # 每层剪枝30%
        
        return layers_to_prune
    
    def _distillation_loss(
        self, 
        student_output: torch.Tensor, 
        teacher_output: torch.Tensor, 
        temperature: float
    ) -> torch.Tensor:
        """计算蒸馏损失"""
        student_soft = F.log_softmax(student_output / temperature, dim=1)
        teacher_soft = F.softmax(teacher_output / temperature, dim=1)
        
        return F.kl_div(student_soft, teacher_soft, reduction='batchmean') * (temperature ** 2)
    
    def _kmeans_1d(self, data: np.ndarray, k: int, max_iters: int = 100) -> List[float]:
        """一维K-means聚类"""
        # 初始化聚类中心
        centroids = np.linspace(data.min(), data.max(), k)
        
        for _ in range(max_iters):
            # 分配数据点到最近的聚类中心
            distances = np.abs(data[:, np.newaxis] - centroids)
            assignments = np.argmin(distances, axis=1)
            
            # 更新聚类中心
            new_centroids = []
            for i in range(k):
                if np.sum(assignments == i) > 0:
                    new_centroids.append(np.mean(data[assignments == i]))
                else:
                    new_centroids.append(centroids[i])
            
            new_centroids = np.array(new_centroids)
            
            # 检查收敛
            if np.allclose(centroids, new_centroids):
                break
            
            centroids = new_centroids
        
        return centroids.tolist()
    
    def _update_compression_stats(self, method: str, params: Any):
        """更新压缩统计信息"""
        original_size = self.original_model_info['total_parameters']
        current_size = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        compression_ratio = 1 - (current_size / original_size)
        
        self.compression_stats[method] = {
            'params': params,
            'original_size': original_size,
            'compressed_size': current_size,
            'compression_ratio': compression_ratio,
            'size_reduction': f"{compression_ratio * 100:.2f}%"
        }
    
    def get_compression_stats(self) -> Dict[str, Any]:
        """获取压缩统计信息"""
        return self.compression_stats
    
    def save_compressed_model(self, path: str):
        """保存压缩后的模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'compression_stats': self.compression_stats
        }, path)
        logger.info(f"压缩模型已保存到: {path}")


if __name__ == "__main__":
    # 测试模型压缩
    from optimized_cnn_lstm import create_optimized_model
    
    config = {'input_size': 3, 'hidden_size': 64, 'num_classes': 1000}
    model = create_optimized_model(config)
    
    compressor = ModelCompressor(model)
    
    # 测试非结构化剪枝
    compressed_model = compressor.unstructured_pruning(pruning_ratio=0.3)
    
    print("压缩统计:")
    for method, stats in compressor.get_compression_stats().items():
        print(f"{method}: {stats['size_reduction']} 减少")
