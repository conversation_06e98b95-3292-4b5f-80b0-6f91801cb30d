# serena MCP服务器PowerShell启动脚本
# 确保serena MCP服务器正常启动并可通过Augment访问

param(
    [switch]$AutoOpen,
    [switch]$SkipHealthCheck
)

Write-Host "🚀 启动serena MCP服务器..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Gray

# 设置项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

Write-Host "📁 项目目录: $ProjectRoot" -ForegroundColor Cyan
Write-Host ""

# 检查serena可执行文件
$SerenaExe = Join-Path $ProjectRoot "venv\Scripts\serena-mcp-server.exe"
if (-not (Test-Path $SerenaExe)) {
    Write-Host "❌ serena-mcp-server.exe 不存在" -ForegroundColor Red
    Write-Host "请先安装serena: pip install serena" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 找到serena-mcp-server.exe" -ForegroundColor Green
Write-Host ""

# 终止现有进程
Write-Host "🔄 终止现有serena进程..." -ForegroundColor Yellow
try {
    Get-Process -Name "serena-mcp-server" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 2
    Write-Host "  ✅ 已终止现有进程" -ForegroundColor Green
} catch {
    Write-Host "  ℹ️  没有找到运行中的serena进程" -ForegroundColor Gray
}

# 检查pyright依赖
Write-Host "🔍 检查pyright依赖..." -ForegroundColor Yellow
try {
    $PyrightCheck = & "C:\Program Files\Python311\python.exe" -c "import pyright.langserver; print('OK')" 2>$null
    if ($PyrightCheck -eq "OK") {
        Write-Host "  ✅ pyright依赖正常" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  pyright依赖可能有问题" -ForegroundColor Yellow
    }
} catch {
    Write-Host "  ⚠️  无法检查pyright依赖" -ForegroundColor Yellow
}

Write-Host ""

# 启动serena MCP服务器
Write-Host "🚀 启动serena MCP服务器..." -ForegroundColor Green

$SerenaArgs = @(
    "--project", $ProjectRoot,
    "--enable-web-dashboard", "true",
    "--host", "127.0.0.1",
    "--port", "24282"
)

Write-Host "命令: $SerenaExe $($SerenaArgs -join ' ')" -ForegroundColor Gray
Write-Host ""

try {
    # 在新窗口中启动serena
    $ProcessInfo = New-Object System.Diagnostics.ProcessStartInfo
    $ProcessInfo.FileName = $SerenaExe
    $ProcessInfo.Arguments = $SerenaArgs -join ' '
    $ProcessInfo.WorkingDirectory = $ProjectRoot
    $ProcessInfo.UseShellExecute = $true
    $ProcessInfo.CreateNoWindow = $false
    
    $Process = [System.Diagnostics.Process]::Start($ProcessInfo)
    Write-Host "✅ serena服务器已启动 (PID: $($Process.Id))" -ForegroundColor Green
    
    # 等待服务器启动
    Write-Host "⏳ 等待服务器启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
    
    # 检查服务器状态
    if (-not $SkipHealthCheck) {
        Write-Host "🔍 检查服务器状态..." -ForegroundColor Yellow
        try {
            $Response = Invoke-WebRequest -Uri "http://127.0.0.1:24282/dashboard/index.html" -TimeoutSec 5 -ErrorAction Stop
            if ($Response.StatusCode -eq 200) {
                Write-Host "  ✅ serena仪表板可访问" -ForegroundColor Green
            }
        } catch {
            Write-Host "  ⚠️  仪表板暂时不可访问，服务器可能仍在启动中" -ForegroundColor Yellow
        }
    }
    
} catch {
    Write-Host "❌ 启动serena服务器失败: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "📋 下一步操作:" -ForegroundColor Cyan
Write-Host "1. 重启Cursor IDE" -ForegroundColor White
Write-Host "2. 在Augment中检查serena MCP连接状态" -ForegroundColor White
Write-Host "3. 访问仪表板: http://127.0.0.1:24282/dashboard/index.html" -ForegroundColor White
Write-Host ""

# 询问是否打开仪表板
if (-not $AutoOpen) {
    $OpenDashboard = Read-Host "是否打开serena仪表板? (y/n)"
    if ($OpenDashboard -eq "y" -or $OpenDashboard -eq "Y") {
        $AutoOpen = $true
    }
}

if ($AutoOpen) {
    Write-Host "🌐 打开serena仪表板..." -ForegroundColor Green
    Start-Process "http://127.0.0.1:24282/dashboard/index.html"
}

Write-Host ""
Write-Host "✅ serena MCP服务器启动完成!" -ForegroundColor Green

if (-not $AutoOpen) {
    Read-Host "按任意键退出"
}
