#!/usr/bin/env python3
"""
增量预测器测试脚本
验证增量预测更新系统的功能
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.append('src')

async def test_incremental_predictor():
    """测试增量预测器"""
    print("🧪 测试增量预测器...")
    
    try:
        # 导入增量预测器
        from core.incremental_predictor import IncrementalPredictor, get_incremental_predictor
        print("✅ 增量预测器导入成功")
        
        # 创建预测器实例
        predictor = IncrementalPredictor()
        print("✅ 增量预测器实例创建成功")
        
        # 测试获取状态
        status = predictor.get_update_status()
        print(f"✅ 获取状态成功: {status}")
        
        # 测试强制更新
        print("\n🔄 测试强制更新...")
        result = await predictor.force_update()
        print(f"✅ 强制更新结果: {result}")
        
        # 测试获取刷新历史
        history = predictor.get_refresh_history(limit=5)
        print(f"✅ 获取刷新历史: {len(history)}条记录")
        
        # 测试单例模式
        predictor2 = get_incremental_predictor()
        print(f"✅ 单例模式测试: {predictor is predictor2}")
        
        print("\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_integration():
    """测试系统集成"""
    print("\n🔗 测试系统集成...")
    
    try:
        from core.incremental_predictor import start_incremental_prediction_service
        
        # 启动增量预测服务
        predictor = await start_incremental_prediction_service()
        print("✅ 增量预测服务启动成功")
        
        # 等待一小段时间
        await asyncio.sleep(2)
        
        # 停止服务
        from core.incremental_predictor import stop_incremental_prediction_service
        await stop_incremental_prediction_service()
        print("✅ 增量预测服务停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def test_imports():
    """测试导入依赖"""
    print("📦 测试导入依赖...")
    
    try:
        # 测试核心导入
        from core.incremental_predictor import (
            IncrementalPredictor,
            DataUpdateHandler,
            PredictionRefreshManager,
            PredictionRefreshStrategy
        )
        print("✅ 核心类导入成功")
        
        # 测试策略枚举
        strategies = [
            PredictionRefreshStrategy.FULL_REFRESH,
            PredictionRefreshStrategy.INCREMENTAL,
            PredictionRefreshStrategy.SMART_REFRESH,
            PredictionRefreshStrategy.MINIMAL_UPDATE
        ]
        print(f"✅ 刷新策略: {strategies}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始增量预测器测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        return
    
    # 测试基本功能
    if not await test_incremental_predictor():
        return
    
    # 测试集成功能
    if not await test_integration():
        return
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成！增量预测器工作正常")

if __name__ == "__main__":
    asyncio.run(main())
