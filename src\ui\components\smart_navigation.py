"""
智能导航组件
提供个性化推荐、使用习惯分析和智能化导航功能
"""

import streamlit as st
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import json
import hashlib
from collections import defaultdict, Counter
import time


class SmartNavigationComponent:
    """智能导航组件"""
    
    def __init__(self):
        """初始化智能导航组件"""
        self.usage_history = self._load_usage_history()
        self.user_preferences = self._load_user_preferences()
        self.page_analytics = defaultdict(lambda: {
            'visits': 0,
            'total_time': 0,
            'last_visit': None,
            'bounce_rate': 0.0
        })
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'smart_nav_session' not in st.session_state:
            st.session_state.smart_nav_session = {
                'session_start': datetime.now(),
                'current_page': None,
                'page_start_time': None,
                'navigation_path': [],
                'search_queries': [],
                'recommendations_shown': [],
                'recommendations_clicked': []
            }
    
    def _load_usage_history(self) -> Dict[str, Any]:
        """加载使用历史记录"""
        # 在实际应用中，这里应该从数据库或文件加载
        # 这里使用模拟数据
        return {
            "📈 数据概览": {"count": 45, "avg_time": 120, "last_used": "2025-07-30"},
            "🎯 预测分析": {"count": 38, "avg_time": 180, "last_used": "2025-07-30"},
            "📊 频率分析": {"count": 25, "avg_time": 90, "last_used": "2025-07-29"},
            "🔄 数据更新": {"count": 20, "avg_time": 60, "last_used": "2025-07-29"},
            "💡 优化建议": {"count": 15, "avg_time": 150, "last_used": "2025-07-28"},
            "🔍 数据查询": {"count": 12, "avg_time": 75, "last_used": "2025-07-28"},
            "📊 实时监控": {"count": 10, "avg_time": 200, "last_used": "2025-07-27"},
            "🔬 特征工程": {"count": 8, "avg_time": 300, "last_used": "2025-07-26"}
        }
    
    def _load_user_preferences(self) -> Dict[str, Any]:
        """加载用户偏好设置"""
        return {
            "preferred_categories": ["📊 数据分析", "🎯 智能预测"],
            "favorite_pages": ["📈 数据概览", "🎯 预测分析"],
            "theme_preference": "蓝色渐变",
            "layout_preference": "卡片式",
            "show_recommendations": True,
            "max_recommendations": 6,
            "auto_refresh": True,
            "notification_enabled": True
        }
    
    def track_page_visit(self, page_name: str):
        """跟踪页面访问"""
        session = st.session_state.smart_nav_session
        current_time = datetime.now()
        
        # 记录上一个页面的停留时间
        if session['current_page'] and session['page_start_time']:
            prev_page = session['current_page']
            stay_time = (current_time - session['page_start_time']).total_seconds()
            self.page_analytics[prev_page]['total_time'] += stay_time
        
        # 更新当前页面信息
        session['current_page'] = page_name
        session['page_start_time'] = current_time
        session['navigation_path'].append({
            'page': page_name,
            'timestamp': current_time,
            'from_recommendation': page_name in session['recommendations_clicked']
        })
        
        # 更新页面分析数据
        self.page_analytics[page_name]['visits'] += 1
        self.page_analytics[page_name]['last_visit'] = current_time
        
        # 更新使用历史
        if page_name in self.usage_history:
            self.usage_history[page_name]['count'] += 1
        else:
            self.usage_history[page_name] = {
                'count': 1,
                'avg_time': 0,
                'last_used': current_time.strftime("%Y-%m-%d")
            }
    
    def get_personalized_recommendations(self, limit: int = 6) -> List[Dict[str, Any]]:
        """获取个性化推荐"""
        recommendations = []
        
        # 基于使用频率的推荐
        frequent_pages = sorted(
            self.usage_history.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )[:3]
        
        for page_name, stats in frequent_pages:
            recommendations.append({
                'page': page_name,
                'reason': f"常用功能 (使用{stats['count']}次)",
                'score': stats['count'] * 0.8,
                'type': 'frequent'
            })
        
        # 基于时间的推荐（最近使用）
        recent_pages = sorted(
            self.usage_history.items(),
            key=lambda x: x[1]['last_used'],
            reverse=True
        )[:2]
        
        for page_name, stats in recent_pages:
            if not any(r['page'] == page_name for r in recommendations):
                recommendations.append({
                    'page': page_name,
                    'reason': f"最近使用 ({stats['last_used']})",
                    'score': 50,
                    'type': 'recent'
                })
        
        # 基于偏好分类的推荐
        preferred_categories = self.user_preferences.get('preferred_categories', [])
        all_pages = self._get_all_pages()
        
        for category in preferred_categories:
            if category in all_pages:
                for page_name in all_pages[category]:
                    if not any(r['page'] == page_name for r in recommendations):
                        recommendations.append({
                            'page': page_name,
                            'reason': f"偏好分类推荐",
                            'score': 30,
                            'type': 'preference'
                        })
                        if len(recommendations) >= limit:
                            break
        
        # 按评分排序并限制数量
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations[:limit]
    
    def _get_all_pages(self) -> Dict[str, List[str]]:
        """获取所有页面分类"""
        return {
            "🏠 首页概览": [
                "📈 数据概览",
                "🎲 最新开奖",
                "📊 系统状态"
            ],
            "🎯 智能预测": [
                "🤖 智能融合预测",
                "📈 趋势分析预测",
                "🎯 预测分析",
                "🏛️ 模型库管理"
            ],
            "📊 数据分析": [
                "🔍 数据查询",
                "📊 频率分析",
                "💰 销售分析",
                "📈 和值分布"
            ],
            "🔧 系统管理": [
                "🔄 数据更新",
                "📊 性能监控",
                "💡 优化建议",
                "🤖 模型训练"
            ],
            "⚙️ 高级功能": [
                "🔬 特征工程",
                "🧪 A/B测试",
                "📊 数据管理深度",
                "🔍 系统诊断"
            ]
        }
    
    def render_smart_recommendations(self) -> None:
        """渲染智能推荐面板"""
        if not self.user_preferences.get('show_recommendations', True):
            return
        
        recommendations = self.get_personalized_recommendations()
        
        if not recommendations:
            return
        
        # 记录显示的推荐
        session = st.session_state.smart_nav_session
        session['recommendations_shown'] = [r['page'] for r in recommendations]
        
        st.markdown("### 🤖 智能推荐")
        
        # 创建推荐卡片
        cols = st.columns(min(3, len(recommendations)))
        
        for i, rec in enumerate(recommendations[:3]):
            with cols[i % 3]:
                # 提取页面图标和名称
                parts = rec['page'].split(" ", 1)
                icon = parts[0] if len(parts) > 1 else "📄"
                name = parts[1] if len(parts) > 1 else rec['page']
                
                # 创建推荐卡片
                card_html = f"""
                <div style="
                    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
                    border: 1px solid rgba(255,255,255,0.2);
                    border-radius: 12px;
                    padding: 16px;
                    margin: 8px 0;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(0,0,0,0.15)';"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                        <span style="font-size: 20px;">{icon}</span>
                        <span style="color: white; font-weight: 600; font-size: 14px;">{name}</span>
                    </div>
                    <div style="color: rgba(255,255,255,0.7); font-size: 12px; margin-bottom: 8px;">
                        {rec['reason']}
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="background: rgba(74, 222, 128, 0.2); color: #4ade80; padding: 2px 8px; border-radius: 8px; font-size: 10px; font-weight: 600;">
                            {rec['type'].upper()}
                        </span>
                        <span style="color: rgba(255,255,255,0.5); font-size: 11px;">
                            评分: {rec['score']:.0f}
                        </span>
                    </div>
                </div>
                """
                
                st.markdown(card_html, unsafe_allow_html=True)
                
                # 添加点击按钮
                if st.button(f"访问 {name}", key=f"rec_{i}_{rec['page']}", use_container_width=True):
                    session['recommendations_clicked'].append(rec['page'])
                    self.track_page_visit(rec['page'])
                    st.success(f"正在跳转到 {rec['page']}")
                    st.rerun()
    
    def render_usage_analytics(self) -> None:
        """渲染使用分析面板"""
        st.markdown("### 📊 使用分析")
        
        # 总体统计
        total_visits = sum(stats['count'] for stats in self.usage_history.values())
        avg_session_time = sum(stats['avg_time'] for stats in self.usage_history.values()) / len(self.usage_history)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("总访问次数", total_visits, "本周")
        
        with col2:
            st.metric("平均停留时间", f"{avg_session_time:.0f}s", "+15s")
        
        with col3:
            st.metric("活跃功能数", len(self.usage_history), "+2")
        
        # 使用频率图表
        if self.usage_history:
            st.markdown("#### 功能使用频率")
            
            # 准备图表数据
            pages = list(self.usage_history.keys())[:8]  # 显示前8个
            counts = [self.usage_history[page]['count'] for page in pages]
            
            # 创建简单的条形图
            chart_data = {
                '功能': [page.split(' ', 1)[1] if ' ' in page else page for page in pages],
                '使用次数': counts
            }
            
            st.bar_chart(chart_data, x='功能', y='使用次数')
    
    def render_navigation_insights(self) -> None:
        """渲染导航洞察面板"""
        st.markdown("### 💡 导航洞察")
        
        session = st.session_state.smart_nav_session
        
        # 会话信息
        session_duration = (datetime.now() - session['session_start']).total_seconds() / 60
        pages_visited = len(set(nav['page'] for nav in session['navigation_path']))
        
        insights = []
        
        # 生成洞察
        if session_duration > 30:
            insights.append("🕒 本次会话时间较长，建议使用收藏功能快速访问常用页面")
        
        if pages_visited > 5:
            insights.append("🔄 您访问了多个页面，可能需要更好的导航组织")
        
        if len(session['recommendations_clicked']) > 0:
            insights.append("✨ 您使用了智能推荐功能，这有助于提高工作效率")
        
        if not insights:
            insights.append("👍 您的导航使用模式很高效！")
        
        # 显示洞察
        for insight in insights:
            st.info(insight)
        
        # 个性化建议
        st.markdown("#### 🎯 个性化建议")
        
        suggestions = []
        
        # 基于使用模式的建议
        most_used = max(self.usage_history.items(), key=lambda x: x[1]['count'])
        suggestions.append(f"💫 将 {most_used[0]} 添加到收藏夹，这是您最常用的功能")
        
        # 基于时间的建议
        current_hour = datetime.now().hour
        if 9 <= current_hour <= 11:
            suggestions.append("🌅 上午时段建议查看数据概览和最新开奖信息")
        elif 14 <= current_hour <= 16:
            suggestions.append("☀️ 下午时段适合进行预测分析和数据查询")
        elif 19 <= current_hour <= 21:
            suggestions.append("🌙 晚间时段可以查看优化建议和系统监控")
        
        for suggestion in suggestions[:2]:  # 显示前2个建议
            st.success(suggestion)
    
    def get_search_suggestions(self, query: str) -> List[str]:
        """获取搜索建议"""
        if not query:
            return []
        
        query_lower = query.lower()
        suggestions = []
        
        # 搜索所有页面
        all_pages = []
        for category_pages in self._get_all_pages().values():
            all_pages.extend(category_pages)
        
        # 模糊匹配
        for page in all_pages:
            page_text = page.lower()
            if query_lower in page_text:
                suggestions.append(page)
        
        # 基于使用历史的建议
        for page in self.usage_history.keys():
            page_text = page.lower()
            if query_lower in page_text and page not in suggestions:
                suggestions.append(page)
        
        return suggestions[:5]  # 返回前5个建议
    
    def save_user_preferences(self, preferences: Dict[str, Any]):
        """保存用户偏好设置"""
        self.user_preferences.update(preferences)
        # 在实际应用中，这里应该保存到数据库或文件
        st.success("偏好设置已保存！")
    
    def export_analytics_data(self) -> Dict[str, Any]:
        """导出分析数据"""
        return {
            'usage_history': self.usage_history,
            'page_analytics': dict(self.page_analytics),
            'user_preferences': self.user_preferences,
            'session_data': st.session_state.smart_nav_session,
            'export_time': datetime.now().isoformat()
        }
