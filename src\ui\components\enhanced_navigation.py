"""
增强版导航组件 - 现代化视觉设计
提供美观的导航界面、悬停效果和视觉反馈
"""

import streamlit as st
from typing import Dict, List, Optional, Tuple
import uuid
from datetime import datetime


class EnhancedNavigationComponent:
    """增强版导航组件"""
    
    def __init__(self):
        """初始化增强版导航组件"""
        self.instance_id = str(uuid.uuid4())[:8]
        self.current_category = None
        self.current_page = None
        
        # 注入现代化CSS样式
        self._inject_modern_styles()
    
    def _inject_modern_styles(self):
        """注入现代化CSS样式"""
        modern_css = """
        <style>
        /* 现代化导航容器 */
        .enhanced-nav-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 24px;
            margin: 16px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        /* 分类标题样式 */
        .nav-category-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        /* 页面按钮样式 */
        .nav-page-button {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 12px 16px;
            margin: 8px 0;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .nav-page-button:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: rgba(255,255,255,0.3);
        }
        
        .nav-page-button:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        /* 活跃状态 */
        .nav-page-button.active {
            background: rgba(255,255,255,0.25);
            border-color: rgba(255,255,255,0.4);
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }
        
        /* 按钮内容 */
        .nav-button-content {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
        }
        
        .nav-button-icon {
            font-size: 20px;
            min-width: 24px;
        }
        
        .nav-button-text {
            font-size: 14px;
            font-weight: 500;
            flex: 1;
        }
        
        .nav-button-badge {
            background: rgba(255,255,255,0.3);
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 11px;
            font-weight: 600;
        }
        
        /* 状态指示器 */
        .status-indicator {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .status-item {
            display: flex;
            justify-content: between;
            align-items: center;
            margin: 8px 0;
            color: white;
        }
        
        .status-label {
            font-size: 13px;
            opacity: 0.9;
        }
        
        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #4ade80;
        }
        
        /* 搜索框样式 */
        .nav-search-box {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 12px 16px;
            color: white;
            width: 100%;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }
        
        .nav-search-box:focus {
            outline: none;
            background: rgba(255,255,255,0.15);
            border-color: rgba(255,255,255,0.4);
            box-shadow: 0 0 0 3px rgba(255,255,255,0.1);
        }
        
        .nav-search-box::placeholder {
            color: rgba(255,255,255,0.6);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .enhanced-nav-container {
                padding: 16px;
                margin: 8px 0;
            }
            
            .nav-page-button {
                padding: 10px 12px;
            }
            
            .nav-button-text {
                font-size: 13px;
            }
        }
        
        /* 加载动画 */
        .nav-loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 脉冲动画 */
        .nav-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        </style>
        """
        
        st.markdown(modern_css, unsafe_allow_html=True)
    
    def render_enhanced_navigation(self) -> Optional[str]:
        """渲染增强版导航界面"""
        
        # 获取功能分类
        categories = self._get_function_categories()
        
        # 渲染导航容器
        nav_html = '<div class="enhanced-nav-container">'
        
        # 添加搜索框
        nav_html += '''
        <div style="margin-bottom: 20px;">
            <input type="text" class="nav-search-box" placeholder="🔍 搜索功能..." id="nav-search">
        </div>
        '''
        
        # 渲染分类和页面
        for category, pages in categories.items():
            nav_html += f'''
            <div class="nav-category-title">
                {category}
            </div>
            '''
            
            for page_name, page_func in pages.items():
                # 检查是否为当前页面
                is_active = self.current_page == page_name
                active_class = "active" if is_active else ""
                
                # 提取图标和文本
                parts = page_name.split(" ", 1)
                icon = parts[0] if len(parts) > 1 else "📄"
                text = parts[1] if len(parts) > 1 else page_name
                
                nav_html += f'''
                <div class="nav-page-button {active_class}" onclick="selectPage('{page_name}')">
                    <div class="nav-button-content">
                        <span class="nav-button-icon">{icon}</span>
                        <span class="nav-button-text">{text}</span>
                    </div>
                </div>
                '''
        
        nav_html += '</div>'
        
        # 添加JavaScript交互
        nav_html += '''
        <script>
        function selectPage(pageName) {
            // 更新Streamlit状态
            window.parent.postMessage({
                type: 'streamlit:setComponentValue',
                value: pageName
            }, '*');
        }
        
        // 搜索功能
        document.getElementById('nav-search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const buttons = document.querySelectorAll('.nav-page-button');
            
            buttons.forEach(button => {
                const text = button.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    button.style.display = 'flex';
                } else {
                    button.style.display = 'none';
                }
            });
        });
        </script>
        '''
        
        # 渲染HTML
        st.markdown(nav_html, unsafe_allow_html=True)
        
        return None
    
    def _get_function_categories(self) -> Dict[str, Dict[str, str]]:
        """获取功能分类"""
        return {
            "🏠 首页概览": {
                "📈 数据概览": "show_data_overview",
                "🎲 最新开奖": "show_latest_draw",
                "📊 系统状态": "show_system_status"
            },
            "🎯 智能预测": {
                "🤖 智能融合预测": "show_intelligent_fusion",
                "📈 趋势分析预测": "show_trend_analysis", 
                "🎯 预测分析": "show_prediction_analysis",
                "🏛️ 模型库管理": "show_model_library"
            },
            "📊 数据分析": {
                "🔍 数据查询": "show_data_query",
                "📊 频率分析": "show_frequency_analysis",
                "💰 销售分析": "show_sales_analysis",
                "📈 和值分布": "show_sum_distribution"
            },
            "🔧 系统管理": {
                "🔄 数据更新": "show_data_update",
                "📊 性能监控": "show_real_time_monitoring",
                "💡 优化建议": "show_optimization_suggestions",
                "🤖 模型训练": "show_training_monitoring"
            },
            "⚙️ 高级功能": {
                "🔬 特征工程": "show_feature_engineering",
                "🧪 A/B测试": "show_ab_testing",
                "📊 数据管理深度": "show_data_management_deep",
                "🔍 系统诊断": "show_bug_detection_status"
            }
        }
    
    def render_status_indicators(self) -> None:
        """渲染增强的状态指示器"""
        status_html = '''
        <div class="status-indicator">
            <div style="color: white; font-weight: 600; margin-bottom: 12px;">
                📊 系统状态
            </div>
            <div class="status-item">
                <span class="status-label">API连接</span>
                <span class="status-value">✅ 正常</span>
            </div>
            <div class="status-item">
                <span class="status-label">数据状态</span>
                <span class="status-value">🔄 实时</span>
            </div>
            <div class="status-item">
                <span class="status-label">系统性能</span>
                <span class="status-value">⚡ 优秀</span>
            </div>
        </div>
        '''
        
        st.markdown(status_html, unsafe_allow_html=True)
