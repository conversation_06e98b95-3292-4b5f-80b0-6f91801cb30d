# 开发环境配置

# 基础配置
environment: development
debug: true
project_name: "福彩3D预测系统"
version: "2025.2.0"

# 数据库配置
database:
  type: sqlite
  name: lottery.db
  echo: true
  echo_pool: false

# API配置
api:
  host: "127.0.0.1"
  port: 8888
  debug: true
  reload: true
  docs_enabled: true
  cors_enabled: true
  rate_limit_enabled: false
  log_level: DEBUG
  workers: 1

# 机器学习配置
ml:
  model_cache_enabled: true
  batch_size: 16
  epochs: 50
  learning_rate: 0.001
  validation_split: 0.2
  use_gpu: false
  monitoring_enabled: true
  quantization_enabled: false
  pruning_enabled: false

# 缓存配置
cache:
  enabled: true
  memory_enabled: true
  redis_enabled: false
  file_enabled: true
  default_ttl: 1800
  monitoring_enabled: true
  compression_enabled: false
  cache_levels:
    - memory
    - file
