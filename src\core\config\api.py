"""
API配置

管理API服务相关的配置参数。
"""

from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class APIConfig(BaseSettings):
    """API配置类"""
    
    # 服务配置
    host: str = Field(default="127.0.0.1", description="API服务主机")
    port: int = Field(default=8888, description="API服务端口")
    debug: bool = Field(default=False, description="调试模式")
    reload: bool = Field(default=False, description="自动重载")
    
    # 安全配置
    secret_key: Optional[str] = Field(default=None, description="密钥")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    allowed_hosts: List[str] = Field(default=["*"], description="允许的主机")
    
    # CORS配置
    cors_enabled: bool = Field(default=True, description="是否启用CORS")
    cors_origins: List[str] = Field(default=["*"], description="CORS允许的源")
    cors_methods: List[str] = Field(default=["*"], description="CORS允许的方法")
    cors_headers: List[str] = Field(default=["*"], description="CORS允许的头部")
    
    # 限流配置
    rate_limit_enabled: bool = Field(default=True, description="是否启用限流")
    rate_limit_requests: int = Field(default=100, description="限流请求数")
    rate_limit_window: int = Field(default=60, description="限流时间窗口(秒)")
    
    # 文档配置
    docs_enabled: bool = Field(default=True, description="是否启用API文档")
    docs_url: str = Field(default="/docs", description="API文档URL")
    redoc_url: str = Field(default="/redoc", description="ReDoc文档URL")
    openapi_url: str = Field(default="/openapi.json", description="OpenAPI规范URL")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    access_log: bool = Field(default=True, description="是否记录访问日志")
    
    # 性能配置
    workers: int = Field(default=1, description="工作进程数")
    timeout: int = Field(default=30, description="请求超时时间")
    keep_alive: int = Field(default=2, description="Keep-Alive超时时间")
    max_requests: int = Field(default=1000, description="最大请求数")
    max_requests_jitter: int = Field(default=50, description="最大请求数抖动")
    
    # WebSocket配置
    websocket_enabled: bool = Field(default=True, description="是否启用WebSocket")
    websocket_ping_interval: int = Field(default=20, description="WebSocket ping间隔")
    websocket_ping_timeout: int = Field(default=20, description="WebSocket ping超时")
    
    class Config:
        env_prefix = "API_"
        env_file = ".env"
        case_sensitive = False
    
    @validator('port')
    def validate_port(cls, v):
        """验证端口号"""
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在1-65535之间')
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是: {", ".join(valid_levels)}')
        return v.upper()
    
    def get_server_url(self) -> str:
        """获取服务器URL"""
        return f"http://{self.host}:{self.port}"
    
    def get_cors_config(self) -> dict:
        """获取CORS配置"""
        if not self.cors_enabled:
            return {}
        
        return {
            "allow_origins": self.cors_origins,
            "allow_methods": self.cors_methods,
            "allow_headers": self.cors_headers,
            "allow_credentials": True,
        }
    
    def get_rate_limit_config(self) -> dict:
        """获取限流配置"""
        if not self.rate_limit_enabled:
            return {}
        
        return {
            "requests": self.rate_limit_requests,
            "window": self.rate_limit_window,
        }
    
    def get_docs_config(self) -> dict:
        """获取文档配置"""
        if not self.docs_enabled:
            return {
                "docs_url": None,
                "redoc_url": None,
                "openapi_url": None,
            }
        
        return {
            "docs_url": self.docs_url,
            "redoc_url": self.redoc_url,
            "openapi_url": self.openapi_url,
        }
    
    def get_uvicorn_config(self) -> dict:
        """获取Uvicorn配置"""
        return {
            "host": self.host,
            "port": self.port,
            "debug": self.debug,
            "reload": self.reload,
            "workers": self.workers if not self.reload else 1,
            "timeout_keep_alive": self.keep_alive,
            "access_log": self.access_log,
            "log_level": self.log_level.lower(),
        }
