# 福彩3D预测系统部署和运行方式（更新版）

## 系统环境要求
### 硬件要求
- **操作系统**: Windows 10 64位 (版本1903或更高)
- **处理器**: Intel i5或AMD Ryzen 5以上
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 2GB可用空间
- **网络**: 稳定的互联网连接

### 软件环境
- **Python版本**: 3.11.9 (严格要求)
- **包管理器**: pip 或 uv
- **开发工具**: Cursor IDE (推荐)
- **浏览器**: Chrome/Edge/Firefox (支持WebSocket)

## 项目结构和配置
### 核心配置文件
```
d:/github/3dyuce/
├── pyproject.toml              # 项目配置和依赖
├── requirements-prod.txt       # 生产环境依赖
├── scheduler_config.json       # 调度器配置
├── start_production_api.py     # API服务启动脚本
├── 一键启动.bat                # 一键启动脚本
├── 正确启动方式.md             # 启动指南
└── venv/                       # 虚拟环境
```

### 环境配置
```toml
# pyproject.toml
[project]
name = "福彩3d-predictor"
version = "2025.1.0"
requires-python = ">=3.11,<3.12"

[project.dependencies]
streamlit = ">=1.28.0,<1.32.0"
fastapi = ">=0.104.0,<0.111.0"
polars = ">=0.19.0,<0.21.0"
torch = ">=2.1.0,<2.3.0"
# ... 其他依赖
```

## ✅ 正确启动方式（严格按序执行）

### 1. 启动API服务（第一步，必须）
```bash
cd D:\github\3dyuce
# 推荐方式（激活虚拟环境）
venv\Scripts\activate && python start_production_api.py
# 或直接使用虚拟环境Python
venv\Scripts\python.exe start_production_api.py
# 或简化命令
python start_production_api.py
```

**API服务配置**：
- 绑定地址：127.0.0.1:8888
- 健康检查：http://127.0.0.1:8888/health
- API文档：http://127.0.0.1:8888/docs
- **验证成功**：健康检查返回JSON格式状态信息

### 2. 启动APScheduler调度器（第二步，可选）
```bash
cd D:\github\3dyuce
# 推荐方式（激活虚拟环境）
venv\Scripts\activate && python scripts/start_scheduler.py --daemon
# 或直接使用虚拟环境Python
venv\Scripts\python.exe scripts/start_scheduler.py --daemon
```

**调度器功能**：
- 数据更新：每天21:30自动执行
- 文件清理：每周日02:00
- 日志清理：每天03:00
- 配置文件：scheduler_config.json

### 3. 启动Streamlit界面（第三步，必须）
**⚠️ 等待5秒确保API服务完全启动后再执行**

```bash
cd D:\github\3dyuce
# 推荐方式（激活虚拟环境）
venv\Scripts\activate && python -m streamlit run src/ui/main.py
# 或直接使用虚拟环境Python
venv\Scripts\python.exe -m streamlit run src/ui/main.py
# 或简化命令
python -m streamlit run src/ui/main.py
```

**界面配置**：
- 绑定地址：127.0.0.1:8501
- 访问地址：http://127.0.0.1:8501
- **验证成功**：页面显示"✅ API服务正常运行"

## 🚀 一键启动方式

### 批处理脚本（推荐）
```batch
# 一键启动.bat
@echo off
echo 🚀 启动福彩3D预测系统...

echo 1. 启动API服务...
start cmd /k "cd /d D:\github\3dyuce && python start_production_api.py"

echo 2. 等待API服务启动...
timeout /t 5

echo 3. 启动Streamlit界面...
cd /d D:\github\3dyuce
python -m streamlit run src/ui/main.py
```

### Python一键启动脚本
```python
# 一键启动.py
import subprocess
import time
import sys

def start_system():
    print("🚀 启动福彩3D预测系统...")
    
    # 1. 启动API服务
    print("📡 启动API服务...")
    api_process = subprocess.Popen([
        sys.executable, "start_production_api.py"
    ], creationflags=subprocess.CREATE_NEW_CONSOLE)
    
    # 2. 等待API服务启动
    print("⏳ 等待API服务启动...")
    time.sleep(5)
    
    # 3. 启动Streamlit界面
    print("🖥️ 启动用户界面...")
    subprocess.run([
        "python", "-m", "streamlit", "run", "src/ui/main.py",
        "--server.port", "8501",
        "--server.address", "127.0.0.1"
    ])
```

## ❌ 已禁用的错误启动方式

**以下启动方式已被禁用，不要尝试使用：**

### API相关
- ❌ `python src/api/production_main.py` (端口配置错误)
- ❌ `python src/api/main.py` (旧版API文件，已弃用)
- ❌ `uvicorn src.api.production_main:app` (直接运行，配置错误)

### UI相关
- ❌ `python src/ui/main.py` (Streamlit应用文件，需通过启动脚本运行)
- ❌ `streamlit run src/ui/main.py` (缺少必要参数)

### 防护措施
1. **移除了所有错误的`__main__`代码块**
2. **添加了提醒注释**，防止意外运行
3. **更新了启动指南**，明确正确方式

## 🔧 服务配置详解

### API服务配置 (start_production_api.py)
```python
import uvicorn
from src.api.production_main import app

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="127.0.0.1",      # 绑定到本地
        port=8888,             # API端口
        log_level="info",      # 日志级别
        access_log=True,       # 访问日志
        reload=False           # 生产模式不重载
    )
```

### Streamlit配置
```toml
# .streamlit/config.toml
[server]
port = 8501
address = "127.0.0.1"
maxUploadSize = 200
enableCORS = false

[browser]
gatherUsageStats = false
serverAddress = "127.0.0.1"
serverPort = 8501

[theme]
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
```

### APScheduler配置 (scheduler_config.json)
```json
{
    "data_update": {
        "enabled": true,
        "schedule": "21:30",
        "timezone": "Asia/Shanghai",
        "retry_attempts": 3,
        "retry_delay": 300
    },
    "log_cleanup": {
        "enabled": true,
        "schedule": "02:00",
        "retention_days": 30
    },
    "performance_check": {
        "enabled": true,
        "interval": 3600
    }
}
```

## 🔍 验证启动成功

### API服务验证
```bash
curl http://127.0.0.1:8888/health
```

**成功响应示例**：
```json
{
  "status": "healthy",
  "timestamp": "2025-07-31T10:30:00Z",
  "database_records": 8351,
  "date_range": "2002-01-01 to 2025-07-23"
}
```

### Streamlit界面验证
浏览器访问：http://127.0.0.1:8501
- ✅ 页面显示：`✅ API服务正常运行`
- ✅ 数据概览显示：`8,351条记录`
- ✅ 所有功能页面正常加载

### APScheduler验证
```bash
python scripts/start_scheduler.py --status
```

## 🛠️ 故障排除

### 如果API启动失败
1. **检查端口占用**：确认8888端口未被占用
2. **确认目录位置**：必须在D:\github\3dyuce目录执行
3. **检查Python环境**：确认Python 3.11.9和依赖包
4. **Bug检测监控错误**：
   - 如果返回500错误，停止API服务
   - 确认Bug检测监控已禁用
   - 重新启动API服务

### 如果Streamlit启动失败
1. **确认API服务状态**：先确保API服务正常运行
2. **检查端口占用**：确认8501端口未被占用
3. **页面显示空白**：
   - 刷新浏览器页面（F5）
   - 清除浏览器缓存
   - 重启Streamlit服务

### 如果APScheduler启动失败
1. **安装依赖**：`pip install apscheduler sqlalchemy`
2. **检查配置**：确认scheduler_config.json存在
3. **检查权限**：确认data/目录写入权限
4. **查看日志**：检查data/logs/scheduler_*.log

### 端口冲突解决
```bash
# 强制停止所有Python进程
taskkill /f /im python.exe

# 重新按顺序启动
cd D:\github\3dyuce
python start_production_api.py
# 等待5秒
python -m streamlit run src/ui/main.py
```

## 📊 可用页面系统

### 主要功能页面
1. **主页面** - 福彩3D预测分析工具主界面
2. **优化建议** - 模型优化建议和参数回测
3. **预测分析仪表板** - 预测结果分析和验证
4. **实时监控** - 系统状态和性能监控
5. **数据管理** - 数据质量分析和管理
6. **特征工程** - 特征选择和工程化
7. **A/B测试** - 模型对比和测试
8. **训练监控** - 模型训练过程监控

### 页面访问特点
- 通过侧边栏导航访问各个页面
- 所有页面都支持响应式布局
- 页面间状态保持和数据共享正常
- 已修复重复页面和执行机制问题

## 📝 重要提醒

### 启动原则
- ✅ **只使用根目录的启动脚本**
- ❌ **不要直接运行src目录下的文件**
- 🔄 **严格按顺序启动：API → 调度器 → 界面**
- 🌐 **确保绑定到127.0.0.1而非0.0.0.0**
- 📁 **确保在项目根目录D:\github\3dyuce执行命令**

### 网络配置
```python
NETWORK_CONFIG = {
    "api_service": "127.0.0.1:8888",
    "streamlit_ui": "127.0.0.1:8501", 
    "websocket": "127.0.0.1:8888/ws",
    "health_check": "127.0.0.1:8888/health",
    "api_docs": "127.0.0.1:8888/docs"
}
```

## 🎯 系统验收标准

### 启动成功标志
- ✅ API健康检查返回正确JSON数据
- ✅ Streamlit界面显示"API服务正常运行"
- ✅ 数据概览显示正确的记录数量
- ✅ 所有功能页面正常加载
- ✅ 页面间导航流畅无错误

### 功能验收标准
- ✅ 预测功能正常工作，结果不固定
- ✅ 数据管理功能可用，支持动态计数
- ✅ 优化建议正常显示
- ✅ 实时监控数据更新正常
- ✅ 自动重训练和手动重训练功能正常

## 🔧 修复完成的功能

经过系统修复，现在具备：
- ✅ **动态数据计数**：不再硬编码数据量
- ✅ **自动重训练机制**：模型自动更新
- ✅ **手动重训练功能**：用户可手动触发训练
- ✅ **预测结果动态化**：预测结果不再固定为"056"
- ✅ **数据同步状态显示**：实时显示数据状态
- ✅ **页面系统修复**：解决重复页面和执行机制问题
- ✅ **错误启动方式禁用**：防止错误的启动方式

## 📚 相关文档

### 完整文档列表
- **正确启动方式.md** - 系统启动指南
- **故障排除指南.md** - 详细的问题诊断和解决方案
- **用户操作手册.md** - 完整的功能使用说明
- **一键启动.bat** - 自动化启动脚本

### 使用建议
1. **首次使用**：先阅读正确启动方式文档
2. **遇到问题**：查看故障排除指南
3. **学习功能**：参考用户操作手册
4. **快速启动**：使用一键启动脚本