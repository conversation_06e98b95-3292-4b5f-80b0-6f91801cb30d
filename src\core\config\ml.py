"""
机器学习配置

管理机器学习模型相关的配置参数。
"""

from typing import Dict, List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class MLConfig(BaseSettings):
    """机器学习配置类"""
    
    # 模型配置
    model_dir: str = Field(default="models", description="模型保存目录")
    model_cache_enabled: bool = Field(default=True, description="是否启用模型缓存")
    model_cache_size: int = Field(default=100, description="模型缓存大小(MB)")
    
    # 训练配置
    batch_size: int = Field(default=32, description="批次大小")
    epochs: int = Field(default=100, description="训练轮数")
    learning_rate: float = Field(default=0.001, description="学习率")
    validation_split: float = Field(default=0.2, description="验证集比例")
    early_stopping_patience: int = Field(default=10, description="早停耐心值")
    
    # 数据配置
    sequence_length: int = Field(default=50, description="序列长度")
    feature_window: int = Field(default=100, description="特征窗口大小")
    prediction_window: int = Field(default=1, description="预测窗口大小")
    data_split_ratio: float = Field(default=0.8, description="训练数据比例")
    
    # CNN-LSTM配置
    cnn_filters: List[int] = Field(default=[64, 128, 256], description="CNN过滤器数量")
    cnn_kernel_size: int = Field(default=3, description="CNN卷积核大小")
    lstm_units: List[int] = Field(default=[128, 64], description="LSTM单元数量")
    dropout_rate: float = Field(default=0.2, description="Dropout比例")
    
    # 优化器配置
    optimizer: str = Field(default="adam", description="优化器类型")
    optimizer_params: Dict = Field(default_factory=dict, description="优化器参数")
    
    # 损失函数配置
    loss_function: str = Field(default="mse", description="损失函数")
    metrics: List[str] = Field(default=["mae"], description="评估指标")
    
    # 预测配置
    confidence_threshold: float = Field(default=0.1, description="置信度阈值")
    max_candidates: int = Field(default=20, description="最大候选数量")
    min_candidates: int = Field(default=5, description="最小候选数量")
    
    # 融合配置
    fusion_enabled: bool = Field(default=True, description="是否启用模型融合")
    fusion_weights: Dict[str, float] = Field(
        default_factory=lambda: {
            "trend_analysis": 0.3,
            "pattern_prediction": 0.3,
            "lstm_prediction": 0.4
        },
        description="融合权重"
    )
    
    # 性能配置
    use_gpu: bool = Field(default=False, description="是否使用GPU")
    gpu_memory_limit: Optional[int] = Field(default=None, description="GPU内存限制(MB)")
    num_threads: int = Field(default=4, description="线程数量")
    
    # 模型压缩配置
    quantization_enabled: bool = Field(default=False, description="是否启用量化")
    pruning_enabled: bool = Field(default=False, description="是否启用剪枝")
    compression_ratio: float = Field(default=0.5, description="压缩比例")
    
    # 监控配置
    monitoring_enabled: bool = Field(default=True, description="是否启用监控")
    metrics_collection_interval: int = Field(default=60, description="指标收集间隔(秒)")
    performance_tracking: bool = Field(default=True, description="是否跟踪性能")
    
    class Config:
        env_prefix = "ML_"
        env_file = ".env"
        case_sensitive = False
    
    @validator('validation_split')
    def validate_validation_split(cls, v):
        """验证验证集比例"""
        if not 0 < v < 1:
            raise ValueError('验证集比例必须在0-1之间')
        return v
    
    @validator('data_split_ratio')
    def validate_data_split_ratio(cls, v):
        """验证数据分割比例"""
        if not 0 < v < 1:
            raise ValueError('数据分割比例必须在0-1之间')
        return v
    
    @validator('dropout_rate')
    def validate_dropout_rate(cls, v):
        """验证Dropout比例"""
        if not 0 <= v <= 1:
            raise ValueError('Dropout比例必须在0-1之间')
        return v
    
    @validator('confidence_threshold')
    def validate_confidence_threshold(cls, v):
        """验证置信度阈值"""
        if not 0 <= v <= 1:
            raise ValueError('置信度阈值必须在0-1之间')
        return v
    
    @validator('compression_ratio')
    def validate_compression_ratio(cls, v):
        """验证压缩比例"""
        if not 0 < v <= 1:
            raise ValueError('压缩比例必须在0-1之间')
        return v
    
    def get_model_path(self, model_name: str) -> str:
        """获取模型文件路径"""
        return f"{self.model_dir}/{model_name}.pkl"
    
    def get_optimizer_config(self) -> dict:
        """获取优化器配置"""
        config = {"learning_rate": self.learning_rate}
        config.update(self.optimizer_params)
        return config
    
    def get_training_config(self) -> dict:
        """获取训练配置"""
        return {
            "batch_size": self.batch_size,
            "epochs": self.epochs,
            "validation_split": self.validation_split,
            "early_stopping_patience": self.early_stopping_patience,
        }
    
    def get_model_architecture_config(self) -> dict:
        """获取模型架构配置"""
        return {
            "sequence_length": self.sequence_length,
            "cnn_filters": self.cnn_filters,
            "cnn_kernel_size": self.cnn_kernel_size,
            "lstm_units": self.lstm_units,
            "dropout_rate": self.dropout_rate,
        }
    
    def get_prediction_config(self) -> dict:
        """获取预测配置"""
        return {
            "confidence_threshold": self.confidence_threshold,
            "max_candidates": self.max_candidates,
            "min_candidates": self.min_candidates,
        }
