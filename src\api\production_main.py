#!/usr/bin/env python3
"""
生产版FastAPI主应用

完整功能的高性能RESTful API服务
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime

from fastapi import (FastAPI, HTTPException, Query, WebSocket,
                     WebSocketDisconnect)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse

sys.path.append('src')

# 导入缓存系统
from core.cache import CacheManager, MemoryCache
# 导入配置
from core.config import get_settings
# 导入异常处理系统
from core.exceptions import LotterySystemException
from core.exceptions.middleware import ExceptionHandlerMiddleware

# 获取配置
settings = get_settings()

# 配置日志
logging.basicConfig(level=getattr(logging, settings.api.log_level))
logger = logging.getLogger(__name__)

# 创建FastAPI应用
docs_config = settings.api.get_docs_config()
app = FastAPI(
    title=settings.project_name,
    description="高性能彩票数据查询和分析服务",
    version=settings.version,
    debug=settings.api.debug,
    **docs_config
)

# 🔍 集成增强版Bug检测系统 - 实时监控和WebSocket支持
try:
    from src.bug_detection.alerts.notification_manager import \
        initialize_notification_manager
    from src.bug_detection.core.database_manager import DatabaseManager
    from src.bug_detection.monitoring.simple_monitor import init_simple_monitor
    from src.bug_detection.realtime.event_bus import initialize_event_bus
    from src.bug_detection.realtime.real_time_analyzer import \
        initialize_real_time_analyzer
    from src.bug_detection.realtime.stream_processor import \
        initialize_stream_processor
    from src.bug_detection.realtime.websocket_manager import \
        initialize_websocket_manager

    # 初始化数据库管理器
    bug_db_manager = DatabaseManager()

    # 初始化简化的性能监控器
    monitor = init_simple_monitor(bug_db_manager)

    # 全局实时监控组件
    event_bus = None
    websocket_manager = None
    stream_processor = None
    real_time_analyzer = None
    notification_manager = None

    if monitor:
        logger.info("✅ Bug检测系统已集成到API服务")
        logger.info("✅ 简化API性能监控已启用")
    else:
        logger.warning("⚠️ API性能监控初始化失败")

except ImportError as e:
    logger.warning(f"⚠️ Bug检测系统未安装，跳过集成: {e}")
except Exception as e:
    logger.error(f"❌ Bug检测系统集成失败: {e}")
    import traceback
    logger.error(f"详细错误: {traceback.format_exc()}")
    logger.info("🔄 API服务将继续运行，但不包含Bug检测功能")

    # 设置为None以避免后续错误
    bug_db_manager = None
    monitor = None
    event_bus = None
    websocket_manager = None
    stream_processor = None
    real_time_analyzer = None
    notification_manager = None

# 添加异常处理中间件
app.add_middleware(ExceptionHandlerMiddleware, debug=settings.debug)

# 配置CORS
cors_config = settings.api.get_cors_config()
if cors_config:
    app.add_middleware(CORSMiddleware, **cors_config)

# 全局数据引擎实例
data_engine = None
data_update_service = None
prediction_service = None
cache_manager = None

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global data_engine, cache_manager
    logger.info("🚀 FastAPI应用启动中...")

    # 初始化缓存管理器
    cache_manager = CacheManager([MemoryCache()])
    
    try:
        # 延迟导入，避免初始化问题
        from core.data_engine import DataEngine

        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        # 初始化数据引擎
        data_engine = DataEngine("data/lottery.db")

        # 检查数据库是否有数据
        record_count = data_engine.db_manager.get_records_count()
        logger.info(f"数据库记录数: {record_count}")

        if record_count == 0:
            logger.info("数据库为空，从文件加载数据...")
            try:
                from data.parser import DataParser
                
                data_file = 'data/raw/3d_data_20250714_144231.txt'
                if os.path.exists(data_file):
                    with open(data_file, 'r', encoding='utf-8') as f:
                        raw_data = f.read()
                    
                    parser = DataParser()
                    records, quality_report = parser.parse_data(raw_data)
                    data_engine.load_data_from_records(records, save_to_db=True)
                    logger.info(f"数据加载完成: {len(records)} 条记录")
                else:
                    logger.warning(f"数据文件不存在: {data_file}")
            except Exception as e:
                logger.error(f"数据加载失败: {e}")
        else:
            # 数据库有数据，加载到Polars引擎
            try:
                data_engine.load_data_from_database()
                logger.info("数据已从数据库加载到Polars引擎")
            except Exception as e:
                logger.error(f"从数据库加载数据到Polars引擎失败: {e}")

        # 初始化数据更新服务
        try:
            from services.data_update_service import DataUpdateService
            global data_update_service
            data_update_service = DataUpdateService(data_engine=data_engine)
            logger.info("数据更新服务初始化完成")
        except Exception as e:
            logger.error(f"数据更新服务初始化失败: {e}")

        # 初始化预测服务
        try:
            from prediction.prediction_service import PredictionService
            global prediction_service
            prediction_service = PredictionService(data_engine=data_engine)
            logger.info("预测服务初始化完成")
        except Exception as e:
            logger.error(f"预测服务初始化失败: {e}")

        # 初始化实时监控组件
        try:
            global event_bus, websocket_manager, stream_processor, real_time_analyzer, notification_manager

            # 检查实时监控函数是否可用
            if 'initialize_event_bus' in globals():
                # 初始化事件总线
                try:
                    event_bus = await initialize_event_bus()
                    if event_bus:
                        logger.info("✅ 事件总线初始化完成")
                    else:
                        logger.warning("⚠️ 事件总线初始化失败，继续以简化模式运行")
                except Exception as e:
                    logger.error(f"❌ 事件总线初始化异常: {e}")
                    event_bus = None

                # 初始化WebSocket管理器
                try:
                    websocket_manager = await initialize_websocket_manager()
                    if websocket_manager:
                        logger.info("✅ WebSocket管理器初始化完成")
                    else:
                        logger.warning("⚠️ WebSocket管理器初始化失败，WebSocket功能将不可用")
                except Exception as e:
                    logger.error(f"❌ WebSocket管理器初始化异常: {e}")
                    websocket_manager = None

                # 初始化流式处理器
                try:
                    stream_processor = await initialize_stream_processor()
                    if stream_processor:
                        logger.info("✅ 流式处理器初始化完成")
                    else:
                        logger.warning("⚠️ 流式处理器初始化失败")
                except Exception as e:
                    logger.error(f"❌ 流式处理器初始化异常: {e}")
                    stream_processor = None

                # 初始化实时分析引擎
                try:
                    real_time_analyzer = await initialize_real_time_analyzer()
                    if real_time_analyzer:
                        logger.info("✅ 实时分析引擎初始化完成")
                    else:
                        logger.warning("⚠️ 实时分析引擎初始化失败")
                except Exception as e:
                    logger.error(f"❌ 实时分析引擎初始化异常: {e}")
                    real_time_analyzer = None

                # 初始化通知管理器
                try:
                    notification_manager = await initialize_notification_manager(bug_db_manager, websocket_manager)
                    if notification_manager:
                        logger.info("✅ 通知管理器初始化完成")
                    else:
                        logger.warning("⚠️ 通知管理器初始化失败")
                except Exception as e:
                    logger.error(f"❌ 通知管理器初始化异常: {e}")
                    notification_manager = None

                logger.info("🚀 实时监控系统初始化完成（部分组件可能以简化模式运行）")
            else:
                logger.warning("⚠️ 实时监控组件未安装，跳过初始化")

        except Exception as e:
            logger.error(f"❌ 实时监控系统初始化失败: {e}")
            logger.info("🔄 API服务将继续运行，但实时监控功能可能不可用")

        logger.info("✅ FastAPI应用启动完成")
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()

@app.get("/health")
async def health_check():
    """健康检查接口"""
    # 开始性能监控
    import time
    start_time = time.time()
    status_code = 200

    try:
        if data_engine is None:
            status_code = 500
            result = {
                "status": "error",
                "message": "Data engine not initialized",
                "timestamp": datetime.now().isoformat(),
                "database_records": 0,
                "date_range": "No data"
            }
        else:
            record_count = data_engine.db_manager.get_records_count()
            date_range = data_engine.db_manager.get_date_range()

            result = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "database_records": record_count,
                "date_range": f"{date_range[0]} to {date_range[1]}" if date_range and date_range[0] else "No data"
            }

        # 记录性能数据
        response_time = time.time() - start_time
        try:
            from src.bug_detection.monitoring.simple_monitor import \
                record_manual_request
            record_manual_request("/health", response_time, status_code)
        except Exception as monitor_error:
            logger.debug(f"Performance monitoring error: {monitor_error}")

        return result

    except Exception as e:
        status_code = 500
        logger.error(f"Health check error: {e}")
        import traceback
        traceback.print_exc()

        # 记录性能数据（即使出错也要记录）
        response_time = time.time() - start_time
        try:
            from src.bug_detection.monitoring.simple_monitor import \
                record_manual_request
            record_manual_request("/health", response_time, status_code)
        except Exception as monitor_error:
            logger.debug(f"Performance monitoring error: {monitor_error}")

        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/v1/stats/basic")
async def get_basic_stats(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取基础统计信息"""
    # 开始性能监控
    from src.bug_detection.monitoring.simple_monitor import (
        end_timing_and_record, start_timing)
    monitor_start = start_timing()
    status_code = 200

    if data_engine is None:
        end_timing_and_record(monitor_start, "/api/v1/stats/basic", 503)
        from core.exceptions.business import DataEngineException
        raise DataEngineException("数据引擎未初始化，请稍后重试")

    try:
        start_time = time.time()
        stats = data_engine.get_basic_stats(use_cache=use_cache)
        query_time = time.time() - start_time

        # 防御性编程：检查返回的数据结构
        if not stats:
            logger.error("get_basic_stats returned empty result")
            status_code = 500
            raise HTTPException(status_code=500, detail="No statistics data available")

        # 记录调试信息
        logger.info(f"Stats keys: {list(stats.keys())}")

        # 安全地提取字段，提供默认值
        result = {
            "total_records": stats.get("total_records", 0),
            "date_range": stats.get("date_range", {"start": "N/A", "end": "N/A"}),
            "sum_value_stats": stats.get("sum_value_stats", {}),
            "span_value_stats": stats.get("span_value_stats", {}),
            "sales_amount_stats": stats.get("sales_amount_stats", {}),
            "data_freshness": stats.get("data_freshness", {}),
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }

        return result

    except Exception as e:
        status_code = 500
        logger.error(f"Basic stats error: {e}")
        logger.error(f"Data engine state: {data_engine is not None}")
        if data_engine and hasattr(data_engine, 'polars_engine'):
            logger.error(f"Polars engine DF state: {data_engine.polars_engine.df is not None}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 记录性能数据
        end_timing_and_record(monitor_start, "/api/v1/stats/basic", status_code)

@app.get("/api/v1/analysis/frequency")
async def get_frequency_analysis(
    position: str = Query("all", description="分析位置: all, hundreds, tens, units"),
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """
    获取号码频率分析

    返回数据格式说明：
    - hundreds: 百位数字频率列表 [{"digit": "0", "count": 813}, ...]
    - tens: 十位数字频率列表 [{"digit": "0", "count": 852}, ...]
    - units: 个位数字频率列表 [{"digit": "0", "count": 856}, ...]
    - position_frequency: 位置频率列表 [{"数字": "0", "位置": "百位", "频率": 813}, ...]
    - digit_frequency: 综合数字频率字典 {"0": 2521, "1": 2522, ...}
    - total_records: 总记录数
    - date_range: 数据时间范围
    - latest_period: 最新期号
    """
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        if position not in ["all", "hundreds", "tens", "units"]:
            raise HTTPException(status_code=400, detail="Invalid position parameter")
        
        start_time = time.time()
        analysis = data_engine.get_frequency_analysis(position, use_cache=use_cache)
        query_time = time.time() - start_time
        
        # 格式化响应以匹配前端期望
        response_data = {
            "success": True,
            "position": position,
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache,
            **analysis  # 展开analysis中的所有字段
        }

        return response_data
    except Exception as e:
        logger.error(f"Frequency analysis error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analysis/sum-distribution")
async def get_sum_distribution(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取和值分布分析"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        distribution = data_engine.get_sum_distribution(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "sum_distribution": distribution["sum_distribution"],
            "trial_sum_distribution": distribution["trial_sum_distribution"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Sum distribution error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analysis/sales")
async def get_sales_analysis(use_cache: bool = Query(True, description="是否使用缓存")):
    """获取销售额分析"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        analysis = data_engine.get_sales_analysis(use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "overall": analysis["overall"],
            "yearly": analysis["yearly"],
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Sales analysis error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/data/query")
async def query_data(
    start_date: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期 (YYYY-MM-DD)"),
    min_sum: int = Query(None, description="最小和值"),
    max_sum: int = Query(None, description="最大和值"),
    limit: int = Query(100, description="返回记录数限制", le=10000),
    use_polars: bool = Query(False, description="是否强制使用Polars引擎")
):
    """数据查询接口"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        
        if start_date and end_date:
            df = data_engine.query_by_date_range(start_date, end_date, use_polars=use_polars)
        elif min_sum is not None and max_sum is not None:
            df = data_engine.query_by_sum_range(min_sum, max_sum, use_polars=use_polars)
        else:
            raise HTTPException(status_code=400, detail="Must provide either date range or sum range")
        
        if len(df) > limit:
            df = df.head(limit)
        
        records = df.to_dicts()
        query_time = time.time() - start_time
        
        return {
            "records": records,
            "total_count": len(records),
            "query_time_ms": round(query_time * 1000, 2),
            "query_params": {
                "start_date": start_date,
                "end_date": end_date,
                "min_sum": min_sum,
                "max_sum": max_sum,
                "limit": limit,
                "use_polars": use_polars
            }
        }
    except Exception as e:
        logger.error(f"Data query error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/analysis/trends")
async def get_trends_analysis(
    days: int = Query(30, description="分析天数", ge=1, le=365),
    use_cache: bool = Query(True, description="是否使用缓存")
):
    """获取趋势分析"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        trends = data_engine.get_recent_trends(days, use_cache=use_cache)
        query_time = time.time() - start_time
        
        return {
            "period": trends.get("period", f"最近{days}期"),
            "trends": trends,
            "query_time_ms": round(query_time * 1000, 2),
            "cached": use_cache
        }
    except Exception as e:
        logger.error(f"Trends analysis error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/system/performance")
async def get_performance_stats():
    """获取系统性能统计"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        perf_stats = data_engine.get_performance_stats()
        db_info = data_engine.db_manager.get_database_info()
        
        return {
            "performance_stats": perf_stats,
            "database_info": db_info,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Performance stats error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/system/cache/clear")
async def clear_cache():
    """清理过期缓存"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")
    
    try:
        start_time = time.time()
        result = data_engine.optimize_performance()
        operation_time = time.time() - start_time
        
        return {
            "message": "Cache cleared successfully",
            "expired_cache_cleared": result["expired_cache_cleared"],
            "operation_time_ms": round(operation_time * 1000, 2),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Cache clear error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# 数据更新相关API端点

@app.get("/api/v1/data/update/status")
async def get_update_status():
    """获取数据更新状态"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        status = await data_update_service.check_update_status()
        return status
    except Exception as e:
        logger.error(f"Get update status error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/data/update/trigger")
async def trigger_update(force_update: bool = Query(False, description="是否强制更新")):
    """手动触发数据更新"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        result = await data_update_service.trigger_update(force_update=force_update)
        return result
    except Exception as e:
        logger.error(f"Trigger update error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/data/update/history")
async def get_update_history(limit: int = Query(10, description="返回记录数限制", ge=1, le=100)):
    """获取更新历史记录"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        history = data_update_service.get_update_history(limit=limit)
        return history
    except Exception as e:
        logger.error(f"Get update history error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/data/update/progress/{update_id}")
async def get_update_progress(update_id: str):
    """获取特定更新的进度"""
    if data_update_service is None:
        raise HTTPException(status_code=503, detail="Data update service not initialized")

    try:
        progress = data_update_service.get_update_progress(update_id)
        return progress
    except Exception as e:
        logger.error(f"Get update progress error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/data/refresh")
async def refresh_data():
    """刷新数据引擎数据"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")

    try:
        logger.info("收到数据刷新请求")
        result = data_engine.refresh_data()

        if result["success"]:
            logger.info(f"数据刷新成功: {result['old_count']} -> {result['new_count']} 条记录")
            return {
                "success": True,
                "message": result["message"],
                "data": {
                    "old_count": result["old_count"],
                    "new_count": result["new_count"],
                    "records_added": result["records_added"],
                    "old_latest_period": result.get("old_latest_period"),
                    "new_latest_period": result.get("new_latest_period"),
                    "refresh_time_ms": round(result["refresh_time"] * 1000, 2)
                },
                "timestamp": result["timestamp"]
            }
        else:
            logger.error(f"数据刷新失败: {result['message']}")
            return {
                "success": False,
                "message": result["message"],
                "timestamp": result["timestamp"]
            }

    except Exception as e:
        logger.error(f"Data refresh error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/data/status")
async def get_data_status():
    """获取数据状态信息"""
    if data_engine is None:
        raise HTTPException(status_code=503, detail="Data engine not initialized")

    try:
        logger.debug("获取数据状态信息")
        version_info = data_engine.check_data_version()

        return {
            "success": True,
            "data": version_info,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Get data status error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# 预测相关API端点

@app.post("/api/v1/prediction/train")
async def train_predictors(force_retrain: bool = Query(False, description="是否强制重新训练")):
    """训练预测模型"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        result = prediction_service.train_all_predictors(force_retrain=force_retrain)
        return result
    except Exception as e:
        logger.error(f"Train predictors error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/predict")
async def get_predictions(predictors: str = Query(None, description="指定预测器名称，多个用逗号分隔")):
    """获取预测结果"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        predictor_names = None
        if predictors:
            predictor_names = [name.strip() for name in predictors.split(",")]

        result = prediction_service.get_predictions(predictor_names)
        return result
    except Exception as e:
        logger.error(f"Get predictions error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/info")
async def get_predictor_info():
    """获取预测器信息"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        info = prediction_service.get_predictor_info()
        return info
    except Exception as e:
        logger.error(f"Get predictor info error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/history")
async def get_prediction_history(limit: int = Query(10, description="返回记录数限制", ge=1, le=100)):
    """获取预测历史"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        history = prediction_service.get_prediction_history(limit=limit)
        return history
    except Exception as e:
        logger.error(f"Get prediction history error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/evaluate")
async def evaluate_predictions(
    start_date: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期 (YYYY-MM-DD)")
):
    """评估预测准确率"""
    if prediction_service is None:
        raise HTTPException(status_code=503, detail="Prediction service not initialized")

    try:
        result = prediction_service.evaluate_predictions(start_date, end_date)
        return result
    except Exception as e:
        logger.error(f"Evaluate predictions error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# 新增：准确性导向融合预测API端点
# ============================================================================

# 导入新的预测模块
try:
    from core.accuracy_focused_fusion import (AccuracyFocusedFusion,
                                              ModelPrediction,
                                              PredictionResult)
    from core.model_performance_tracker import ModelPerformanceTracker
    from core.number_ranking_system import NumberRankingSystem, RankingItem

    # 全局预测系统实例
    performance_tracker = None
    fusion_engine = None
    ranking_system = None

    def get_prediction_systems():
        """获取预测系统实例"""
        global performance_tracker, fusion_engine, ranking_system

        if performance_tracker is None:
            performance_tracker = ModelPerformanceTracker()

        if fusion_engine is None:
            fusion_engine = AccuracyFocusedFusion(performance_tracker)

        if ranking_system is None:
            ranking_system = NumberRankingSystem(fusion_engine, performance_tracker)

        return performance_tracker, fusion_engine, ranking_system

    # 数据模型定义
    from typing import Any, Dict, List, Optional

    from pydantic import BaseModel, Field

    class PredictionRequest(BaseModel):
        """预测请求模型"""
        period_number: Optional[str] = Field(None, description="期号")
        candidate_count: int = Field(10, ge=5, le=50, description="候选数量")
        confidence_threshold: float = Field(0.3, ge=0.1, le=0.9, description="置信度阈值")
        window_size: int = Field(50, ge=10, le=200, description="历史数据窗口大小")

    class BestPrediction(BaseModel):
        """最佳预测模型"""
        number: str = Field(..., description="推荐号码")
        confidence: float = Field(..., description="预测置信度")
        fusion_method: str = Field(..., description="融合方法")
        recommendation_level: str = Field(..., description="推荐等级")
        historical_hit_rate: float = Field(..., description="历史命中率")
        prediction_basis: str = Field(..., description="预测依据")
        model_support: List[str] = Field(..., description="支持模型列表")

    class RankingItemResponse(BaseModel):
        """排行榜项目响应模型"""
        rank: int = Field(..., description="排名")
        number: str = Field(..., description="号码")
        confidence: float = Field(..., description="置信度")
        composite_score: float = Field(..., description="综合评分")
        model_support_count: int = Field(..., description="模型支持数")
        historical_hit_rate: float = Field(..., description="历史命中率")
        recommendation_level: str = Field(..., description="推荐等级")
        support_models: List[str] = Field(..., description="支持模型")

    class ModelPerformanceSummary(BaseModel):
        """模型性能摘要"""
        model_name: str = Field(..., description="模型名称")
        accuracy_rate: float = Field(..., description="准确率")
        total_predictions: int = Field(..., description="总预测次数")
        correct_predictions: int = Field(..., description="正确预测次数")
        last_prediction: Optional[str] = Field(None, description="最后预测时间")
        current_weight: float = Field(..., description="当前权重")

    class PredictionMetadata(BaseModel):
        """预测元数据"""
        prediction_time: datetime = Field(..., description="预测时间")
        data_window_size: int = Field(..., description="数据窗口大小")
        total_candidates: int = Field(..., description="候选总数")
        fusion_strategy: str = Field(..., description="融合策略")
        model_weights: Dict[str, float] = Field(..., description="模型权重")

    class SingleBestPredictionResponse(BaseModel):
        """单一最优预测响应"""
        best_prediction: BestPrediction
        ranking_list: List[RankingItemResponse]
        model_performance: List[ModelPerformanceSummary]
        prediction_metadata: PredictionMetadata
        success: bool = True
        message: str = "预测成功"

    def _get_mock_model_predictions() -> List[ModelPrediction]:
        """获取模拟的模型预测结果"""
        import random

        models = ['markov_enhanced', 'deep_learning_cnn_lstm', 'trend_analyzer', 'intelligent_fusion']
        predictions = []

        for model in models:
            # 生成随机候选号码和置信度
            candidates = {}
            for _ in range(10):
                number = f"{random.randint(0, 9)}{random.randint(0, 9)}{random.randint(0, 9)}"
                confidence = random.uniform(0.1, 0.9)
                candidates[number] = confidence

            # 按置信度排序
            sorted_candidates = sorted(candidates.items(), key=lambda x: x[1], reverse=True)
            top_candidate = sorted_candidates[0][0]
            top_confidence = sorted_candidates[0][1]

            prediction = ModelPrediction(
                model_name=model,
                top_candidate=top_candidate,
                top_confidence=top_confidence,
                all_candidates=candidates
            )
            predictions.append(prediction)

        return predictions

    @app.post("/api/v1/prediction/single-best", response_model=SingleBestPredictionResponse)
    async def get_single_best_prediction(request: PredictionRequest):
        """获取单一最优预测"""
        try:
            logger.info(f"开始单一最优预测，期号: {request.period_number}")

            # 获取预测系统实例
            tracker, fusion, ranking = get_prediction_systems()

            # 模拟获取各模型预测结果
            model_predictions = _get_mock_model_predictions()

            # 获取单一最优预测
            best_result = fusion.get_single_best_prediction(model_predictions)

            # 生成排行榜
            ranking_list = ranking.generate_ranking_list(model_predictions, request.candidate_count)

            # 获取模型性能
            performance_summary = tracker.get_model_performance_summary()
            model_weights = tracker.calculate_dynamic_weights()

            # 构建响应
            response = SingleBestPredictionResponse(
                best_prediction=BestPrediction(
                    number=best_result.number,
                    confidence=best_result.confidence,
                    fusion_method=best_result.method,
                    recommendation_level=ranking.get_recommendation_level(best_result.confidence),
                    historical_hit_rate=ranking.get_historical_hit_rate(best_result.number),
                    prediction_basis=fusion.get_fusion_explanation(best_result),
                    model_support=best_result.model_support or []
                ),
                ranking_list=[
                    RankingItemResponse(
                        rank=item.rank,
                        number=item.number,
                        confidence=item.confidence,
                        composite_score=item.composite_score,
                        model_support_count=item.model_support_count,
                        historical_hit_rate=item.historical_hit_rate,
                        recommendation_level=item.recommendation_level,
                        support_models=item.support_models or []
                    ) for item in ranking_list
                ],
                model_performance=[
                    ModelPerformanceSummary(
                        model_name=model,
                        accuracy_rate=stats['accuracy_rate'],
                        total_predictions=stats['total_predictions'],
                        correct_predictions=stats['correct_predictions'],
                        last_prediction=stats['last_prediction'],
                        current_weight=model_weights.get(model, 0.25)
                    ) for model, stats in performance_summary.items()
                ],
                prediction_metadata=PredictionMetadata(
                    prediction_time=datetime.now(),
                    data_window_size=request.window_size,
                    total_candidates=len(ranking_list),
                    fusion_strategy=best_result.method,
                    model_weights=model_weights
                )
            )

            logger.info(f"单一最优预测完成: {best_result.number}")
            return response

        except Exception as e:
            logger.error(f"单一最优预测失败: {e}")
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")

    @app.get("/api/v1/models/performance")
    async def get_model_performance():
        """获取模型性能统计"""
        try:
            logger.info("获取模型性能统计")

            # 获取预测系统实例
            tracker, fusion, ranking = get_prediction_systems()

            performance_summary = tracker.get_model_performance_summary()
            model_weights = tracker.calculate_dynamic_weights()

            models = [
                ModelPerformanceSummary(
                    model_name=model,
                    accuracy_rate=stats['accuracy_rate'],
                    total_predictions=stats['total_predictions'],
                    correct_predictions=stats['correct_predictions'],
                    last_prediction=stats['last_prediction'],
                    current_weight=model_weights.get(model, 0.25)
                ) for model, stats in performance_summary.items()
            ]

            # 计算整体统计
            total_predictions = sum(m.total_predictions for m in models)
            total_correct = sum(m.correct_predictions for m in models)
            overall_accuracy = total_correct / total_predictions if total_predictions > 0 else 0

            overall_stats = {
                'total_predictions': total_predictions,
                'total_correct': total_correct,
                'overall_accuracy': overall_accuracy,
                'active_models': len([m for m in models if m.total_predictions > 0]),
                'best_model': max(models, key=lambda x: x.accuracy_rate).model_name if models else None
            }

            response = {
                'models': models,
                'overall_stats': overall_stats,
                'last_updated': datetime.now()
            }

            logger.info("模型性能统计获取完成")
            return response

        except Exception as e:
            logger.error(f"获取模型性能失败: {e}")
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"获取模型性能失败: {str(e)}")

except ImportError as e:
    logger.warning(f"新预测模块导入失败: {e}")

    # 如果新模块导入失败，提供备用端点
    @app.post("/api/v1/prediction/single-best")
    async def get_single_best_prediction_fallback(request: dict):
        """备用预测端点"""
        return {
            "success": False,
            "message": "新预测模块未正确安装，请检查核心模块",
            "error": "Module import failed"
        }

    @app.get("/api/v1/models/performance")
    async def get_model_performance_fallback():
        """备用模型性能端点"""
        return {
            "success": False,
            "message": "新预测模块未正确安装，请检查核心模块",
            "error": "Module import failed"
        }

# ============================================================================
# 原有智能融合预测API端点
# ============================================================================

@app.post("/api/v1/prediction/intelligent-fusion/train")
async def train_intelligent_fusion():
    """训练智能融合模型"""
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem

        intelligent_system = IntelligentFusionSystem()
        training_result = intelligent_system.train_all_models()

        return {
            "success": training_result.get('success', False),
            "message": "智能融合模型训练完成" if training_result.get('success') else "训练失败",
            "training_details": training_result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Train intelligent fusion error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/intelligent-fusion/predict")
async def intelligent_fusion_predict(
    prediction_mode: str = Query("智能融合", description="预测模式"),
    max_candidates: int = Query(20, description="候选数量", ge=5, le=50),
    confidence_threshold: float = Query(0.5, description="置信度阈值", ge=0.1, le=1.0),
    auto_train: bool = Query(True, description="是否自动训练模型")
):
    """智能融合预测"""
    try:
        from core.database import DatabaseManager
        from prediction.intelligent_fusion import IntelligentFusionSystem

        # 初始化系统
        intelligent_system = IntelligentFusionSystem()

        # 检查是否需要训练
        training_needed = False
        training_reason = ""

        if auto_train:
            if not intelligent_system.fusion_ready:
                training_needed = True
                training_reason = "模型未训练"
            elif hasattr(intelligent_system, '_check_data_changed') and intelligent_system._check_data_changed():
                training_needed = True
                training_reason = "检测到数据变化"

            if training_needed:
                logger.info(f"{training_reason}，开始自动训练...")
                training_result = intelligent_system.train_all_models(force_retrain=True)

                if not training_result.get('success', False):
                    return {
                        "success": False,
                        "error": f"模型训练失败: {training_reason}",
                        "training_details": training_result,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    logger.info(f"模型训练成功: {training_reason}")
            else:
                logger.info("模型已训练且数据未变化，跳过训练")

        # 获取历史数据
        db_manager = DatabaseManager("data/lottery.db")
        recent_records = db_manager.get_recent_records(50)

        if not recent_records:
            raise HTTPException(status_code=400, detail="数据库中没有足够的历史数据")

        test_data = [record.numbers for record in recent_records]

        # 根据模式进行预测
        if prediction_mode == "智能融合":
            prediction = intelligent_system.generate_fusion_prediction(
                test_data,
                max_candidates=max_candidates,
                confidence_threshold=confidence_threshold
            )
        elif prediction_mode == "趋势分析":
            prediction = intelligent_system.generate_trend_predictions(test_data)
        elif prediction_mode == "形态预测":
            prediction = intelligent_system.generate_pattern_predictions(test_data)
        else:
            prediction = intelligent_system.generate_fusion_prediction(
                test_data,
                max_candidates=max_candidates,
                confidence_threshold=confidence_threshold
            )

        if 'error' in prediction:
            return {
                "success": False,
                "error": prediction['error'],
                "timestamp": datetime.now().isoformat()
            }

        return {
            "success": True,
            "prediction": prediction,
            "prediction_mode": prediction_mode,
            "data_count": len(test_data),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Intelligent fusion predict error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/intelligent-fusion/status")
async def intelligent_fusion_status():
    """获取智能融合系统状态"""
    try:
        from prediction.intelligent_fusion import IntelligentFusionSystem

        intelligent_system = IntelligentFusionSystem()
        system_summary = intelligent_system.get_system_summary()

        return {
            "success": True,
            "status": system_summary,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Get intelligent fusion status error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# 增量预测更新系统API端点
# ============================================================================

@app.get("/api/v1/prediction/incremental/status")
async def get_incremental_prediction_status():
    """获取增量预测系统状态"""
    try:
        from core.incremental_predictor import get_incremental_predictor

        predictor = get_incremental_predictor()
        status = predictor.get_update_status()

        return {
            "success": True,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Get incremental prediction status error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/prediction/incremental/force-update")
async def force_incremental_update(
    strategy: str = Query(None, description="指定刷新策略: full_refresh, smart_refresh, incremental, minimal_update")
):
    """强制执行增量预测更新"""
    try:
        from core.incremental_predictor import get_incremental_predictor

        predictor = get_incremental_predictor()
        result = await predictor.force_update(strategy=strategy)

        return {
            "success": result.get('success', False),
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Force incremental update error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/prediction/incremental/history")
async def get_incremental_prediction_history(
    limit: int = Query(10, description="返回记录数限制", ge=1, le=50)
):
    """获取增量预测更新历史"""
    try:
        from core.incremental_predictor import get_incremental_predictor

        predictor = get_incremental_predictor()
        history = predictor.get_refresh_history(limit=limit)

        return {
            "success": True,
            "history": history,
            "count": len(history),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Get incremental prediction history error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/prediction/incremental/start-monitoring")
async def start_incremental_monitoring():
    """启动增量预测监控服务"""
    try:
        from core.incremental_predictor import \
            start_incremental_prediction_service

        predictor = await start_incremental_prediction_service()
        status = predictor.get_update_status()

        return {
            "success": True,
            "message": "增量预测监控服务已启动",
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Start incremental monitoring error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/prediction/incremental/stop-monitoring")
async def stop_incremental_monitoring():
    """停止增量预测监控服务"""
    try:
        from core.incremental_predictor import \
            stop_incremental_prediction_service

        await stop_incremental_prediction_service()

        return {
            "success": True,
            "message": "增量预测监控服务已停止",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Stop incremental monitoring error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# 自定义Swagger UI页面，使用本地CDN
@app.get("/docs", response_class=HTMLResponse)
async def custom_swagger_ui_html():
    """自定义Swagger UI页面，使用本地CDN资源"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>福彩3D数据分析API - Swagger UI</title>
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css" />
        <style>
            html {
                box-sizing: border-box;
                overflow: -moz-scrollbars-vertical;
                overflow-y: scroll;
            }
            *, *:before, *:after {
                box-sizing: inherit;
            }
            body {
                margin:0;
                background: #fafafa;
            }
        </style>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
        <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
        <script>
            window.onload = function() {
                const ui = SwaggerUIBundle({
                    url: '/openapi.json',
                    dom_id: '#swagger-ui',
                    deepLinking: true,
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIStandalonePreset
                    ],
                    plugins: [
                        SwaggerUIBundle.plugins.DownloadUrl
                    ],
                    layout: "StandaloneLayout"
                });
            };
        </script>
    </body>
    </html>
    """

# 🔍 Bug检测系统API端点
try:
    from src.api.bug_detection.monitoring import \
        router as bug_monitoring_router
    from src.api.bug_detection.reporting import router as bug_reporting_router

    # 添加Bug检测相关的API路由
    app.include_router(bug_monitoring_router)
    app.include_router(bug_reporting_router)

    logger.info("✅ Bug检测API端点已添加")
except ImportError:
    logger.warning("⚠️ Bug检测API端点未安装，跳过添加")
except Exception as e:
    logger.error(f"❌ Bug检测API端点添加失败: {e}")

# 🏥 健康检查API端点
try:
    from api.endpoints.health import router as health_router
    from api.endpoints.health import set_health_checker_components

    # 添加健康检查路由
    app.include_router(health_router)

    # 设置健康检查器的组件（如果可用）
    try:
        # 尝试设置数据库管理器
        db_manager = None
        if 'db_manager' in globals():
            db_manager = globals()['db_manager']

        # 尝试设置数据源管理器
        data_source_manager = None
        # 这里可以添加数据源管理器的引用

        # 尝试设置WebSocket管理器
        ws_manager = None
        if 'websocket_manager' in globals():
            ws_manager = globals()['websocket_manager']

        # 配置健康检查器
        set_health_checker_components(
            database_manager=db_manager,
            data_source_manager=data_source_manager,
            websocket_manager=ws_manager
        )

        logger.info("✅ 健康检查器组件配置完成")
    except Exception as e:
        logger.warning(f"⚠️ 健康检查器组件配置失败: {e}")

    logger.info("✅ 健康检查API端点已添加")
except ImportError as e:
    logger.warning(f"⚠️ 健康检查API端点导入失败: {e}")
except Exception as e:
    logger.error(f"❌ 健康检查API端点添加失败: {e}")

# ==================== WebSocket处理函数 ====================

async def handle_websocket_error_batch(message: dict, websocket: WebSocket):
    """处理WebSocket批量错误事件"""
    try:
        batch_data = message.get('data', [])
        timestamp = message.get('timestamp', time.time())

        logger.info(f"收到批量错误事件: {len(batch_data)} 个错误")

        # 处理每个错误事件
        processed_count = 0
        for error_data in batch_data:
            try:
                # 添加追踪ID
                if 'trace_id' not in error_data:
                    from src.bug_detection.monitoring.data_flow_tracer import \
                        start_error_trace
                    error_data['trace_id'] = start_error_trace(error_data)

                # 保存到数据库
                if bug_db_manager:
                    bug_id = bug_db_manager.save_bug_report(error_data)
                    if bug_id:
                        processed_count += 1
                        logger.debug(f"错误事件已保存: {bug_id}")

                        # 同时保存到实时事件表
                        realtime_event_data = {
                            'event_type': 'javascript_error',
                            'priority': 2,  # MEDIUM priority
                            'source': 'websocket_batch',
                            'timestamp': time.time(),
                            'data': error_data,
                            'tags': ['javascript', 'error', 'batch'],
                            'correlation_id': error_data.get('trace_id', bug_id)
                        }
                        bug_db_manager.save_realtime_event(realtime_event_data)

                # 发布到事件总线
                if event_bus:
                    from src.bug_detection.realtime.event_bus import (
                        EventPriority, EventType, publish_event)
                    await publish_event(
                        EventType.JAVASCRIPT_ERROR,
                        error_data,
                        EventPriority.MEDIUM,
                        "websocket_batch"
                    )

            except Exception as e:
                logger.error(f"处理单个错误事件失败: {e}")

        # 发送确认消息
        await websocket.send_json({
            'type': 'batch_processed',
            'processed_count': processed_count,
            'total_count': len(batch_data),
            'timestamp': time.time()
        })

        logger.info(f"批量错误事件处理完成: {processed_count}/{len(batch_data)}")

    except Exception as e:
        logger.error(f"处理批量错误事件失败: {e}")
        await websocket.send_json({
            'type': 'error',
            'message': f'批量错误处理失败: {str(e)}',
            'timestamp': time.time()
        })

async def handle_websocket_javascript_error(message: dict, websocket: WebSocket):
    """处理WebSocket单个JavaScript错误事件"""
    try:
        error_data = message.get('data', message)

        logger.info(f"收到JavaScript错误事件: {error_data.get('message', 'Unknown error')}")

        # 添加追踪ID
        if 'trace_id' not in error_data:
            from src.bug_detection.monitoring.data_flow_tracer import \
                start_error_trace
            error_data['trace_id'] = start_error_trace(error_data)

        # 保存到数据库
        bug_id = None
        if bug_db_manager:
            bug_id = bug_db_manager.save_bug_report(error_data)

            if bug_id:
                # 同时保存到实时事件表
                realtime_event_data = {
                    'event_type': 'javascript_error',
                    'priority': 1,  # HIGH priority for single errors
                    'source': 'websocket_single',
                    'timestamp': time.time(),
                    'data': error_data,
                    'tags': ['javascript', 'error', 'single'],
                    'correlation_id': error_data.get('trace_id', bug_id)
                }
                bug_db_manager.save_realtime_event(realtime_event_data)

        # 发布到事件总线
        if event_bus:
            from src.bug_detection.realtime.event_bus import (EventPriority,
                                                              EventType,
                                                              publish_event)
            await publish_event(
                EventType.JAVASCRIPT_ERROR,
                error_data,
                EventPriority.HIGH,
                "websocket_single"
            )

        # 发送确认消息
        await websocket.send_json({
            'type': 'error_processed',
            'bug_id': bug_id,
            'trace_id': error_data.get('trace_id'),
            'timestamp': time.time()
        })

        logger.info(f"JavaScript错误事件处理完成: {bug_id}")

    except Exception as e:
        logger.error(f"处理JavaScript错误事件失败: {e}")
        await websocket.send_json({
            'type': 'error',
            'message': f'JavaScript错误处理失败: {str(e)}',
            'timestamp': time.time()
        })

# ==================== WebSocket端点 ====================

@app.websocket("/ws/bug-detection")
async def websocket_bug_detection(websocket: WebSocket):
    """Bug检测WebSocket端点 - 增强版本"""
    session_id = None
    connection_start_time = time.time()
    message_count = 0
    last_activity = time.time()

    try:
        # 使用增强的WebSocket管理器
        if websocket_manager:
            session_id = await websocket_manager.connect(websocket)
            logger.info(f"WebSocket连接建立: {session_id}")
        else:
            # 回退到简单模式
            await websocket.accept()
            session_id = f"simple_{int(time.time())}"
            logger.info(f"WebSocket连接建立 (简单模式): {session_id}")

        # 发送连接成功消息
        await websocket.send_json({
            'type': 'connection_established',
            'session_id': session_id,
            'message': 'WebSocket连接已建立',
            'timestamp': time.time(),
            'features': ['heartbeat', 'error_handling', 'timeout_management']
        })

        try:
            while True:
                # 设置接收超时
                try:
                    data = await asyncio.wait_for(
                        websocket.receive_text(),
                        timeout=300.0  # 5分钟超时
                    )
                    message_count += 1
                    last_activity = time.time()

                    logger.debug(f"收到WebSocket消息 [{session_id}]: {data[:100]}...")

                    # 解析消息
                    try:
                        message = json.loads(data)
                        message_type = message.get('type', 'unknown')

                        # 处理不同类型的消息
                        if message_type == 'ping':
                            await websocket.send_json({
                                'type': 'pong',
                                'session_id': session_id,
                                'timestamp': time.time(),
                                'connection_duration': time.time() - connection_start_time,
                                'message_count': message_count
                            })
                        elif message_type == 'heartbeat':
                            # 更新心跳时间
                            if websocket_manager and session_id in websocket_manager.active_connections:
                                session = websocket_manager.active_connections[session_id]
                                session.last_pong = time.time()
                                session.missed_pings = 0

                            await websocket.send_json({
                                'type': 'heartbeat_ack',
                                'session_id': session_id,
                                'timestamp': time.time()
                            })
                        elif message_type == 'subscribe':
                            events = message.get('events', [])
                            # 使用WebSocket管理器处理订阅
                            if websocket_manager:
                                await websocket_manager.subscribe_to_topics(session_id, events)

                            await websocket.send_json({
                                'type': 'subscribed',
                                'session_id': session_id,
                                'events': events,
                                'timestamp': time.time()
                            })
                        elif message_type == 'error_batch':
                            # 处理批量错误事件
                            await handle_websocket_error_batch(message, websocket)
                        elif message_type == 'javascript_error':
                            # 处理单个JavaScript错误
                            await handle_websocket_javascript_error(message, websocket)
                        elif message_type == 'status_check':
                            # 状态检查
                            status = {
                                'session_id': session_id,
                                'connection_duration': time.time() - connection_start_time,
                                'message_count': message_count,
                                'last_activity': last_activity,
                                'websocket_manager_available': websocket_manager is not None
                            }

                            if websocket_manager and session_id in websocket_manager.active_connections:
                                session = websocket_manager.active_connections[session_id]
                                status.update({
                                    'ping_count': session.ping_count,
                                    'missed_pings': session.missed_pings,
                                    'state': session.state.value,
                                    'subscriptions': list(session.subscriptions)
                                })

                            await websocket.send_json({
                                'type': 'status_response',
                                'data': status,
                                'timestamp': time.time()
                            })
                        else:
                            await websocket.send_json({
                                'type': 'echo',
                                'session_id': session_id,
                                'data': message,
                                'timestamp': time.time()
                            })

                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析错误 [{session_id}]: {e}")
                        await websocket.send_json({
                            'type': 'error',
                            'session_id': session_id,
                            'message': 'Invalid JSON format',
                            'error_details': str(e),
                            'timestamp': time.time()
                        })
                    except Exception as e:
                        logger.error(f"消息处理错误 [{session_id}]: {e}")
                        await websocket.send_json({
                            'type': 'error',
                            'session_id': session_id,
                            'message': 'Message processing failed',
                            'error_details': str(e),
                            'timestamp': time.time()
                        })

                except asyncio.TimeoutError:
                    logger.warning(f"WebSocket接收超时 [{session_id}]")
                    await websocket.send_json({
                        'type': 'timeout_warning',
                        'session_id': session_id,
                        'message': 'No activity for 5 minutes',
                        'timestamp': time.time()
                    })
                    # 继续等待，不断开连接
                    continue

        except WebSocketDisconnect:
            logger.info(f"WebSocket客户端断开连接: {session_id}")
        except Exception as e:
            logger.error(f"WebSocket处理消息失败 [{session_id}]: {e}")
            try:
                await websocket.send_json({
                    'type': 'error',
                    'session_id': session_id,
                    'message': 'Connection error occurred',
                    'error_details': str(e),
                    'timestamp': time.time()
                })
            except:
                pass

    except Exception as e:
        logger.error(f"WebSocket连接失败: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass
    finally:
        # 清理连接
        if websocket_manager and session_id:
            try:
                await websocket_manager.disconnect(session_id)
            except Exception as e:
                logger.warning(f"清理WebSocket连接失败 [{session_id}]: {e}")

        connection_duration = time.time() - connection_start_time
        logger.info(f"WebSocket连接结束 [{session_id}]: 持续时间 {connection_duration:.2f}秒, 消息数 {message_count}")

@app.websocket("/ws/realtime-stats")
async def websocket_realtime_stats(websocket: WebSocket):
    """实时统计WebSocket端点 - 增强版本"""
    session_id = None
    connection_start_time = time.time()
    stats_sent = 0
    update_interval = 5  # 默认5秒更新间隔

    try:
        # 使用增强的WebSocket管理器
        if websocket_manager:
            session_id = await websocket_manager.connect(websocket, metadata={'type': 'stats'})
            logger.info(f"实时统计WebSocket连接建立: {session_id}")
        else:
            # 回退到简单模式
            await websocket.accept()
            session_id = f"stats_{int(time.time())}"
            logger.info(f"实时统计WebSocket连接建立 (简单模式): {session_id}")

        # 发送连接确认
        await websocket.send_json({
            'type': 'stats_connection_established',
            'session_id': session_id,
            'update_interval': update_interval,
            'timestamp': time.time()
        })

        # 创建统计发送任务和消息接收任务
        async def stats_sender():
            """统计数据发送任务"""
            nonlocal stats_sent
            while True:
                try:
                    # 获取实时统计数据
                    stats_data = {}

                    if bug_db_manager:
                        try:
                            stats_data['bug_stats'] = bug_db_manager.get_realtime_stats()
                        except Exception as e:
                            logger.warning(f"获取Bug统计失败: {e}")
                            stats_data['bug_stats'] = {'error': str(e)}

                    # 添加WebSocket管理器统计
                    if websocket_manager:
                        try:
                            stats_data['websocket_stats'] = websocket_manager.get_stats()
                        except Exception as e:
                            logger.warning(f"获取WebSocket统计失败: {e}")
                            stats_data['websocket_stats'] = {'error': str(e)}

                    # 添加连接统计
                    stats_data['connection_stats'] = {
                        'session_id': session_id,
                        'connection_duration': time.time() - connection_start_time,
                        'stats_sent': stats_sent,
                        'update_interval': update_interval
                    }

                    await websocket.send_json({
                        'type': 'stats_update',
                        'data': stats_data,
                        'timestamp': time.time()
                    })

                    stats_sent += 1
                    await asyncio.sleep(update_interval)

                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error(f"发送实时统计失败 [{session_id}]: {e}")
                    try:
                        await websocket.send_json({
                            'type': 'stats_error',
                            'session_id': session_id,
                            'error': str(e),
                            'timestamp': time.time()
                        })
                    except:
                        pass
                    await asyncio.sleep(update_interval)

        async def message_receiver():
            """消息接收任务"""
            nonlocal update_interval
            while True:
                try:
                    data = await asyncio.wait_for(
                        websocket.receive_text(),
                        timeout=60.0  # 1分钟超时
                    )

                    try:
                        message = json.loads(data)
                        message_type = message.get('type', 'unknown')

                        if message_type == 'ping':
                            await websocket.send_json({
                                'type': 'pong',
                                'session_id': session_id,
                                'timestamp': time.time()
                            })
                        elif message_type == 'set_interval':
                            # 设置更新间隔
                            new_interval = message.get('interval', 5)
                            if 1 <= new_interval <= 60:  # 限制在1-60秒之间
                                update_interval = new_interval
                                await websocket.send_json({
                                    'type': 'interval_updated',
                                    'session_id': session_id,
                                    'new_interval': update_interval,
                                    'timestamp': time.time()
                                })
                            else:
                                await websocket.send_json({
                                    'type': 'error',
                                    'session_id': session_id,
                                    'message': 'Invalid interval (must be 1-60 seconds)',
                                    'timestamp': time.time()
                                })
                        elif message_type == 'get_status':
                            # 获取连接状态
                            status = {
                                'session_id': session_id,
                                'connection_duration': time.time() - connection_start_time,
                                'stats_sent': stats_sent,
                                'update_interval': update_interval,
                                'websocket_manager_available': websocket_manager is not None,
                                'bug_db_manager_available': bug_db_manager is not None
                            }

                            await websocket.send_json({
                                'type': 'status_response',
                                'data': status,
                                'timestamp': time.time()
                            })

                    except json.JSONDecodeError:
                        await websocket.send_json({
                            'type': 'error',
                            'session_id': session_id,
                            'message': 'Invalid JSON format',
                            'timestamp': time.time()
                        })

                except asyncio.TimeoutError:
                    # 超时不是错误，继续等待
                    continue
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error(f"接收消息失败 [{session_id}]: {e}")
                    break

        # 并发运行发送和接收任务
        try:
            await asyncio.gather(
                stats_sender(),
                message_receiver(),
                return_exceptions=True
            )
        except Exception as e:
            logger.error(f"统计WebSocket任务失败 [{session_id}]: {e}")

    except Exception as e:
        logger.error(f"实时统计WebSocket连接失败: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass
    finally:
        # 清理连接
        if websocket_manager and session_id:
            try:
                await websocket_manager.disconnect(session_id)
            except Exception as e:
                logger.warning(f"清理统计WebSocket连接失败 [{session_id}]: {e}")

        connection_duration = time.time() - connection_start_time
        logger.info(f"实时统计WebSocket连接结束 [{session_id}]: 持续时间 {connection_duration:.2f}秒, 发送统计 {stats_sent} 次")

# ==================== Bug检测API端点 ====================

@app.post("/api/v1/bug-detection/js-error")
async def report_js_error(request: dict):
    """报告单个JavaScript错误"""
    try:
        session_id = request.get('session_id', 'unknown')
        page_name = request.get('page_name', 'unknown')

        # 发布到事件总线
        if event_bus:
            from src.bug_detection.realtime.event_bus import (EventPriority,
                                                              EventType,
                                                              publish_event)

            await publish_event(
                EventType.JAVASCRIPT_ERROR,
                {
                    'session_id': session_id,
                    'page_name': page_name,
                    **request
                },
                EventPriority.MEDIUM,
                "javascript_api"
            )

        # 保存到数据库
        if bug_db_manager:
            bug_db_manager.save_js_error(request)

        return {"status": "success", "message": "Error reported successfully"}

    except Exception as e:
        logger.error(f"JavaScript错误报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/bug-detection/js-error/batch")
async def report_js_errors_batch(request: dict):
    """批量报告JavaScript错误"""
    try:
        errors = request.get('errors', [])
        session_id = request.get('session_id', 'unknown')
        page_name = request.get('page_name', 'unknown')

        if not errors:
            raise HTTPException(status_code=400, detail="No errors provided")

        # 处理每个错误
        processed_count = 0
        for error in errors:
            try:
                # 发布到事件总线
                if event_bus:
                    from src.bug_detection.realtime.event_bus import (
                        EventPriority, EventType, publish_event)

                    await publish_event(
                        EventType.JAVASCRIPT_ERROR,
                        {
                            'session_id': session_id,
                            'page_name': page_name,
                            **error
                        },
                        EventPriority.MEDIUM,
                        "javascript_batch_api"
                    )
                    processed_count += 1

                # 保存到数据库
                if bug_db_manager:
                    # 添加追踪ID
                    if 'trace_id' not in error:
                        from src.bug_detection.monitoring.data_flow_tracer import \
                            start_error_trace
                        error['trace_id'] = start_error_trace(error)

                    # 保存到bug_reports表
                    bug_id = bug_db_manager.save_bug_report(error)

                    # 保存到realtime_events表
                    realtime_event_data = {
                        'id': error.get('id', f"batch_{int(time.time() * 1000)}"),
                        'event_type': 'javascript_error',
                        'priority': 2,
                        'source': 'javascript_batch_api',
                        'timestamp': error.get('timestamp', time.time()),
                        'data': error,
                        'tags': ['javascript', 'error', 'batch', 'api'],
                        'correlation_id': error.get('trace_id', bug_id)
                    }
                    bug_db_manager.save_realtime_event(realtime_event_data)

            except Exception as e:
                logger.error(f"处理批量错误失败: {e}")

        return {
            "status": "success",
            "processed_count": processed_count,
            "total_count": len(errors),
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error(f"批量错误报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 预测结果WebSocket端点 ====================

# 导入预测WebSocket管理器
try:
    from .prediction_pusher import get_prediction_pusher
    from .websocket_manager import get_prediction_websocket_manager
    PREDICTION_WEBSOCKET_AVAILABLE = True
except ImportError:
    try:
        import os
        import sys
        sys.path.append(os.path.dirname(__file__))
        from prediction_pusher import get_prediction_pusher
        from websocket_manager import get_prediction_websocket_manager
        PREDICTION_WEBSOCKET_AVAILABLE = True
    except ImportError as e:
        logger.warning(f"预测WebSocket模块导入失败: {e}")
        PREDICTION_WEBSOCKET_AVAILABLE = False

@app.websocket("/ws/prediction-results")
async def websocket_prediction_results(websocket: WebSocket):
    """预测结果WebSocket端点"""
    if not PREDICTION_WEBSOCKET_AVAILABLE:
        await websocket.close(code=1011, reason="Prediction WebSocket not available")
        return

    session_id = None
    connection_start_time = time.time()

    try:
        # 获取WebSocket管理器
        ws_manager = await get_prediction_websocket_manager()

        # 建立连接
        session_id = await ws_manager.connect(websocket)
        logger.info(f"预测结果WebSocket连接建立: {session_id}")

        # 自动订阅预测结果主题
        await ws_manager.subscribe_to_topic(session_id, 'prediction_results')
        await ws_manager.subscribe_to_topic(session_id, 'prediction_progress')

        # 发送订阅确认
        await websocket.send_json({
            'type': 'subscription_confirmed',
            'session_id': session_id,
            'subscribed_topics': ['prediction_results', 'prediction_progress'],
            'timestamp': time.time()
        })

        # 消息处理循环
        while True:
            try:
                data = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=300.0  # 5分钟超时
                )

                message = json.loads(data)
                message_type = message.get('type', 'unknown')

                if message_type == 'ping':
                    await websocket.send_json({
                        'type': 'pong',
                        'session_id': session_id,
                        'timestamp': time.time()
                    })
                elif message_type == 'subscribe':
                    topics = message.get('topics', [])
                    for topic in topics:
                        await ws_manager.subscribe_to_topic(session_id, topic)

                    await websocket.send_json({
                        'type': 'subscribed',
                        'session_id': session_id,
                        'topics': topics,
                        'timestamp': time.time()
                    })
                elif message_type == 'unsubscribe':
                    topics = message.get('topics', [])
                    for topic in topics:
                        await ws_manager.unsubscribe_from_topic(session_id, topic)

                    await websocket.send_json({
                        'type': 'unsubscribed',
                        'session_id': session_id,
                        'topics': topics,
                        'timestamp': time.time()
                    })
                elif message_type == 'get_stats':
                    stats = ws_manager.get_stats()
                    await websocket.send_json({
                        'type': 'stats_response',
                        'session_id': session_id,
                        'data': stats,
                        'timestamp': time.time()
                    })
                else:
                    await websocket.send_json({
                        'type': 'echo',
                        'session_id': session_id,
                        'data': message,
                        'timestamp': time.time()
                    })

            except asyncio.TimeoutError:
                # 发送心跳检查
                await websocket.send_json({
                    'type': 'heartbeat',
                    'session_id': session_id,
                    'timestamp': time.time()
                })
                continue
            except json.JSONDecodeError as e:
                await websocket.send_json({
                    'type': 'error',
                    'session_id': session_id,
                    'message': 'Invalid JSON format',
                    'error_details': str(e),
                    'timestamp': time.time()
                })
            except Exception as e:
                logger.error(f"处理预测WebSocket消息失败 [{session_id}]: {e}")
                await websocket.send_json({
                    'type': 'error',
                    'session_id': session_id,
                    'message': 'Message processing failed',
                    'error_details': str(e),
                    'timestamp': time.time()
                })

    except WebSocketDisconnect:
        logger.info(f"预测WebSocket客户端断开连接: {session_id}")
    except Exception as e:
        logger.error(f"预测WebSocket连接失败: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass
    finally:
        # 清理连接
        if PREDICTION_WEBSOCKET_AVAILABLE and session_id:
            try:
                ws_manager = await get_prediction_websocket_manager()
                await ws_manager.disconnect(session_id)
            except Exception as e:
                logger.warning(f"清理预测WebSocket连接失败 [{session_id}]: {e}")

        connection_duration = time.time() - connection_start_time
        logger.info(f"预测WebSocket连接结束 [{session_id}]: 持续时间 {connection_duration:.2f}秒")

# 注意：请使用项目根目录的 start_production_api.py 启动API服务
# 不要直接运行此文件
