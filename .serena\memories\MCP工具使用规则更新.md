# MCP工具使用规则更新 - Serena工具集成

## 更新概述
已成功将Serena MCP工具集成到项目的MCP使用规则文件 `.cursor\rules\mcprules.mdc` 中，确保Serena工具在各个开发阶段都能得到正确使用。

## Serena工具在各模式中的应用

### [模式：研究] - 需求分析阶段
- **新增**：使用`serena`工具进行项目激活、入门分析和符号概览
- **工具顺序**：knowledge-graph → codebase-retrieval → **serena** → context7-mcp → deepwiki-mcp → sequential-thinking
- **具体应用**：
  - `activate_project` - 激活项目获取上下文
  - `onboarding` - 自动分析项目结构和关键组件
  - `get_symbols_overview` - 获取代码符号概览

### [模式：构思] - 方案设计阶段
- **新增**：使用`serena`工具查找相关代码符号和引用关系
- **具体应用**：
  - `find_symbol` - 查找相关的类、函数、变量
  - `find_referencing_symbols` - 追踪代码依赖关系
  - `search_for_pattern` - 搜索相关代码模式

### [模式：计划] - 详细规划阶段
- **新增**：使用`serena`工具精确定位需要修改的代码符号和文件
- **具体应用**：
  - `find_symbol` - 精确定位需要修改的代码位置
  - `get_symbols_overview` - 分析影响范围
  - `find_referencing_symbols` - 评估修改影响

### [模式：执行] - 代码实现阶段
- **新增**：使用`serena`工具进行精确的代码编辑和符号替换
- **工具优先级**：serena → str-replace-editor → desktop-commander
- **具体应用**：
  - `replace_symbol_body` - 替换整个函数或类的实现
  - `insert_after_symbol` / `insert_before_symbol` - 精确插入代码
  - `create_text_file` - 创建新文件

### [模式：评审] - 质量检查阶段
- **新增**：使用`serena`工具验证代码符号的正确性和完整性
- **具体应用**：
  - `find_symbol` - 验证修改后的符号结构
  - `find_referencing_symbols` - 检查引用关系是否正确
  - `get_symbols_overview` - 确认整体结构完整性

## MCP服务优先级更新

**更新后的优先级排序**：
1. `mcp-feedback-enhanced` - 用户交互和确认
2. `sequential-thinking` - 复杂问题分析和深度思考
3. **`serena` - 项目管理和精确代码编辑** ⭐ 新增第3位
4. `context7-mcp` - 查询最新库文档和示例
5. `deepwiki-mcp` - 获取背景知识和领域概念
6. `mcp-shrimp-task-manager` - 拆分与管理任务依赖
7. `codebase-retrieval` - 分析现有代码结构
8. `desktop-commander` - 系统文件操作和命令执行
9. `Browser MCP` - 浏览器自动化操作

## 开发工作流程更新

### 新增的工作流程
- **项目管理**：使用`serena`工具进行项目激活、入门分析和项目记忆管理
- **代码检索**：使用`codebase-retrieval`和`serena`工具获取模板文件信息和符号分析
- **代码编辑**：优先使用`serena`工具进行精确的符号级编辑，辅助使用`str-replace-editor`工具

### 工具协同策略
- **Serena + Codebase Retrieval**：结合使用进行全面的代码分析
- **Serena + Sequential Thinking**：复杂重构时的深度分析
- **Serena + Context7**：技术方案验证时的文档查询
- **Serena + Task Manager**：大型项目的任务拆解和管理

## Serena工具详细使用指南

### 核心功能分类
#### 1. 项目管理功能
- `activate_project` - 激活项目获取上下文
- `onboarding` - 项目入门分析
- `get_active_project` - 查看当前激活项目

#### 2. 代码分析功能
- `find_symbol` - 全局符号搜索
- `get_symbols_overview` - 符号概览
- `find_referencing_symbols` - 引用关系分析
- `search_for_pattern` - 模式搜索

#### 3. 精确编辑功能
- `replace_symbol_body` - 替换符号定义
- `insert_after_symbol` - 在符号后插入
- `insert_before_symbol` - 在符号前插入

#### 4. 文件操作功能
- `create_text_file` - 创建文件
- `read_file` - 读取文件
- `list_dir` - 列出目录

#### 5. 项目记忆功能
- `write_memory` - 写入项目记忆
- `read_memory` - 读取项目记忆
- `list_memories` - 列出所有记忆

#### 6. 执行工具
- `execute_shell_command` - 执行命令
- `search_for_pattern` - 搜索模式

### 使用时机详解
#### 何时使用Serena而非其他工具
- **需要精确代码修改时** - 使用Serena而非str-replace-editor
- **需要理解代码结构时** - 使用Serena而非简单的文件读取
- **需要追踪代码关系时** - 使用Serena的引用分析功能
- **需要项目上下文时** - 使用Serena的项目记忆功能

#### Serena的独特优势
- **语义理解**：基于语言服务器的符号级代码理解
- **精确操作**：避免传统文本替换的错误
- **项目记忆**：学习和记住项目特定信息
- **多语言支持**：支持Python、TypeScript、JavaScript、PHP、Go、Rust、C#、Java等

## 实施效果

### 对开发流程的改进
1. **提高代码修改精度** - 减少因文本替换导致的错误
2. **增强项目理解能力** - 通过符号分析深入理解代码结构
3. **改善项目连续性** - 通过项目记忆保持开发上下文
4. **优化工具协同** - 与其他MCP工具形成完整的开发工具链

### 对福彩3D项目的特殊价值
- **项目复杂性管理** - 帮助理解复杂的预测算法和数据处理逻辑
- **代码质量提升** - 通过精确编辑减少引入bug的风险
- **开发效率提升** - 快速定位和修改相关代码
- **知识传承** - 通过项目记忆保存重要的开发决策和经验

## 更新完成确认

✅ **MCP规则文件更新完成**
✅ **Serena工具已集成到所有开发模式**
✅ **工具优先级已调整**
✅ **使用指南已完善**
✅ **工作流程已优化**

现在Augment环境中的所有AI助手都将按照更新后的规则正确使用Serena工具，确保在福彩3D预测系统的开发和维护中发挥最大价值！