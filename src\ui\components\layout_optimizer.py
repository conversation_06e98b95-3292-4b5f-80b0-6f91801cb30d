"""
布局优化组件
提供响应式布局、间距优化和视觉层次管理
"""

import streamlit as st
from typing import Dict, List, Optional, Tuple
import json


class LayoutOptimizer:
    """布局优化器"""
    
    def __init__(self):
        """初始化布局优化器"""
        self.grid_system = self._init_grid_system()
        self.spacing_system = self._init_spacing_system()
        self.breakpoints = self._init_breakpoints()
    
    def _init_grid_system(self) -> Dict[str, int]:
        """初始化网格系统"""
        return {
            "columns": 12,
            "gutter": 16,
            "margin": 24,
            "max_width": 1200
        }
    
    def _init_spacing_system(self) -> Dict[str, int]:
        """初始化间距系统"""
        return {
            "xs": 4,
            "sm": 8,
            "md": 16,
            "lg": 24,
            "xl": 32,
            "xxl": 48
        }
    
    def _init_breakpoints(self) -> Dict[str, int]:
        """初始化断点系统"""
        return {
            "mobile": 576,
            "tablet": 768,
            "desktop": 992,
            "large": 1200,
            "xlarge": 1400
        }
    
    def inject_layout_styles(self) -> None:
        """注入布局优化样式"""
        layout_css = f"""
        <style>
        /* 响应式网格系统 */
        .layout-container {{
            max-width: {self.grid_system['max_width']}px;
            margin: 0 auto;
            padding: 0 {self.grid_system['margin']}px;
        }}
        
        .layout-row {{
            display: flex;
            flex-wrap: wrap;
            margin: 0 -{self.grid_system['gutter']//2}px;
        }}
        
        .layout-col {{
            padding: 0 {self.grid_system['gutter']//2}px;
            flex: 1;
        }}
        
        /* 间距工具类 */
        .spacing-xs {{ margin: {self.spacing_system['xs']}px; }}
        .spacing-sm {{ margin: {self.spacing_system['sm']}px; }}
        .spacing-md {{ margin: {self.spacing_system['md']}px; }}
        .spacing-lg {{ margin: {self.spacing_system['lg']}px; }}
        .spacing-xl {{ margin: {self.spacing_system['xl']}px; }}
        .spacing-xxl {{ margin: {self.spacing_system['xxl']}px; }}
        
        .padding-xs {{ padding: {self.spacing_system['xs']}px; }}
        .padding-sm {{ padding: {self.spacing_system['sm']}px; }}
        .padding-md {{ padding: {self.spacing_system['md']}px; }}
        .padding-lg {{ padding: {self.spacing_system['lg']}px; }}
        .padding-xl {{ padding: {self.spacing_system['xl']}px; }}
        .padding-xxl {{ padding: {self.spacing_system['xxl']}px; }}
        
        /* 导航布局优化 */
        .nav-layout-optimized {{
            display: flex;
            flex-direction: column;
            gap: {self.spacing_system['md']}px;
            padding: {self.spacing_system['lg']}px;
            margin: {self.spacing_system['md']}px 0;
        }}
        
        .nav-section {{
            background: rgba(255,255,255,0.05);
            border-radius: 16px;
            padding: {self.spacing_system['lg']}px;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }}
        
        .nav-section-header {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: {self.spacing_system['md']}px;
            padding-bottom: {self.spacing_system['sm']}px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }}
        
        .nav-section-title {{
            font-size: 18px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: {self.spacing_system['sm']}px;
        }}
        
        .nav-section-badge {{
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }}
        
        .nav-items-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: {self.spacing_system['sm']}px;
        }}
        
        .nav-item-card {{
            background: rgba(255,255,255,0.08);
            border: 1px solid rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: {self.spacing_system['md']}px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }}
        
        .nav-item-card:hover {{
            background: rgba(255,255,255,0.15);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }}
        
        .nav-item-header {{
            display: flex;
            align-items: center;
            gap: {self.spacing_system['sm']}px;
            margin-bottom: {self.spacing_system['xs']}px;
        }}
        
        .nav-item-icon {{
            font-size: 20px;
            min-width: 24px;
        }}
        
        .nav-item-title {{
            font-size: 14px;
            font-weight: 600;
            color: white;
            flex: 1;
        }}
        
        .nav-item-description {{
            font-size: 12px;
            color: rgba(255,255,255,0.7);
            line-height: 1.4;
        }}
        
        /* 响应式断点 */
        @media (max-width: {self.breakpoints['mobile']}px) {{
            .layout-container {{
                padding: 0 {self.spacing_system['md']}px;
            }}
            
            .nav-layout-optimized {{
                padding: {self.spacing_system['md']}px;
                gap: {self.spacing_system['sm']}px;
            }}
            
            .nav-section {{
                padding: {self.spacing_system['md']}px;
            }}
            
            .nav-items-grid {{
                grid-template-columns: 1fr;
                gap: {self.spacing_system['xs']}px;
            }}
            
            .nav-item-card {{
                padding: {self.spacing_system['sm']}px;
            }}
        }}
        
        @media (max-width: {self.breakpoints['tablet']}px) {{
            .nav-items-grid {{
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }}
        }}
        
        @media (min-width: {self.breakpoints['desktop']}px) {{
            .nav-items-grid {{
                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            }}
        }}
        
        /* 视觉层次优化 */
        .visual-hierarchy-1 {{
            font-size: 24px;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: {self.spacing_system['lg']}px;
        }}
        
        .visual-hierarchy-2 {{
            font-size: 20px;
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: {self.spacing_system['md']}px;
        }}
        
        .visual-hierarchy-3 {{
            font-size: 16px;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: {self.spacing_system['sm']}px;
        }}
        
        .visual-hierarchy-4 {{
            font-size: 14px;
            font-weight: 500;
            line-height: 1.5;
            margin-bottom: {self.spacing_system['xs']}px;
        }}
        
        /* 内容对齐 */
        .align-left {{ text-align: left; }}
        .align-center {{ text-align: center; }}
        .align-right {{ text-align: right; }}
        .align-justify {{ text-align: justify; }}
        
        .flex-center {{
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        
        .flex-between {{
            display: flex;
            align-items: center;
            justify-content: space-between;
        }}
        
        .flex-start {{
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }}
        
        .flex-end {{
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }}
        
        /* 卡片布局 */
        .card-layout {{
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 16px;
            padding: {self.spacing_system['lg']}px;
            margin: {self.spacing_system['md']}px 0;
            backdrop-filter: blur(10px);
        }}
        
        .card-header {{
            margin-bottom: {self.spacing_system['md']}px;
            padding-bottom: {self.spacing_system['sm']}px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }}
        
        .card-body {{
            margin-bottom: {self.spacing_system['md']}px;
        }}
        
        .card-footer {{
            margin-top: {self.spacing_system['md']}px;
            padding-top: {self.spacing_system['sm']}px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }}
        </style>
        """
        
        st.markdown(layout_css, unsafe_allow_html=True)
    
    def create_responsive_navigation(self, categories: Dict[str, Dict[str, str]]) -> str:
        """创建响应式导航布局"""
        nav_html = '<div class="nav-layout-optimized">'
        
        for category_name, pages in categories.items():
            # 提取分类图标和名称
            parts = category_name.split(" ", 1)
            category_icon = parts[0] if len(parts) > 1 else "📁"
            category_title = parts[1] if len(parts) > 1 else category_name
            
            nav_html += f'''
            <div class="nav-section nav-fade-in">
                <div class="nav-section-header">
                    <div class="nav-section-title">
                        <span>{category_icon}</span>
                        <span>{category_title}</span>
                    </div>
                    <div class="nav-section-badge">{len(pages)}</div>
                </div>
                <div class="nav-items-grid">
            '''
            
            for page_name, page_func in pages.items():
                # 提取页面图标和名称
                page_parts = page_name.split(" ", 1)
                page_icon = page_parts[0] if len(page_parts) > 1 else "📄"
                page_title = page_parts[1] if len(page_parts) > 1 else page_name
                
                nav_html += f'''
                <div class="nav-item-card nav-ripple" onclick="selectPage('{page_name}')">
                    <div class="nav-item-header">
                        <span class="nav-item-icon">{page_icon}</span>
                        <span class="nav-item-title">{page_title}</span>
                    </div>
                    <div class="nav-item-description">
                        点击访问{page_title}功能
                    </div>
                </div>
                '''
            
            nav_html += '''
                </div>
            </div>
            '''
        
        nav_html += '</div>'
        
        # 添加JavaScript交互
        nav_html += '''
        <script>
        function selectPage(pageName) {
            // 更新Streamlit状态
            window.parent.postMessage({
                type: 'streamlit:setComponentValue',
                value: pageName
            }, '*');
        }
        </script>
        '''
        
        return nav_html
    
    def get_spacing_class(self, size: str) -> str:
        """获取间距类名"""
        return f"spacing-{size}"
    
    def get_padding_class(self, size: str) -> str:
        """获取内边距类名"""
        return f"padding-{size}"
