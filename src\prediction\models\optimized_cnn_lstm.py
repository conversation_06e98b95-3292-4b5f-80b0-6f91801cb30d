"""
优化的CNN-LSTM模型

基于原有CNN-LSTM模型进行优化，提升推理速度和内存效率。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional, Tuple
import numpy as np
import logging

logger = logging.getLogger(__name__)


class OptimizedCNNLSTM(nn.Module):
    """优化的CNN-LSTM模型"""
    
    def __init__(
        self,
        input_size: int = 3,
        hidden_size: int = 64,
        num_layers: int = 2,
        num_classes: int = 1000,
        dropout: float = 0.2,
        use_attention: bool = True,
        use_residual: bool = True,
        use_batch_norm: bool = True
    ):
        super(OptimizedCNNLSTM, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.num_classes = num_classes
        self.use_attention = use_attention
        self.use_residual = use_residual
        
        # 优化的CNN层 - 使用深度可分离卷积
        self.conv_layers = nn.ModuleList([
            # 第一层：标准卷积
            nn.Conv1d(input_size, hidden_size // 2, kernel_size=3, padding=1),
            nn.BatchNorm1d(hidden_size // 2) if use_batch_norm else nn.Identity(),
            nn.ReLU(inplace=True),
            
            # 第二层：深度可分离卷积
            nn.Conv1d(hidden_size // 2, hidden_size // 2, kernel_size=3, 
                     padding=1, groups=hidden_size // 2),  # 深度卷积
            nn.Conv1d(hidden_size // 2, hidden_size, kernel_size=1),  # 点卷积
            nn.BatchNorm1d(hidden_size) if use_batch_norm else nn.Identity(),
            nn.ReLU(inplace=True),
        ])
        
        # 优化的LSTM层 - 使用GRU替代LSTM以提升速度
        self.gru = nn.GRU(
            hidden_size, 
            hidden_size, 
            num_layers, 
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False  # 单向以提升速度
        )
        
        # 注意力机制（可选）
        if use_attention:
            self.attention = nn.MultiheadAttention(
                hidden_size, 
                num_heads=4,  # 减少头数以提升速度
                dropout=dropout,
                batch_first=True
            )
        
        # 输出层
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, num_classes)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        for module in self.modules():
            if isinstance(module, nn.Conv1d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight)
                nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, input_size]
            
        Returns:
            输出张量 [batch_size, num_classes]
        """
        batch_size, seq_len, _ = x.shape
        
        # CNN特征提取
        # 转换维度：[batch_size, seq_len, input_size] -> [batch_size, input_size, seq_len]
        x = x.transpose(1, 2)
        
        # 应用CNN层
        for i, layer in enumerate(self.conv_layers):
            if isinstance(layer, (nn.Conv1d, nn.BatchNorm1d, nn.ReLU, nn.Identity)):
                x = layer(x)
        
        # 转换回序列格式：[batch_size, hidden_size, seq_len] -> [batch_size, seq_len, hidden_size]
        x = x.transpose(1, 2)
        
        # 残差连接（如果启用）
        if self.use_residual and x.shape[-1] == self.hidden_size:
            residual = x
        else:
            residual = None
        
        # GRU处理
        gru_out, _ = self.gru(x)
        
        # 注意力机制（如果启用）
        if self.use_attention:
            attn_out, _ = self.attention(gru_out, gru_out, gru_out)
            x = attn_out
        else:
            x = gru_out
        
        # 残差连接
        if residual is not None and self.use_residual:
            x = x + residual
        
        # 取最后一个时间步的输出
        x = x[:, -1, :]
        
        # 全连接层
        x = self.dropout(x)
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        
        return x
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'model_name': 'OptimizedCNNLSTM',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'num_classes': self.num_classes,
            'use_attention': self.use_attention,
            'use_residual': self.use_residual,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # 假设float32
        }
    
    def optimize_for_inference(self):
        """优化模型以进行推理"""
        self.eval()
        
        # 融合BatchNorm层（如果存在）
        for module in self.modules():
            if hasattr(module, 'fuse_modules'):
                module.fuse_modules()
        
        # 设置为推理模式
        torch.jit.optimized_execution(True)
        
        logger.info("模型已优化用于推理")
    
    def get_flops(self, input_shape: Tuple[int, int, int]) -> int:
        """
        估算模型的FLOPs
        
        Args:
            input_shape: 输入形状 (batch_size, seq_len, input_size)
            
        Returns:
            FLOPs数量
        """
        batch_size, seq_len, input_size = input_shape
        flops = 0
        
        # CNN层FLOPs
        # 简化计算，实际应该更精确
        flops += seq_len * input_size * self.hidden_size * 3  # 第一个卷积层
        flops += seq_len * self.hidden_size * self.hidden_size * 3  # 第二个卷积层
        
        # GRU层FLOPs
        flops += seq_len * self.hidden_size * self.hidden_size * 3 * self.num_layers
        
        # 注意力层FLOPs（如果启用）
        if self.use_attention:
            flops += seq_len * seq_len * self.hidden_size * 4
        
        # 全连接层FLOPs
        flops += self.hidden_size * (self.hidden_size // 2)
        flops += (self.hidden_size // 2) * self.num_classes
        
        return flops * batch_size


def create_optimized_model(config: Dict[str, Any]) -> OptimizedCNNLSTM:
    """
    创建优化的CNN-LSTM模型
    
    Args:
        config: 模型配置
        
    Returns:
        优化的模型实例
    """
    model = OptimizedCNNLSTM(
        input_size=config.get('input_size', 3),
        hidden_size=config.get('hidden_size', 64),
        num_layers=config.get('num_layers', 2),
        num_classes=config.get('num_classes', 1000),
        dropout=config.get('dropout', 0.2),
        use_attention=config.get('use_attention', True),
        use_residual=config.get('use_residual', True),
        use_batch_norm=config.get('use_batch_norm', True)
    )
    
    logger.info(f"创建优化模型: {model.get_model_info()}")
    return model


if __name__ == "__main__":
    # 测试模型
    config = {
        'input_size': 3,
        'hidden_size': 64,
        'num_layers': 2,
        'num_classes': 1000,
        'dropout': 0.2
    }
    
    model = create_optimized_model(config)
    
    # 测试前向传播
    batch_size, seq_len = 32, 10
    x = torch.randn(batch_size, seq_len, config['input_size'])
    
    with torch.no_grad():
        output = model(x)
        print(f"输入形状: {x.shape}")
        print(f"输出形状: {output.shape}")
        print(f"模型信息: {model.get_model_info()}")
        print(f"FLOPs: {model.get_flops((batch_size, seq_len, config['input_size'])):,}")
