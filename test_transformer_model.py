#!/usr/bin/env python3
"""
测试Transformer模型集成
"""

def test_transformer_model():
    print("🧪 测试Transformer模型集成...")
    
    try:
        # 测试Transformer模型单独功能
        print("\n1. 测试Transformer模型导入...")
        from src.prediction.models.transformer_predictor import create_transformer_predictor
        print("✅ Transformer模型导入成功")
        
        # 创建模型
        print("\n2. 测试模型创建...")
        config = {
            'input_size': 3,
            'd_model': 64,
            'num_heads': 4,
            'num_layers': 2,
            'num_classes': 1000
        }
        model = create_transformer_predictor(config)
        model_info = model.get_model_info()
        print("✅ Transformer模型创建成功")
        print(f"   参数量: {model_info['total_parameters']:,}")
        print(f"   模型大小: {model_info['model_size_mb']:.2f} MB")
        
        # 测试前向传播
        print("\n3. 测试前向传播...")
        import torch
        test_input = torch.randn(2, 10, 3)
        with torch.no_grad():
            output = model(test_input)
        print(f"✅ 前向传播测试成功: {test_input.shape} -> {output.shape}")
        
        # 测试集成到IntelligentFusionSystem
        print("\n4. 测试集成到融合系统...")
        from src.prediction.intelligent_fusion import IntelligentFusionSystem
        
        fusion = IntelligentFusionSystem()
        print("✅ 融合系统初始化成功")
        print(f"   优化模型状态: {fusion.optimized_models_ready}")
        print(f"   可用模型: {list(fusion.optimized_models.keys())}")
        
        if 'transformer' in fusion.optimized_models:
            print("✅ Transformer模型已集成到融合系统")
            
            # 测试Transformer预测
            test_data = ['123', '456', '789', '012', '345']
            result = fusion.generate_optimized_predictions(test_data)
            
            if 'error' not in result:
                print("✅ 优化模型预测成功")
                print(f"   使用的模型: {result.get('model_types', [])}")
                print(f"   候选数量: {len(result.get('candidates', []))}")
                
                # 检查是否包含Transformer结果
                individual_results = result.get('individual_results', {})
                if 'transformer' in individual_results:
                    print("✅ Transformer模型预测结果已包含")
                    transformer_result = individual_results['transformer']
                    print(f"   Transformer候选: {transformer_result.get('candidates', [])[:3]}")
                else:
                    print("⚠️ Transformer模型预测结果未找到")
            else:
                print(f"❌ 预测失败: {result['error']}")
        else:
            print("⚠️ Transformer模型未在融合系统中找到")
        
        print("\n🎉 Transformer模型集成测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_transformer_model()
