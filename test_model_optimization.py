#!/usr/bin/env python3
"""
深度学习模型优化测试
"""

import numpy as np
import torch

from src.prediction.models.inference_engine import InferenceEngine
from src.prediction.models.optimized_cnn_lstm import create_optimized_model
from src.prediction.models.performance_profiler import PerformanceProfiler


def test_model_optimization():
    print("🚀 深度学习模型优化测试")
    
    # 创建优化模型
    config = {
        'input_size': 3,
        'hidden_size': 32,
        'num_classes': 1000,
        'dropout': 0.2
    }
    
    print("\n1. 创建优化模型...")
    model = create_optimized_model(config)
    model_info = model.get_model_info()
    print(f"✅ 模型创建成功: {model_info['model_name']}")
    print(f"✅ 模型参数量: {model_info['total_parameters']:,}")
    print(f"✅ 模型大小: {model_info['model_size_mb']:.2f} MB")
    
    # 测试推理引擎
    print("\n2. 测试推理引擎...")
    engine = InferenceEngine(model, device='cpu')
    test_input = torch.randn(5, 10, 3)
    result = engine.predict_batch(test_input)
    print(f"✅ 批量推理成功: 输入 {test_input.shape} -> 输出 {result.shape}")

    # 测试性能分析
    print("\n3. 测试性能分析...")
    profiler = PerformanceProfiler(model, device='cpu')
    perf_results = profiler.profile_inference(test_input, num_runs=10, use_profiler=False)
    print(f"✅ 性能分析完成:")
    print(f"   平均推理时间: {perf_results['inference_time']['mean']:.4f}s")
    print(f"   吞吐量: {perf_results['throughput']['samples_per_sec']:.2f} samples/s")
    
    print("\n🎉 深度学习模型优化测试全部通过！")
    
    return {
        'model_info': model_info,
        'performance_stats': perf_results
    }

if __name__ == "__main__":
    test_model_optimization()
