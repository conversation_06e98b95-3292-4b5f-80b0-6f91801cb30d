"""
基础异常类

定义系统的异常基类和错误代码体系。
"""

import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = "E1000"
    INVALID_PARAMETER = "E1001"
    MISSING_PARAMETER = "E1002"
    PERMISSION_DENIED = "E1003"
    RESOURCE_NOT_FOUND = "E1004"
    OPERATION_TIMEOUT = "E1005"
    
    # 数据相关错误 (2000-2999)
    DATA_NOT_FOUND = "E2000"
    DATA_VALIDATION_ERROR = "E2001"
    DATA_FORMAT_ERROR = "E2002"
    DATA_CORRUPTION = "E2003"
    DATA_DUPLICATE = "E2004"
    
    # 预测相关错误 (3000-3999)
    PREDICTION_FAILED = "E3000"
    MODEL_NOT_READY = "E3001"
    INSUFFICIENT_DATA = "E3002"
    PREDICTION_TIMEOUT = "E3003"
    MODEL_LOAD_ERROR = "E3004"
    
    # 数据库相关错误 (4000-4999)
    DATABASE_CONNECTION_ERROR = "E4000"
    DATABASE_QUERY_ERROR = "E4001"
    DATABASE_TRANSACTION_ERROR = "E4002"
    DATABASE_INTEGRITY_ERROR = "E4003"
    
    # 网络相关错误 (5000-5999)
    NETWORK_CONNECTION_ERROR = "E5000"
    NETWORK_TIMEOUT = "E5001"
    API_RATE_LIMIT = "E5002"
    EXTERNAL_SERVICE_ERROR = "E5003"
    
    # 文件系统错误 (6000-6999)
    FILE_NOT_FOUND = "E6000"
    FILE_PERMISSION_ERROR = "E6001"
    DISK_SPACE_ERROR = "E6002"
    FILE_CORRUPTION = "E6003"
    
    # 缓存相关错误 (7000-7999)
    CACHE_CONNECTION_ERROR = "E7000"
    CACHE_OPERATION_ERROR = "E7001"
    CACHE_SERIALIZATION_ERROR = "E7002"
    
    # 配置相关错误 (8000-8999)
    CONFIG_LOAD_ERROR = "E8000"
    CONFIG_VALIDATION_ERROR = "E8001"
    CONFIG_MISSING_ERROR = "E8002"


class LotterySystemException(Exception):
    """系统基础异常类"""
    
    def __init__(
        self,
        message: str,
        code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        初始化异常
        
        Args:
            message: 错误消息
            code: 错误代码
            severity: 错误严重程度
            details: 错误详细信息
            cause: 原始异常
        """
        self.message = message
        self.code = code
        self.severity = severity
        self.details = details or {}
        self.cause = cause
        self.timestamp = datetime.now()
        self.traceback_info = traceback.format_exc() if cause else None
        
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error": {
                "code": self.code.value,
                "message": self.message,
                "severity": self.severity.value,
                "timestamp": self.timestamp.isoformat(),
                "details": self.details,
                "traceback": self.traceback_info
            }
        }
    
    def to_user_message(self) -> str:
        """获取用户友好的错误消息"""
        user_messages = {
            ErrorCode.UNKNOWN_ERROR: "系统发生未知错误，请稍后重试",
            ErrorCode.INVALID_PARAMETER: "输入参数无效，请检查后重试",
            ErrorCode.MISSING_PARAMETER: "缺少必要参数，请补充完整信息",
            ErrorCode.PERMISSION_DENIED: "权限不足，无法执行此操作",
            ErrorCode.RESOURCE_NOT_FOUND: "请求的资源不存在",
            ErrorCode.OPERATION_TIMEOUT: "操作超时，请稍后重试",
            
            ErrorCode.DATA_NOT_FOUND: "未找到相关数据",
            ErrorCode.DATA_VALIDATION_ERROR: "数据验证失败，请检查输入",
            ErrorCode.DATA_FORMAT_ERROR: "数据格式错误",
            ErrorCode.DATA_CORRUPTION: "数据已损坏，请联系管理员",
            ErrorCode.DATA_DUPLICATE: "数据重复，请检查后重试",
            
            ErrorCode.PREDICTION_FAILED: "预测失败，请稍后重试",
            ErrorCode.MODEL_NOT_READY: "模型尚未准备就绪，请稍后重试",
            ErrorCode.INSUFFICIENT_DATA: "数据不足，无法进行预测",
            ErrorCode.PREDICTION_TIMEOUT: "预测超时，请稍后重试",
            ErrorCode.MODEL_LOAD_ERROR: "模型加载失败，请联系管理员",
            
            ErrorCode.DATABASE_CONNECTION_ERROR: "数据库连接失败，请稍后重试",
            ErrorCode.DATABASE_QUERY_ERROR: "数据查询失败，请稍后重试",
            ErrorCode.DATABASE_TRANSACTION_ERROR: "数据库事务失败，请稍后重试",
            ErrorCode.DATABASE_INTEGRITY_ERROR: "数据完整性错误，请联系管理员",
            
            ErrorCode.NETWORK_CONNECTION_ERROR: "网络连接失败，请检查网络后重试",
            ErrorCode.NETWORK_TIMEOUT: "网络超时，请稍后重试",
            ErrorCode.API_RATE_LIMIT: "请求过于频繁，请稍后重试",
            ErrorCode.EXTERNAL_SERVICE_ERROR: "外部服务异常，请稍后重试",
            
            ErrorCode.FILE_NOT_FOUND: "文件不存在",
            ErrorCode.FILE_PERMISSION_ERROR: "文件权限不足",
            ErrorCode.DISK_SPACE_ERROR: "磁盘空间不足",
            ErrorCode.FILE_CORRUPTION: "文件已损坏",
            
            ErrorCode.CACHE_CONNECTION_ERROR: "缓存连接失败",
            ErrorCode.CACHE_OPERATION_ERROR: "缓存操作失败",
            ErrorCode.CACHE_SERIALIZATION_ERROR: "缓存序列化失败",
            
            ErrorCode.CONFIG_LOAD_ERROR: "配置加载失败",
            ErrorCode.CONFIG_VALIDATION_ERROR: "配置验证失败",
            ErrorCode.CONFIG_MISSING_ERROR: "配置缺失",
        }
        
        return user_messages.get(self.code, self.message)
    
    def is_critical(self) -> bool:
        """是否为严重错误"""
        return self.severity == ErrorSeverity.CRITICAL
    
    def is_user_error(self) -> bool:
        """是否为用户错误"""
        user_error_codes = [
            ErrorCode.INVALID_PARAMETER,
            ErrorCode.MISSING_PARAMETER,
            ErrorCode.DATA_VALIDATION_ERROR,
            ErrorCode.DATA_FORMAT_ERROR,
        ]
        return self.code in user_error_codes
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"[{self.code.value}] {self.message}"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"{self.__class__.__name__}("
            f"code={self.code.value}, "
            f"message='{self.message}', "
            f"severity={self.severity.value})"
        )
