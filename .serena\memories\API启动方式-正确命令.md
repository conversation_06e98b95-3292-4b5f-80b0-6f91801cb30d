# API启动方式 - 关键信息

## ✅ 正确启动命令
```bash
python start_production_api.py
```

## ❌ 禁止使用的错误命令
```bash
python src/api/production_main.py  # 此命令无法启动服务
```

## 🔍 技术原理
- `start_production_api.py`: 专门的启动器，包含uvicorn服务器配置
- `src/api/production_main.py`: 只定义FastAPI应用对象，无启动代码
- 文件末尾明确注释："不要直接运行此文件"

## 📍 重要注意事项
1. 必须在项目根目录执行
2. 确保8888端口未被占用
3. 建议在虚拟环境中运行
4. 服务绑定地址：127.0.0.1:8888

## ✅ 验证方法
```bash
curl http://127.0.0.1:8888/health
```
预期返回：包含database_records和date_range的JSON健康状态

## 🔗 访问地址
- API文档: http://127.0.0.1:8888/docs
- 健康检查: http://127.0.0.1:8888/health
- WebSocket: ws://127.0.0.1:8888/ws/