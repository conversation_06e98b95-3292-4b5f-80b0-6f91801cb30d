@echo off
REM serena MCP服务器一键启动脚本
REM 确保serena MCP服务器正常启动并可通过Augment访问

echo 🚀 启动serena MCP服务器...
echo ================================================

REM 设置项目根目录
set PROJECT_ROOT=%~dp0..
cd /d "%PROJECT_ROOT%"

echo 📁 项目目录: %PROJECT_ROOT%
echo.

REM 检查虚拟环境
if not exist "venv\Scripts\serena-mcp-server.exe" (
    echo ❌ serena-mcp-server.exe 不存在
    echo 请先安装serena: pip install serena
    pause
    exit /b 1
)

echo ✅ 找到serena-mcp-server.exe
echo.

REM 终止现有进程
echo 🔄 终止现有serena进程...
taskkill /F /IM serena-mcp-server.exe >nul 2>&1
timeout /t 2 >nul

REM 启动serena MCP服务器
echo 🚀 启动serena MCP服务器...
echo 命令: venv\Scripts\serena-mcp-server.exe --project "%PROJECT_ROOT%" --enable-web-dashboard true --host 127.0.0.1 --port 24282
echo.

start "serena MCP Server" cmd /k "venv\Scripts\serena-mcp-server.exe --project "%PROJECT_ROOT%" --enable-web-dashboard true --host 127.0.0.1 --port 24282"

REM 等待服务器启动
echo ⏳ 等待服务器启动...
timeout /t 5 >nul

REM 检查服务器状态
echo 🔍 检查服务器状态...
python scripts\serena_health_check.py --quick

echo.
echo 📋 下一步操作:
echo 1. 重启Cursor IDE
echo 2. 在Augment中检查serena MCP连接状态
echo 3. 访问仪表板: http://127.0.0.1:24282/dashboard/index.html
echo.

REM 询问是否打开仪表板
set /p OPEN_DASHBOARD=是否打开serena仪表板? (y/n): 
if /i "%OPEN_DASHBOARD%"=="y" (
    start http://127.0.0.1:24282/dashboard/index.html
)

echo.
echo ✅ serena MCP服务器启动完成!
pause
