#!/usr/bin/env python3
"""
键盘快捷键组件
创建日期: 2025年7月31日
用途: 提供键盘快捷键支持，提升操作效率和用户体验
"""

import json
import uuid
from typing import Dict, List, Optional, Callable, Any
import streamlit as st
import streamlit.components.v1 as components


class KeyboardShortcuts:
    """键盘快捷键管理器"""
    
    def __init__(self):
        self.shortcuts_id = str(uuid.uuid4())
        self.shortcuts = {}
        self._init_session_state()
        self._init_default_shortcuts()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'keyboard_shortcuts' not in st.session_state:
            st.session_state.keyboard_shortcuts = {
                'enabled': True,
                'shortcuts': {},
                'help_visible': False,
                'last_pressed': None
            }
    
    def _init_default_shortcuts(self):
        """初始化默认快捷键"""
        self.shortcuts = {
            'Ctrl+H': {
                'description': '显示/隐藏快捷键帮助',
                'action': 'toggle_help',
                'category': '通用'
            },
            'Ctrl+R': {
                'description': '刷新页面数据',
                'action': 'refresh_data',
                'category': '数据'
            },
            'Ctrl+P': {
                'description': '开始预测分析',
                'action': 'start_prediction',
                'category': '预测'
            },
            'Ctrl+D': {
                'description': '切换到数据概览',
                'action': 'goto_overview',
                'category': '导航'
            },
            'Ctrl+F': {
                'description': '切换到频率分析',
                'action': 'goto_frequency',
                'category': '导航'
            },
            'Ctrl+S': {
                'description': '切换到和值分布',
                'action': 'goto_sum_distribution',
                'category': '导航'
            },
            'Ctrl+Q': {
                'description': '切换到数据查询',
                'action': 'goto_query',
                'category': '导航'
            },
            'Ctrl+M': {
                'description': '切换到数据管理',
                'action': 'goto_management',
                'category': '导航'
            },
            'Escape': {
                'description': '关闭当前对话框',
                'action': 'close_dialog',
                'category': '通用'
            },
            'F1': {
                'description': '显示帮助文档',
                'action': 'show_help',
                'category': '帮助'
            },
            'F5': {
                'description': '强制刷新页面',
                'action': 'force_refresh',
                'category': '通用'
            }
        }
    
    def inject_shortcuts(self):
        """注入键盘快捷键监听器"""
        shortcuts_js = f"""
        <script>
        // 键盘快捷键管理器
        (function() {{
            const shortcuts = {json.dumps(self.shortcuts)};
            let helpVisible = false;
            
            // 快捷键处理函数
            function handleShortcut(action) {{
                console.log('🔥 快捷键触发:', action);
                
                switch(action) {{
                    case 'toggle_help':
                        toggleHelp();
                        break;
                    case 'refresh_data':
                        refreshData();
                        break;
                    case 'start_prediction':
                        startPrediction();
                        break;
                    case 'goto_overview':
                        navigateToPage('overview');
                        break;
                    case 'goto_frequency':
                        navigateToPage('frequency');
                        break;
                    case 'goto_sum_distribution':
                        navigateToPage('sum_distribution');
                        break;
                    case 'goto_query':
                        navigateToPage('query');
                        break;
                    case 'goto_management':
                        navigateToPage('management');
                        break;
                    case 'close_dialog':
                        closeDialog();
                        break;
                    case 'show_help':
                        showHelp();
                        break;
                    case 'force_refresh':
                        location.reload();
                        break;
                    default:
                        console.warn('未知的快捷键动作:', action);
                }}
            }}
            
            // 切换帮助显示
            function toggleHelp() {{
                helpVisible = !helpVisible;
                const helpDialog = document.getElementById('shortcuts-help-dialog');
                if (helpDialog) {{
                    helpDialog.style.display = helpVisible ? 'flex' : 'none';
                }} else if (helpVisible) {{
                    showHelp();
                }}
            }}
            
            // 显示帮助对话框
            function showHelp() {{
                if (document.getElementById('shortcuts-help-dialog')) return;
                
                const helpDialog = document.createElement('div');
                helpDialog.id = 'shortcuts-help-dialog';
                helpDialog.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                `;
                
                const helpContent = document.createElement('div');
                helpContent.style.cssText = `
                    background: white;
                    border-radius: 8px;
                    padding: 20px;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                `;
                
                let helpHTML = '<h2>🔥 键盘快捷键</h2>';
                const categories = {{}};
                
                // 按类别分组快捷键
                Object.entries(shortcuts).forEach(([key, info]) => {{
                    if (!categories[info.category]) {{
                        categories[info.category] = [];
                    }}
                    categories[info.category].push({{key, ...info}});
                }});
                
                // 生成帮助内容
                Object.entries(categories).forEach(([category, items]) => {{
                    helpHTML += `<h3>${{category}}</h3><ul>`;
                    items.forEach(item => {{
                        helpHTML += `<li><kbd>${{item.key}}</kbd> - ${{item.description}}</li>`;
                    }});
                    helpHTML += '</ul>';
                }});
                
                helpHTML += '<p style="text-align: center; margin-top: 20px;"><button onclick="document.getElementById(\\'shortcuts-help-dialog\\').remove(); helpVisible = false;">关闭 (Esc)</button></p>';
                
                helpContent.innerHTML = helpHTML;
                helpDialog.appendChild(helpContent);
                document.body.appendChild(helpDialog);
                
                helpVisible = true;
                
                // 点击外部关闭
                helpDialog.addEventListener('click', (e) => {{
                    if (e.target === helpDialog) {{
                        helpDialog.remove();
                        helpVisible = false;
                    }}
                }});
            }}
            
            // 刷新数据
            function refreshData() {{
                const refreshButton = document.querySelector('button[data-testid="refresh-button"]') || 
                                    document.querySelector('button:contains("刷新")') ||
                                    document.querySelector('button:contains("🔄")');
                if (refreshButton) {{
                    refreshButton.click();
                }} else {{
                    // 触发Streamlit重新运行
                    window.parent.postMessage({{
                        type: 'streamlit:rerun'
                    }}, '*');
                }}
            }}
            
            // 开始预测
            function startPrediction() {{
                const predictionButton = document.querySelector('button:contains("开始预测")') ||
                                       document.querySelector('button:contains("🎯")') ||
                                       document.querySelector('button[data-testid="prediction-button"]');
                if (predictionButton) {{
                    predictionButton.click();
                }}
            }}
            
            // 页面导航
            function navigateToPage(page) {{
                const pageMap = {{
                    'overview': '数据概览',
                    'frequency': '频率分析',
                    'sum_distribution': '和值分布',
                    'query': '数据查询',
                    'management': '数据管理'
                }};
                
                const pageName = pageMap[page];
                if (pageName) {{
                    const navLink = document.querySelector(`a:contains("${{pageName}}")`) ||
                                  document.querySelector(`button:contains("${{pageName}}")`);
                    if (navLink) {{
                        navLink.click();
                    }}
                }}
            }}
            
            // 关闭对话框
            function closeDialog() {{
                const dialogs = document.querySelectorAll('[role="dialog"], .modal, #shortcuts-help-dialog');
                dialogs.forEach(dialog => {{
                    if (dialog.style.display !== 'none') {{
                        dialog.style.display = 'none';
                        if (dialog.id === 'shortcuts-help-dialog') {{
                            helpVisible = false;
                        }}
                    }}
                }});
            }}
            
            // 键盘事件监听
            document.addEventListener('keydown', function(e) {{
                // 构建快捷键字符串
                let shortcut = '';
                if (e.ctrlKey) shortcut += 'Ctrl+';
                if (e.altKey) shortcut += 'Alt+';
                if (e.shiftKey) shortcut += 'Shift+';
                
                // 特殊键处理
                if (e.key === 'Escape') {{
                    shortcut = 'Escape';
                }} else if (e.key.startsWith('F') && e.key.length <= 3) {{
                    shortcut += e.key;
                }} else if (e.key.length === 1) {{
                    shortcut += e.key.toUpperCase();
                }} else {{
                    return; // 忽略其他键
                }}
                
                // 检查是否是已定义的快捷键
                if (shortcuts[shortcut]) {{
                    e.preventDefault();
                    e.stopPropagation();
                    handleShortcut(shortcuts[shortcut].action);
                    
                    // 记录快捷键使用
                    console.log('🔥 快捷键使用:', shortcut, shortcuts[shortcut].description);
                }}
            }});
            
            // 添加快捷键样式
            const style = document.createElement('style');
            style.textContent = `
                kbd {{
                    background-color: #f7f7f7;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    box-shadow: 0 1px 0 rgba(0,0,0,0.2), 0 0 0 2px #fff inset;
                    color: #333;
                    display: inline-block;
                    font-family: Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    margin: 0 .1em;
                    padding: .1em .6em;
                    text-shadow: 0 1px 0 #fff;
                }}
                
                #shortcuts-help-dialog h2 {{
                    margin-top: 0;
                    color: #333;
                    text-align: center;
                }}
                
                #shortcuts-help-dialog h3 {{
                    color: #666;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 5px;
                }}
                
                #shortcuts-help-dialog ul {{
                    list-style: none;
                    padding: 0;
                }}
                
                #shortcuts-help-dialog li {{
                    padding: 5px 0;
                    display: flex;
                    align-items: center;
                }}
                
                #shortcuts-help-dialog li kbd {{
                    margin-right: 10px;
                    min-width: 80px;
                }}
            `;
            document.head.appendChild(style);
            
            console.log('🔥 键盘快捷键已初始化，按 Ctrl+H 查看帮助');
        }})();
        </script>
        """
        
        components.html(shortcuts_js, height=0)
    
    def add_shortcut(self, key: str, description: str, action: str, category: str = '自定义'):
        """添加自定义快捷键
        
        Args:
            key: 快捷键组合 (如 'Ctrl+K')
            description: 描述
            action: 动作标识
            category: 分类
        """
        self.shortcuts[key] = {
            'description': description,
            'action': action,
            'category': category
        }
    
    def remove_shortcut(self, key: str):
        """移除快捷键"""
        if key in self.shortcuts:
            del self.shortcuts[key]
    
    def get_shortcuts_by_category(self) -> Dict[str, List[Dict]]:
        """按类别获取快捷键"""
        categories = {}
        for key, info in self.shortcuts.items():
            category = info['category']
            if category not in categories:
                categories[category] = []
            categories[category].append({'key': key, **info})
        return categories
    
    def show_shortcuts_help(self):
        """显示快捷键帮助"""
        st.subheader("🔥 键盘快捷键")
        
        categories = self.get_shortcuts_by_category()
        
        for category, shortcuts in categories.items():
            st.markdown(f"### {category}")
            
            for shortcut in shortcuts:
                col1, col2 = st.columns([1, 3])
                with col1:
                    st.code(shortcut['key'])
                with col2:
                    st.write(shortcut['description'])
        
        st.info("💡 提示：按 Ctrl+H 可以随时显示/隐藏快捷键帮助")
    
    def create_shortcut_indicator(self):
        """创建快捷键指示器"""
        indicator_html = """
        <div id="shortcuts-indicator" style="
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
            cursor: pointer;
        " onclick="document.dispatchEvent(new KeyboardEvent('keydown', {key: 'H', ctrlKey: true}));">
            🔥 Ctrl+H
        </div>
        
        <style>
        #shortcuts-indicator:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.05);
            transition: all 0.2s ease;
        }
        </style>
        """
        components.html(indicator_html, height=0)


# 便捷函数
def inject_keyboard_shortcuts():
    """注入键盘快捷键支持"""
    shortcuts = KeyboardShortcuts()
    shortcuts.inject_shortcuts()
    return shortcuts

def show_shortcuts_help():
    """显示快捷键帮助"""
    shortcuts = KeyboardShortcuts()
    shortcuts.show_shortcuts_help()

def create_shortcuts_indicator():
    """创建快捷键指示器"""
    shortcuts = KeyboardShortcuts()
    shortcuts.create_shortcut_indicator()

# 全局快捷键管理器实例
global_shortcuts_manager = KeyboardShortcuts()
