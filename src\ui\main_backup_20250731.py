#!/usr/bin/env python3
"""
Streamlit主应用 - 智能融合优化版

现代化的福彩3D预测分析工具用户界面
集成智能融合优化功能：短期趋势捕捉、形态转换预测、自适应权重融合
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import date, datetime
from typing import Any, Dict, List

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import streamlit as st

# WebSocket相关导入
try:
    import websockets
    HAS_WEBSOCKETS = True
except ImportError:
    HAS_WEBSOCKETS = False

# 设置日志
logger = logging.getLogger(__name__)

# 导入新的UI组件
from ui.components.navigation import NavigationComponent
from ui.components.page_manager import PageManager
from ui.components.user_preferences import UserPreferenceManager


def check_websocket_connection():
    """检查WebSocket连接状态"""
    if not HAS_WEBSOCKETS:
        return False

    try:
        import asyncio

        async def test_connection():
            try:
                uri = "ws://127.0.0.1:8888/ws/bug-detection"
                async with websockets.connect(uri, timeout=2) as websocket:
                    return True
            except:
                return False

        # 在Streamlit中运行异步检查
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(test_connection())
        except:
            return asyncio.run(test_connection())
    except Exception as e:
        logger.warning(f"WebSocket连接检查失败: {e}")
        return False


def render_page_header_with_favorite(page_title: str, page_name: str):
    """
    渲染带收藏功能的页面标题

    Args:
        page_title: 页面显示标题
        page_name: 页面名称（用于收藏）
    """
    if 'pref_manager' in st.session_state:
        col1, col2 = st.columns([10, 1])
        with col1:
            st.title(page_title)
        with col2:
            # 收藏按钮
            is_favorite = st.session_state.pref_manager.is_favorite(page_name)
            fav_icon = "⭐" if is_favorite else "☆"
            fav_help = "取消收藏" if is_favorite else "添加到收藏"

            if st.button(
                fav_icon,
                key=f"fav_header_{page_name}",
                help=fav_help
            ):
                st.session_state.pref_manager.toggle_favorite(page_name)
                st.rerun()
    else:
        st.title(page_title)


# DataFrame安全操作工具函数
def safe_apply_column(df: pd.DataFrame, column_name: str, apply_func, default_value: str = "N/A") -> pd.Series:
    """
    安全地对DataFrame列应用函数

    Args:
        df: DataFrame对象
        column_name: 列名
        apply_func: 要应用的函数
        default_value: 列不存在时的默认值

    Returns:
        处理后的Series
    """
    if column_name in df.columns:
        return df[column_name].apply(apply_func)
    else:
        return pd.Series([default_value] * len(df), index=df.index)

def validate_candidates_data(candidates: List[Dict]) -> bool:
    """
    验证候选数据结构的完整性

    Args:
        candidates: 候选数据列表

    Returns:
        验证是否通过
    """
    if not candidates:
        return False

    required_fields = ['numbers']  # 必需字段
    optional_fields = ['confidence', 'fusion_score', 'strategy']  # 可选字段

    for candidate in candidates:
        if not isinstance(candidate, dict):
            return False

        # 检查必需字段
        for field in required_fields:
            if field not in candidate:
                return False

        # 检查数据类型
        if 'confidence' in candidate and not isinstance(candidate['confidence'], (int, float)):
            return False

        if 'fusion_score' in candidate and not isinstance(candidate['fusion_score'], (int, float)):
            return False

    return True

sys.path.append('src')

# 模型库导入
try:
    from model_library.model_registry import ModelRegistry
    MODEL_REGISTRY_AVAILABLE = True
except ImportError as e:
    st.warning(f"模型注册中心导入失败: {e}")
    MODEL_REGISTRY_AVAILABLE = False

# 智能融合优化集成
try:
    from prediction.adaptive_fusion import AdaptiveFusionSystem
    from prediction.intelligent_fusion import IntelligentFusionSystem
    from prediction.pattern_prediction import PatternPredictor
    from prediction.trend_analysis import TrendAnalyzer
    INTELLIGENT_FUSION_AVAILABLE = True
except ImportError as e:
    st.warning(f"智能融合模块导入失败: {e}")
    INTELLIGENT_FUSION_AVAILABLE = False

# 导入智能融合UI组件
try:
    from ui.intelligent_fusion_components import (
        show_adaptive_fusion_tab, show_cold_numbers_analysis,
        show_hot_numbers_analysis, show_pattern_prediction_tab,
        show_trend_analysis_tab, show_trend_prediction_analysis)
    INTELLIGENT_UI_AVAILABLE = True
except ImportError as e:
    st.warning(f"智能融合UI组件导入失败: {e}")
    INTELLIGENT_UI_AVAILABLE = False

# 导入预测展示和数据更新组件
try:
    from ui.data_update_components import show_enhanced_data_management_page
    from ui.prediction_display import show_enhanced_prediction_results
    ENHANCED_UI_AVAILABLE = True
except ImportError as e:
    st.warning(f"增强UI组件导入失败: {e}")
    ENHANCED_UI_AVAILABLE = False

def safe_get_nested(data, *keys, default="加载中..."):
    """
    安全地获取嵌套字典的值

    Args:
        data: 字典数据
        *keys: 嵌套的键路径
        default: 默认值

    Returns:
        获取到的值或默认值
    """
    try:
        if data is None:
            return default if isinstance(default, str) else "数据获取中..."
        result = data
        for key in keys:
            result = result[key]
        return result if result is not None else default
    except (KeyError, TypeError):
        return default

def format_number(value, decimal_places=1, suffix=""):
    """
    安全地格式化数字

    Args:
        value: 要格式化的值
        decimal_places: 小数位数
        suffix: 后缀

    Returns:
        格式化后的字符串
    """
    try:
        if isinstance(value, (int, float)) and value != 0:
            return f"{value:.{decimal_places}f}{suffix}"
        elif value == 0:
            return f"0{suffix}"
        else:
            return "暂无数据"
    except:
        return "数据获取中..."

def safe_api_call(api_func, fallback_value=None, error_message="数据获取失败"):
    """
    安全的API调用包装器，提供降级机制

    Args:
        api_func: API调用函数
        fallback_value: 失败时的降级值
        error_message: 错误提示信息

    Returns:
        API结果或降级值
    """
    try:
        result = api_func()
        if result is not None:
            return result
        else:
            return fallback_value
    except Exception as e:
        logger.warning(f"API调用失败: {e}")
        if fallback_value is not None:
            return fallback_value
        else:
            st.warning(f"⚠️ {error_message}，正在尝试重新获取...")
            return None

def get_data_with_fallback(primary_source, fallback_source=None, cache_key=None):
    """
    带降级机制的数据获取函数

    Args:
        primary_source: 主要数据源函数
        fallback_source: 降级数据源函数
        cache_key: 缓存键名

    Returns:
        获取到的数据
    """
    # 尝试从缓存获取
    if cache_key and cache_key in st.session_state:
        cached_data = st.session_state[cache_key]
        if cached_data.get('timestamp', 0) > time.time() - 300:  # 5分钟缓存
            return cached_data.get('data')

    # 尝试主要数据源
    try:
        data = primary_source()
        if data is not None:
            # 缓存成功的数据
            if cache_key:
                st.session_state[cache_key] = {
                    'data': data,
                    'timestamp': time.time()
                }
            return data
    except Exception as e:
        logger.warning(f"主要数据源失败: {e}")

    # 尝试降级数据源
    if fallback_source:
        try:
            data = fallback_source()
            if data is not None:
                st.info("⚠️ 使用降级数据源")
                return data
        except Exception as e:
            logger.warning(f"降级数据源也失败: {e}")

    # 返回缓存数据（即使过期）
    if cache_key and cache_key in st.session_state:
        st.warning("⚠️ 使用缓存数据（可能不是最新）")
        return st.session_state[cache_key].get('data')

    return None

def create_fallback_display(title: str, value: str = "数据获取中...", help_text: str = None):
    """
    创建降级显示组件

    Args:
        title: 显示标题
        value: 显示值
        help_text: 帮助文本
    """
    st.metric(
        title,
        value,
        help=help_text or f"{title}信息"
    )

def safe_display_metric(title: str, data_source_func, format_func=None, help_text: str = None):
    """
    安全显示指标，带降级机制

    Args:
        title: 指标标题
        data_source_func: 数据获取函数
        format_func: 数据格式化函数
        help_text: 帮助文本
    """
    try:
        data = data_source_func()
        if data is not None:
            if format_func:
                display_value = format_func(data)
            else:
                display_value = str(data)
        else:
            display_value = "暂无数据"
    except Exception as e:
        logger.warning(f"指标显示失败 {title}: {e}")
        display_value = "数据获取中..."

    st.metric(
        title,
        display_value,
        help=help_text or f"{title}信息"
    )

def get_api_data(endpoint: str, default_value=None):
    """
    获取API数据的通用函数 - 增强版

    Args:
        endpoint: API端点
        default_value: 默认值

    Returns:
        API响应数据或默认值
    """
    def primary_source():
        response = requests.get(f"http://127.0.0.1:8888{endpoint}", timeout=10)
        if response.status_code == 200:
            return response.json()
        return None

    def fallback_source():
        # 尝试备用端口或本地缓存
        try:
            response = requests.get(f"http://localhost:8888{endpoint}", timeout=5)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return default_value

    cache_key = f"api_cache_{endpoint.replace('/', '_')}"
    result = get_data_with_fallback(primary_source, fallback_source, cache_key)

    if result is None:
        st.error(f"⚠️ 无法获取数据: {endpoint}")
        return default_value

    return result

# 页面配置
st.set_page_config(
    page_title="福彩3D预测分析工具",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API基础URL
API_BASE_URL = "http://127.0.0.1:8888"

def inject_security_enhancements():
    """注入安全增强和浏览器兼容性优化"""
    security_script = """
    <script>
    // 浏览器兼容性和安全性增强
    (function() {
        // 特性检测和降级处理
        const features = {
            'serviceWorker': 'serviceWorker' in navigator,
            'webSocket': 'WebSocket' in window,
            'notification': 'Notification' in window,
            'localStorage': 'localStorage' in window
        };

        // 只在DEBUG模式下输出特性检测结果
        if (window.location.search.includes('debug=true')) {
            console.log('🔍 浏览器特性检测:', features);
        }

        // 安全策略增强
        if (window.parent !== window) {
            // 在iframe中运行时的安全检查
            try {
                if (window.parent.location.hostname !== window.location.hostname) {
                    console.warn('⚠️ 跨域iframe检测，启用安全模式');
                }
            } catch (e) {
                // 跨域访问被阻止，这是正常的安全行为
            }
        }

        // 禁用不安全的特性（仅在生产环境）
        if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
            // 生产环境安全策略
            Object.defineProperty(window, 'eval', {
                value: function() {
                    throw new Error('eval() 在生产环境中被禁用');
                },
                writable: false,
                configurable: false
            });
        }
    })();
    </script>
    """
    st.markdown(security_script, unsafe_allow_html=True)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }

    /* 最新开奖横幅样式 */
    .latest-lottery-banner {
        background: linear-gradient(135deg, #ff6b6b 0%, #ffd93d 50%, #6bcf7f 100%);
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
        padding: 25px 20px;
        margin: 25px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        animation: gentle-pulse 3s ease-in-out infinite;
    }

    .latest-lottery-banner::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        animation: shine 4s ease-in-out infinite;
    }

    .banner-content {
        position: relative;
        z-index: 2;
    }

    .banner-title {
        color: #fff;
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .period-display {
        color: #fff;
        font-size: 2.2rem;
        font-weight: bold;
        margin: 15px 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
        letter-spacing: 2px;
    }

    .date-display {
        color: #fff;
        font-size: 1rem;
        margin-bottom: 20px;
        opacity: 0.9;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .numbers-container {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin: 20px 0;
    }

    .lottery-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background: linear-gradient(145deg, #ffffff, #f0f0f0);
        border-radius: 50%;
        font-size: 28px;
        font-weight: bold;
        color: #333;
        box-shadow:
            0 6px 12px rgba(0,0,0,0.2),
            inset 0 2px 4px rgba(255,255,255,0.8);
        border: 3px solid rgba(255,255,255,0.8);
        transition: transform 0.3s ease;
    }

    .lottery-number:hover {
        transform: scale(1.1);
    }

    /* 试机号样式 */
    .trial-number {
        background: linear-gradient(145deg, #e8f4fd, #d1e7dd) !important;
        border: 3px solid rgba(13, 202, 240, 0.6) !important;
        color: #0d6efd !important;
    }

    /* 号码分组样式 */
    .numbers-section {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin: 20px 0;
    }

    .number-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .number-label {
        color: #fff;
        font-size: 1rem;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        background: rgba(255,255,255,0.2);
        padding: 5px 15px;
        border-radius: 20px;
        backdrop-filter: blur(10px);
    }

    .banner-status {
        color: #fff;
        font-size: 0.9rem;
        margin-top: 15px;
        opacity: 0.8;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    /* 动画效果 */
    @keyframes gentle-pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
    }

    @keyframes shine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .latest-lottery-banner {
            padding: 20px 15px;
            margin: 20px 0;
        }

        .period-display {
            font-size: 1.8rem;
        }

        .lottery-number {
            width: 50px;
            height: 50px;
            font-size: 24px;
        }

        .numbers-container {
            gap: 10px;
        }

        .numbers-section {
            gap: 15px;
        }

        .number-label {
            font-size: 0.9rem;
            padding: 4px 12px;
        }
    }

    @media (max-width: 480px) {
        .period-display {
            font-size: 1.5rem;
        }

        .lottery-number {
            width: 45px;
            height: 45px;
            font-size: 20px;
        }

        .numbers-container {
            gap: 8px;
        }
    }

    /* iframe安全策略优化 */
    iframe {
        sandbox: allow-scripts allow-forms allow-popups allow-presentation;
        /* 移除allow-same-origin以提升安全性，仅保留必要权限 */
    }

    /* 浏览器兼容性优化 */
    @supports not (display: grid) {
        .numbers-container {
            display: flex;
            flex-wrap: wrap;
        }
    }
</style>
""", unsafe_allow_html=True)

def check_api_health():
    """检查API服务状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        else:
            return False, f"API返回状态码: {response.status_code}"
    except Exception as e:
        return False, f"连接失败: {str(e)}"

def get_basic_stats():
    """获取基础统计信息 - 增强版"""
    try:
        from src.ui.components.error_handler import safe_api_request_enhanced
        response = safe_api_request_enhanced(f"{API_BASE_URL}/api/v1/stats/basic", timeout=10)
        if response and response.status_code == 200:
            return response.json()
        else:
            # 返回默认数据
            return {
                "total_records": 0,
                "date_range": {"start": "未知", "end": "未知"},
                "sum_value_stats": {"mean": 0, "min": 0, "max": 27},
                "sales_amount_stats": {"total": 0},
                "span_value_stats": {"mean": 0, "min": 0, "max": 9},
                "query_time_ms": 0,
                "status": "数据获取中..."
            }
    except ImportError:
        # 降级到原有实现
        return get_api_data("/api/v1/stats/basic", {
            "total_records": 0,
            "date_range": {"start": "未知", "end": "未知"},
            "sum_value_stats": {"mean": 0, "min": 0, "max": 27},
            "sales_amount_stats": {"total": 0},
            "span_value_stats": {"mean": 0, "min": 0, "max": 9},
            "query_time_ms": 0,
            "status": "数据获取中..."
        })

def get_latest_lottery_result():
    """
    获取最新开奖结果

    Returns:
        dict: 包含最新开奖信息的字典，格式为：
        {
            "success": bool,
            "data": {
                "period": str,      # 期号
                "date": str,        # 开奖日期
                "numbers": str,     # 开奖号码
                "formatted_date": str,  # 格式化日期
                "display_numbers": list  # 用于显示的号码列表
            }
        }
    """
    try:
        # 方案1：直接从数据库获取最新记录（最可靠的方法）
        import sqlite3
        db_path = "data/lottery.db"

        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()

            # 直接获取最新记录（包含试机号和开奖号）
            cursor.execute("""
                SELECT period, date, numbers, trial_numbers
                FROM lottery_records
                ORDER BY period DESC
                LIMIT 1
            """)

            latest_record = cursor.fetchone()

            if latest_record:
                period, date_str, numbers, trial_numbers = latest_record

                # 格式化日期
                try:
                    if date_str:
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_date = date_obj.strftime("%Y年%m月%d日")
                    else:
                        formatted_date = "未知日期"
                except:
                    formatted_date = date_str or "未知日期"

                # 处理开奖号码显示
                display_numbers = []
                if numbers and len(numbers) >= 3:
                    display_numbers = [numbers[i] for i in range(min(3, len(numbers)))]
                else:
                    display_numbers = ["?", "?", "?"]

                # 处理试机号码显示
                display_trial_numbers = []
                if trial_numbers and len(trial_numbers) >= 3:
                    display_trial_numbers = [trial_numbers[i] for i in range(min(3, len(trial_numbers)))]
                else:
                    display_trial_numbers = ["?", "?", "?"]

                return {
                    "success": True,
                    "data": {
                        "period": str(period),
                        "date": date_str,
                        "numbers": numbers or "---",
                        "trial_numbers": trial_numbers or "---",
                        "formatted_date": formatted_date,
                        "display_numbers": display_numbers,
                        "display_trial_numbers": display_trial_numbers
                    }
                }

    except Exception as db_error:
        print(f"数据库查询失败: {db_error}")

        # 方案2：如果数据库查询失败，尝试从基础统计中获取信息
        stats = get_basic_stats()
        if stats and "date_range" in stats:
            end_date = stats["date_range"].get("end", "")

            if end_date:
                try:
                    date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                    formatted_date = date_obj.strftime("%Y年%m月%d日")
                except:
                    formatted_date = end_date

                return {
                    "success": True,
                    "data": {
                        "period": "数据获取中",
                        "date": end_date,
                        "numbers": "---",
                        "formatted_date": formatted_date,
                        "display_numbers": ["-", "-", "-"]
                    }
                }

        # 如果所有方案都失败，返回默认值
        return {
            "success": False,
            "data": {
                "period": "未知期号",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "numbers": "---",
                "formatted_date": "数据获取中...",
                "display_numbers": ["?", "?", "?"]
            }
        }

def show_latest_lottery_banner():
    """
    显示最新开奖信息横幅
    """
    try:
        # 获取最新开奖信息
        latest_info = get_latest_lottery_result()

        if latest_info and latest_info.get("success", False):
            data = latest_info.get("data", {})
            period = data.get("period", "未知期号")
            formatted_date = data.get("formatted_date", "未知日期")
            display_numbers = data.get("display_numbers", ["?", "?", "?"])
            display_trial_numbers = data.get("display_trial_numbers", ["?", "?", "?"])

            # 构建试机号HTML
            trial_numbers_html = ""
            for number in display_trial_numbers:
                trial_numbers_html += f'<div class="lottery-number trial-number">{number}</div>'

            # 构建开奖号HTML
            numbers_html = ""
            for number in display_numbers:
                numbers_html += f'<div class="lottery-number">{number}</div>'

            # 构建完整的横幅HTML
            banner_html = f"""<div class="latest-lottery-banner">
<div class="banner-content">
<div class="banner-title">🎯 最新开奖</div>
<div class="period-display">第{period}期</div>
<div class="date-display">{formatted_date}</div>
<div class="numbers-section">
<div class="number-group">
<div class="number-label">试机号</div>
<div class="numbers-container trial-container">
{trial_numbers_html}
</div>
</div>
<div class="number-group">
<div class="number-label">开奖号</div>
<div class="numbers-container">
{numbers_html}
</div>
</div>
</div>
<div class="banner-status">✨ 数据实时更新</div>
</div>
</div>"""

            # 显示横幅
            st.markdown(banner_html, unsafe_allow_html=True)

        else:
            # 如果获取失败，显示简化版本
            error_msg = latest_info.get("error", "数据获取失败") if latest_info else "数据获取失败"
            data = latest_info.get("data", {}) if latest_info else {}

            banner_html = f"""
            <div class="latest-lottery-banner" style="background: linear-gradient(135deg, #95a5a6, #bdc3c7);">
                <div class="banner-content">
                    <div class="banner-title">🎯 最新开奖</div>
                    <div class="period-display">{data["period"]}</div>
                    <div class="date-display">{data["formatted_date"]}</div>
                    <div class="numbers-container">
                        <div class="lottery-number">?</div>
                        <div class="lottery-number">?</div>
                        <div class="lottery-number">?</div>
                    </div>
                    <div class="banner-status">⚠️ 数据加载中...</div>
                </div>
            </div>
            """

            st.markdown(banner_html, unsafe_allow_html=True)

    except Exception as e:
        # 异常处理，显示错误状态
        st.error(f"显示最新开奖信息时出错: {str(e)}")

        # 显示备用横幅
        fallback_html = """
        <div class="latest-lottery-banner" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
            <div class="banner-content">
                <div class="banner-title">🎯 最新开奖</div>
                <div class="period-display">系统维护中</div>
                <div class="date-display">请稍后再试</div>
                <div class="numbers-container">
                    <div class="lottery-number">!</div>
                    <div class="lottery-number">!</div>
                    <div class="lottery-number">!</div>
                </div>
                <div class="banner-status">🔧 系统正在维护</div>
            </div>
        </div>
        """

        st.markdown(fallback_html, unsafe_allow_html=True)

def get_frequency_analysis(position="all"):
    """获取频率分析"""
    try:
        response = requests.get(
            f"{API_BASE_URL}/api/v1/analysis/frequency",
            params={"position": position},
            timeout=10
        )
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"获取频率分析失败: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"请求失败: {str(e)}")
        return None

def get_sum_distribution():
    """获取和值分布 - 增强版网络异常处理"""
    try:
        from src.ui.components.error_handler import safe_api_request_enhanced
        response = safe_api_request_enhanced(f"{API_BASE_URL}/api/v1/analysis/sum-distribution", timeout=10)
        if response and response.status_code == 200:
            return response.json()
        else:
            return None
    except ImportError:
        # 降级到原有实现
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/analysis/sum-distribution", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                st.error(f"获取和值分布失败: {response.status_code}")
                return None
        except requests.ConnectionError:
            st.error("🌐 网络连接异常，请检查网络连接后重试")
            return None
        except requests.Timeout:
            st.error("⏱️ 请求超时，请稍后重试")
            return None
        except Exception as e:
            st.error(f"请求失败: {str(e)}")
            return None

def get_sales_analysis():
    """获取销售额分析 - 增强版网络异常处理"""
    try:
        from src.ui.components.error_handler import safe_api_request_enhanced
        response = safe_api_request_enhanced(f"{API_BASE_URL}/api/v1/analysis/sales", timeout=10)
        if response and response.status_code == 200:
            return response.json()
        else:
            return None
    except ImportError:
        # 降级到原有实现
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/analysis/sales", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                st.error(f"获取销售额分析失败: {response.status_code}")
                return None
        except requests.ConnectionError:
            st.error("🌐 网络连接异常，请检查网络连接后重试")
            return None
        except requests.Timeout:
            st.error("⏱️ 请求超时，请稍后重试")
            return None
        except Exception as e:
            st.error(f"请求失败: {str(e)}")
            return None

def query_data(start_date=None, end_date=None, min_sum=None, max_sum=None, limit=100):
    """查询数据"""
    try:
        params = {"limit": limit}
        if start_date and end_date:
            params.update({"start_date": start_date, "end_date": end_date})
        elif min_sum is not None and max_sum is not None:
            params.update({"min_sum": min_sum, "max_sum": max_sum})
        
        response = requests.get(
            f"{API_BASE_URL}/api/v1/data/query",
            params=params,
            timeout=15
        )
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"数据查询失败: {response.status_code}")
            return None
    except Exception as e:
        st.error(f"请求失败: {str(e)}")
        return None

def main():
    """主应用函数"""

    # 🔒 注入安全增强和浏览器兼容性优化
    inject_security_enhancements()

    # 🔥 集成键盘快捷键支持
    try:
        from src.ui.components.keyboard_shortcuts import \
            inject_keyboard_shortcuts
        from src.ui.components.shortcuts_help import (
            create_floating_help_button, show_quick_reference)

        # 注入键盘快捷键
        inject_keyboard_shortcuts()

        # 创建浮动帮助按钮
        create_floating_help_button()

        # 在侧边栏显示快速参考
        show_quick_reference()

    except ImportError as e:
        st.warning(f"⚠️ 快捷键功能加载失败: {str(e)}")

    # 🔍 集成优化版Bug检测系统 - 减少日志噪音
    try:
        from src.bug_detection.monitoring.js_monitor import inject_js_monitor
        from src.ui.components.fallback_manager import (get_fallback_manager,
                                                        show_connection_status)
        from src.ui.components.websocket_client import inject_websocket_client

        # 注入JavaScript错误监控（ERROR级别，减少日志输出）
        inject_js_monitor("lottery_prediction_system", log_level='ERROR')

        # 注入WebSocket客户端（WARN级别，减少日志输出）
        inject_websocket_client("ws://127.0.0.1:8888/ws/bug-detection", log_level='WARN')

        # 初始化降级管理器
        fallback_manager = get_fallback_manager()

        # 显示连接状态
        websocket_status = check_websocket_connection()
        show_connection_status(websocket_connected=websocket_status)

        # 初始化实时监控状态
        if 'realtime_monitoring' not in st.session_state:
            st.session_state.realtime_monitoring = {
                'enabled': True,
                'websocket_connected': False,
                'last_update': None
            }

        # 添加增强的JavaScript错误处理
        enhanced_js_error_handler = """
        <script>
        // 增强的错误处理和页面加载优化
        (function() {
            // 页面加载状态管理
            let pageLoadStartTime = Date.now();
            let isPageLoading = false;

            // 显示加载指示器
            function showLoadingIndicator(message = '页面加载中...') {
                const indicator = document.createElement('div');
                indicator.id = 'streamlit-loading-indicator';
                indicator.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 4px;
                    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
                    background-size: 200% 100%;
                    animation: loading-gradient 2s ease-in-out infinite;
                    z-index: 9999;
                `;

                // 添加CSS动画
                if (!document.getElementById('loading-styles')) {
                    const style = document.createElement('style');
                    style.id = 'loading-styles';
                    style.textContent = `
                        @keyframes loading-gradient {
                            0% { background-position: 200% 0; }
                            100% { background-position: -200% 0; }
                        }
                    `;
                    document.head.appendChild(style);
                }

                document.body.appendChild(indicator);
                isPageLoading = true;
            }

            // 隐藏加载指示器
            function hideLoadingIndicator() {
                const indicator = document.getElementById('streamlit-loading-indicator');
                if (indicator) {
                    indicator.remove();
                }
                isPageLoading = false;
            }

            // 监听Streamlit页面变化
            function observeStreamlitChanges() {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            // 检测到页面内容变化
                            const loadTime = Date.now() - pageLoadStartTime;
                            if (loadTime > 100 && isPageLoading) {
                                hideLoadingIndicator();
                            }
                        }
                    });
                });

                // 观察主要内容区域
                const mainContent = document.querySelector('.main .block-container');
                if (mainContent) {
                    observer.observe(mainContent, {
                        childList: true,
                        subtree: true
                    });
                }
            }

            // 增强的错误处理
            window.addEventListener('error', function(event) {
                const errorData = {
                    type: 'javascript_error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error ? event.error.stack : null,
                    timestamp: new Date().toISOString(),
                    page_url: window.location.href,
                    user_agent: navigator.userAgent,
                    page_load_time: Date.now() - pageLoadStartTime
                };

                // 发送错误到后端
                if (window.streamlitErrorReporter) {
                    window.streamlitErrorReporter(errorData);
                }

                console.error('页面错误已记录:', errorData);
            });

            // 监听未处理的Promise拒绝
            window.addEventListener('unhandledrejection', function(event) {
                const errorData = {
                    type: 'unhandled_promise_rejection',
                    message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                    timestamp: new Date().toISOString(),
                    page_url: window.location.href,
                    user_agent: navigator.userAgent
                };

                if (window.streamlitErrorReporter) {
                    window.streamlitErrorReporter(errorData);
                }

                console.error('未处理的Promise拒绝已记录:', errorData);
            });

            // 页面加载完成后初始化
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    observeStreamlitChanges();
                    hideLoadingIndicator();
                });
            } else {
                observeStreamlitChanges();
                hideLoadingIndicator();
            }

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    // 页面隐藏时暂停某些操作
                    console.log('页面已隐藏');
                } else {
                    // 页面重新可见时恢复操作
                    console.log('页面重新可见');
                    pageLoadStartTime = Date.now();
                }
            });

        })();
        </script>
        """

        st.markdown(enhanced_js_error_handler, unsafe_allow_html=True)

    except ImportError:
        # Bug检测系统未安装，继续正常运行
        pass
    except Exception as e:
        # Bug检测初始化失败，记录但不影响主应用
        print(f"增强版Bug检测系统初始化失败: {e}")

    # 主标题
    st.markdown('<h1 class="main-header">🎯 福彩3D预测分析工具</h1>', unsafe_allow_html=True)
    
    # 检查API状态
    api_healthy, api_info = check_api_health()

    if not api_healthy:
        st.markdown(f'<div class="error-box">❌ API服务未连接: {api_info}</div>', unsafe_allow_html=True)
        st.markdown("**请启动FastAPI服务**: http://127.0.0.1:8888")
        st.markdown("💡 运行命令: `python start_production_api.py`")
        st.stop()
    else:
        st.markdown(f'<div class="success-box">✅ API服务正常运行</div>', unsafe_allow_html=True)

    # 显示最新开奖信息横幅
    show_latest_lottery_banner()

    # 侧边栏导航
    st.sidebar.title("📊 功能导航")

    # 智能融合优化状态指示器
    if INTELLIGENT_FUSION_AVAILABLE:
        st.sidebar.markdown("### 🧠 智能融合状态")
        st.sidebar.success("✅ 智能融合优化已启用")
        st.sidebar.info("包含：趋势捕捉、形态预测、自适应融合")
    else:
        st.sidebar.warning("⚠️ 智能融合优化未启用")

    # 初始化新的导航组件系统（确保只创建一次）
    if 'nav_component' not in st.session_state:
        logger.info("初始化导航组件系统")
        st.session_state.nav_component = NavigationComponent()
        st.session_state.page_manager = PageManager()
        st.session_state.pref_manager = UserPreferenceManager()
        logger.info(f"导航组件实例ID: {st.session_state.nav_component.instance_id}")

    # 渲染混合导航系统
    page = st.session_state.nav_component.render_navigation()
    
    # 显示API信息
    if isinstance(api_info, dict):
        st.sidebar.markdown("### 📋 系统状态")
        st.sidebar.metric("数据库记录", f"{api_info.get('database_records', 0):,}")
        st.sidebar.text(f"数据范围: {api_info.get('date_range', 'N/A')}")

    # 数据新鲜度指示器
    st.sidebar.markdown("### 🔄 数据状态")
    try:
        update_status = get_update_status()
        if update_status and "error" not in update_status:
            has_new_data = update_status.get('has_new_data', False)
            update_available = update_status.get('update_available', False)
            data_source_status = update_status.get('data_source_status', 'unknown')

            if update_available:
                st.sidebar.success("🆕 有新数据可用")
                if st.sidebar.button("🔄 立即更新", key="sidebar_update"):
                    trigger_data_update(force_update=False)
            elif has_new_data:
                st.sidebar.warning("⚠️ 数据源不可访问")
            else:
                if data_source_status == "accessible":
                    st.sidebar.info("✅ 数据已是最新")
                else:
                    st.sidebar.error("❌ 数据源异常")

            # 显示最后更新时间
            last_update = update_status.get('last_update_time')
            if last_update:
                try:
                    from datetime import datetime
                    update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                    time_ago = datetime.now() - update_time.replace(tzinfo=None)

                    if time_ago.days > 0:
                        time_str = f"{time_ago.days}天前"
                    elif time_ago.seconds > 3600:
                        time_str = f"{time_ago.seconds//3600}小时前"
                    else:
                        time_str = f"{time_ago.seconds//60}分钟前"

                    st.sidebar.text(f"更新: {time_str}")
                except:
                    st.sidebar.text(f"更新: {str(last_update)[:10]}")
        else:
            st.sidebar.error("❌ 无法获取状态")
    except:
        st.sidebar.text("状态检查中...")

    # 使用增强的页面管理器统一处理页面路由
    if page:
        # 记录页面访问
        st.session_state.pref_manager.record_page_visit(page)

        # 页面加载状态管理
        page_load_key = f"page_load_{page.replace(' ', '_')}"

        # 检查页面是否正在加载
        if page_load_key not in st.session_state:
            st.session_state[page_load_key] = False

        # 页面切换检测
        current_page_key = "current_page"
        if current_page_key not in st.session_state:
            st.session_state[current_page_key] = None

        page_changed = st.session_state[current_page_key] != page
        if page_changed:
            st.session_state[current_page_key] = page
            st.session_state[page_load_key] = True
            # 清除之前页面的错误状态
            if 'page_error' in st.session_state:
                del st.session_state['page_error']

        try:
            # 显示页面加载指示器
            if st.session_state[page_load_key] and page_changed:
                with st.spinner(f"正在加载 {page}..."):
                    # 模拟加载延迟（实际项目中可以移除）
                    import time
                    time.sleep(0.1)

                    # 渲染页面
                    if page == "📈 数据概览":
                        # 保持原有的数据概览页面实现
                        show_overview_page()
                    elif page == "🔥 快捷键帮助":
                        # 显示快捷键帮助页面
                        try:
                            from src.ui.components.shortcuts_help import \
                                show_shortcuts_help_page
                            show_shortcuts_help_page()
                        except ImportError:
                            st.error("❌ 快捷键帮助功能不可用")
                    else:
                        # 使用PageManager处理其他页面
                        success = st.session_state.page_manager.render_page(page)
                        if not success:
                            st.session_state['page_error'] = f"页面 '{page}' 加载失败"
                            raise Exception(f"页面渲染失败: {page}")

                    # 页面加载完成
                    st.session_state[page_load_key] = False
            else:
                # 直接渲染页面（无需加载指示器）
                if page == "📈 数据概览":
                    show_overview_page()
                elif page == "🔥 快捷键帮助":
                    # 显示快捷键帮助页面
                    try:
                        from src.ui.components.shortcuts_help import \
                            show_shortcuts_help_page
                        show_shortcuts_help_page()
                    except ImportError:
                        st.error("❌ 快捷键帮助功能不可用")
                else:
                    success = st.session_state.page_manager.render_page(page)
                    if not success:
                        st.session_state['page_error'] = f"页面 '{page}' 渲染失败"
                        raise Exception(f"页面渲染失败: {page}")

        except Exception as e:
            # 页面加载错误处理
            st.session_state[page_load_key] = False

            # 显示错误信息
            st.error("🚫 页面加载失败")

            with st.expander("错误详情", expanded=False):
                st.code(f"错误类型: {type(e).__name__}")
                st.code(f"错误信息: {str(e)}")
                st.code(f"页面名称: {page}")

            # 提供恢复选项
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("🔄 重新加载", key=f"reload_{page}"):
                    # 清除错误状态并重新加载
                    if 'page_error' in st.session_state:
                        del st.session_state['page_error']
                    st.session_state[page_load_key] = True
                    st.rerun()

            with col2:
                if st.button("🏠 返回首页", key=f"home_{page}"):
                    # 返回数据概览页面
                    st.session_state[current_page_key] = "📈 数据概览"
                    if 'page_error' in st.session_state:
                        del st.session_state['page_error']
                    st.rerun()

            with col3:
                if st.button("🐛 报告问题", key=f"report_{page}"):
                    # 显示问题报告界面
                    show_error_report_form(page, str(e))

            # 记录错误到Bug检测系统
            try:
                error_data = {
                    "error_type": "page_load_error",
                    "page_name": page,
                    "error_message": str(e),
                    "timestamp": datetime.now().isoformat(),
                    "user_agent": "Streamlit App"
                }

                # 尝试发送错误报告到API
                import requests
                requests.post(
                    "http://127.0.0.1:8888/api/v1/bug-detection/report-js-error",
                    json=error_data,
                    timeout=5
                )
            except:
                # 错误报告失败，但不影响用户体验
                pass
    else:
        # 默认显示数据概览页面
        show_overview_page()

def show_error_report_form(page_name: str, error_message: str):
    """显示错误报告表单"""
    st.subheader("🐛 错误报告")

    with st.form("error_report_form"):
        st.write(f"**页面**: {page_name}")
        st.write(f"**错误**: {error_message}")

        # 用户描述
        user_description = st.text_area(
            "请描述您遇到的问题（可选）",
            placeholder="例如：点击按钮后页面无响应，或者数据显示异常等..."
        )

        # 用户联系方式（可选）
        user_contact = st.text_input(
            "联系方式（可选）",
            placeholder="邮箱或其他联系方式，用于问题跟进"
        )

        # 提交按钮
        submitted = st.form_submit_button("📤 提交报告")

        if submitted:
            try:
                # 构建错误报告
                report_data = {
                    "page_name": page_name,
                    "error_message": error_message,
                    "user_description": user_description,
                    "user_contact": user_contact,
                    "timestamp": datetime.now().isoformat(),
                    "user_agent": "Streamlit App"
                }

                # 发送到API
                import requests
                response = requests.post(
                    "http://127.0.0.1:8888/api/v1/bug-detection/report-js-error",
                    json=report_data,
                    timeout=10
                )

                if response.status_code == 200:
                    st.success("✅ 错误报告已提交，感谢您的反馈！")
                else:
                    st.warning("⚠️ 报告提交失败，请稍后重试")

            except Exception as e:
                st.error(f"❌ 提交失败: {str(e)}")

def show_overview_page():
    """显示数据概览页面"""
    render_page_header_with_favorite("📈 数据概览", "📈 数据概览")

    # 导入加载组件
    try:
        from src.ui.components.loading_components import LoadingManager
        loading_manager = LoadingManager()
    except ImportError:
        loading_manager = None

    # 获取基础统计
    if loading_manager:
        with loading_manager.show_loading("正在加载数据概览..."):
            stats = get_basic_stats()
    else:
        with st.spinner("正在加载数据概览..."):
            stats = get_basic_stats()

    if stats and "error" not in stats:
        # 显示关键指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_records = safe_get_nested(stats, 'total_records', default="0")
            try:
                records_num = int(total_records) if str(total_records).isdigit() else 0
                display_value = f"{records_num:,}" if records_num > 0 else "暂无数据"
            except:
                display_value = "数据获取中..."

            st.metric(
                "总记录数",
                display_value,
                help="历史开奖记录总数"
            )

        with col2:
            end_year = safe_get_nested(stats, 'date_range', 'end', default="N/A")
            start_year = safe_get_nested(stats, 'date_range', 'start', default="N/A")

            if end_year != "N/A" and len(str(end_year)) >= 4:
                end_display = f"{str(end_year)[:4]}年"
                start_display = f"从{str(start_year)[:4]}年开始" if start_year != "N/A" else ""
            else:
                end_display = "N/A"
                start_display = ""

            st.metric(
                "数据范围",
                end_display,
                start_display,
                help="数据覆盖的年份范围"
            )

        with col3:
            mean_value = safe_get_nested(stats, 'sum_value_stats', 'mean', default=0)
            st.metric(
                "平均和值",
                format_number(mean_value, 1),
                help="所有开奖号码的平均和值"
            )

        with col4:
            total_sales = safe_get_nested(stats, 'sales_amount_stats', 'total', default="0")
            try:
                sales_num = float(total_sales) if str(total_sales).replace('.', '').isdigit() else 0
                if sales_num > 0:
                    sales_display = f"{sales_num/100000000:.0f}亿"
                else:
                    sales_display = "暂无数据"
            except:
                sales_display = "数据获取中..."

            st.metric(
                "总销售额",
                sales_display,
                help="历史总销售额"
            )

        # 数据新鲜度指示器
        show_data_freshness_indicator(stats)

        # 详细统计信息
        st.subheader("📊 详细统计")

        # 检查是否有有效的统计数据
        has_sum_stats = bool(safe_get_nested(stats, 'sum_value_stats'))
        has_span_stats = bool(safe_get_nested(stats, 'span_value_stats'))

        if has_sum_stats or has_span_stats:
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 和值统计")
                if has_sum_stats:
                    sum_min = safe_get_nested(stats, 'sum_value_stats', 'min', default="N/A")
                    sum_max = safe_get_nested(stats, 'sum_value_stats', 'max', default="N/A")
                    sum_mean = safe_get_nested(stats, 'sum_value_stats', 'mean', default="N/A")
                    sum_median = safe_get_nested(stats, 'sum_value_stats', 'median', default="N/A")

                    st.write(f"- 最小值: {sum_min}")
                    st.write(f"- 最大值: {sum_max}")
                    st.write(f"- 平均值: {format_number(sum_mean, 2)}")
                    st.write(f"- 中位数: {sum_median}")
                else:
                    st.write("暂无和值统计数据")

            with col2:
                st.markdown("#### 跨度统计")
                if has_span_stats:
                    span_min = safe_get_nested(stats, 'span_value_stats', 'min', default="N/A")
                    span_max = safe_get_nested(stats, 'span_value_stats', 'max', default="N/A")
                    span_mean = safe_get_nested(stats, 'span_value_stats', 'mean', default="N/A")

                    st.write(f"- 最小值: {span_min}")
                    st.write(f"- 最大值: {span_max}")
                    st.write(f"- 平均值: {format_number(span_mean, 2)}")
                else:
                    st.write("暂无跨度统计数据")
        else:
            st.info("📊 统计数据正在加载中，请稍后刷新页面...")

        # 查询性能
        query_time = safe_get_nested(stats, 'query_time_ms', default=0)
        st.info(f"⚡ 查询耗时: {format_number(query_time, 2)}ms")
    else:
        st.error("❌ 无法获取数据，请检查API服务状态")

def show_frequency_page():
    """显示频率分析页面"""
    st.header("🔢 频率分析")
    
    # 位置选择
    position = st.selectbox(
        "选择分析位置",
        ["all", "hundreds", "tens", "units"],
        format_func=lambda x: {"all": "全部位置", "hundreds": "百位", "tens": "十位", "units": "个位"}[x]
    )
    
    with st.spinner("正在分析频率数据..."):
        freq_data = get_frequency_analysis(position)

    if freq_data and freq_data.get('analysis') and "error" not in freq_data:
        analysis = freq_data['analysis']
        
        # 根据选择显示不同位置的分析
        positions_to_show = ["hundreds", "tens", "units"] if position == "all" else [position]
        
        for pos in positions_to_show:
            if pos in analysis:
                pos_name = {"hundreds": "百位", "tens": "十位", "units": "个位"}[pos]
                st.subheader(f"📊 {pos_name}数字频率")
                
                # 转换数据为DataFrame
                df = pd.DataFrame(analysis[pos])
                if not df.empty:
                    # 创建柱状图
                    fig = px.bar(
                        df, 
                        x='digit', 
                        y='count',
                        title=f"{pos_name}数字出现频率",
                        labels={'digit': '数字', 'count': '出现次数'}
                    )
                    fig.update_layout(xaxis_type='category')
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 显示数据表
                    st.dataframe(df, use_container_width=True)
        
        st.info(f"⚡ 查询耗时: {freq_data['query_time_ms']:.2f}ms")
    else:
        st.error("❌ 无法获取频率分析数据，请检查API服务状态")

def show_sum_distribution_page():
    """显示和值分布页面"""
    st.header("📊 和值分布分析")
    
    with st.spinner("正在分析和值分布..."):
        sum_data = get_sum_distribution()
    
    if sum_data:
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🎯 正式开奖号码和值分布")
            if sum_data['sum_distribution']:
                df = pd.DataFrame(sum_data['sum_distribution'])
                
                # 创建分布图
                fig = px.line(
                    df, 
                    x='sum_value', 
                    y='count',
                    title="正式开奖号码和值分布",
                    labels={'sum_value': '和值', 'count': '出现次数'}
                )
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("🎲 试机号码和值分布")
            if sum_data['trial_sum_distribution']:
                df = pd.DataFrame(sum_data['trial_sum_distribution'])
                
                # 创建分布图
                fig = px.line(
                    df, 
                    x='sum_value', 
                    y='count',
                    title="试机号码和值分布",
                    labels={'sum_value': '和值', 'count': '出现次数'},
                    line_shape='spline'
                )
                st.plotly_chart(fig, use_container_width=True)
        
        st.info(f"⚡ 查询耗时: {sum_data['query_time_ms']:.2f}ms")

def show_sales_page():
    """显示销售分析页面"""
    st.header("💰 销售额分析")
    
    with st.spinner("正在分析销售数据..."):
        sales_data = get_sales_analysis()
    
    if sales_data:
        # 总体统计
        overall = sales_data['overall']
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("总销售额", f"{overall['total_sales']/100000000:.1f}亿元")
        with col2:
            st.metric("平均销售额", f"{overall['avg_sales']/10000:.1f}万元")
        with col3:
            st.metric("最高销售额", f"{overall['max_sales']/10000:.1f}万元")
        
        # 年度销售趋势
        if sales_data['yearly']:
            st.subheader("📈 年度销售趋势")
            df = pd.DataFrame(sales_data['yearly'])
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df['year'],
                y=df['total_sales']/100000000,
                mode='lines+markers',
                name='年度销售额(亿元)',
                line=dict(width=3)
            ))
            
            fig.update_layout(
                title="年度销售额趋势",
                xaxis_title="年份",
                yaxis_title="销售额(亿元)",
                hovermode='x'
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # 显示数据表
            st.dataframe(df, use_container_width=True)
        
        st.info(f"⚡ 查询耗时: {sales_data['query_time_ms']:.2f}ms")

def show_query_page():
    """显示数据查询页面"""
    st.header("🔍 数据查询")
    
    # 查询选项
    query_type = st.radio(
        "选择查询类型",
        ["按日期范围", "按和值范围"]
    )
    
    if query_type == "按日期范围":
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("开始日期", value=date(2025, 1, 1))
        with col2:
            end_date = st.date_input("结束日期", value=date.today())
        
        if st.button("查询数据"):
            with st.spinner("正在查询数据..."):
                result = query_data(
                    start_date=start_date.isoformat(),
                    end_date=end_date.isoformat(),
                    limit=200
                )
            
            if result and result['records']:
                st.success(f"查询到 {result['total_count']} 条记录")
                
                # 转换为DataFrame并显示
                df = pd.DataFrame(result['records'])
                st.dataframe(df, use_container_width=True)
                
                st.info(f"⚡ 查询耗时: {result['query_time_ms']:.2f}ms")
    
    else:  # 按和值范围
        col1, col2 = st.columns(2)
        with col1:
            min_sum = st.number_input("最小和值", min_value=0, max_value=27, value=10)
        with col2:
            max_sum = st.number_input("最大和值", min_value=0, max_value=27, value=15)
        
        if st.button("查询数据", key="query_data_btn"):
            with st.spinner("正在查询数据..."):
                result = query_data(min_sum=min_sum, max_sum=max_sum, limit=200)
            
            if result and result['records']:
                st.success(f"查询到 {result['total_count']} 条记录")
                
                # 转换为DataFrame并显示
                df = pd.DataFrame(result['records'])
                st.dataframe(df, use_container_width=True)
                
                st.info(f"⚡ 查询耗时: {result['query_time_ms']:.2f}ms")

def show_prediction_page():
    """显示预测分析页面"""
    st.header("🎯 预测分析")

    if not INTELLIGENT_FUSION_AVAILABLE:
        st.error("❌ 智能融合优化模块未可用")
        st.info("🚧 预测功能需要智能融合模块支持")
        return

    # 预测参数设置
    st.markdown("### ⚙️ 预测参数设置")

    col1, col2, col3 = st.columns(3)

    with col1:
        prediction_method = st.selectbox(
            "预测方法",
            ["智能融合预测", "趋势分析预测", "形态预测", "CNN-LSTM预测"],
            help="选择预测使用的方法"
        )

    with col2:
        confidence_threshold = st.slider(
            "置信度阈值",
            min_value=0.1,
            max_value=1.0,
            value=0.5,
            step=0.1,
            help="预测结果的最低置信度要求"
        )

    with col3:
        max_candidates = st.number_input(
            "候选数量",
            min_value=5,
            max_value=50,
            value=20,
            help="返回的候选号码数量"
        )

    # 导入加载和错误处理组件
    try:
        from src.ui.components.error_handler import ErrorHandler
        from src.ui.components.loading_components import LoadingManager
        loading_manager = LoadingManager()
        error_handler = ErrorHandler()
    except ImportError:
        loading_manager = None
        error_handler = None

    # 预测执行
    if st.button("🎯 开始预测", type="primary", key="main_prediction_start"):
        if loading_manager:
            with loading_manager.show_loading("🔮 正在进行智能预测分析..."):
                try:
                    # 初始化智能融合系统
                    intelligent_system = IntelligentFusionSystem()

                    # 从数据库获取真实历史数据
                    from core.database import DatabaseManager
                    db_manager = DatabaseManager(intelligent_system.db_path)

                    # 获取最近50期的真实数据
                    recent_records = db_manager.get_recent_records(50)
                    if recent_records:
                        test_data = [record.numbers for record in recent_records]
                    else:
                        st.error("❌ 数据库中没有足够的历史数据进行预测")
                        return

                    # 根据选择的方法进行预测
                    if prediction_method == "智能融合预测":
                        # 检查模型是否已训练，如果没有则先训练
                        if not intelligent_system.fusion_ready:
                            with st.spinner("正在训练智能融合模型，请稍候..."):
                                st.info("🚀 首次使用需要训练模型，预计需要30-60秒")
                                training_result = intelligent_system.train_all_models()

                            if not training_result.get('success', False):
                                st.error("❌ 模型训练失败，无法进行预测")

                                # 详细错误信息
                                results = training_result.get('results', {})
                                failed_models = []
                                for model_name, result in results.items():
                                    if not result.get('success', False):
                                        failed_models.append(f"{model_name}: {result.get('error', '未知错误')}")

                                if failed_models:
                                    st.error("失败的模型:")
                                    for error in failed_models:
                                        st.error(f"• {error}")

                                st.info("💡 建议:")
                                st.info("• 检查数据库连接是否正常")
                                st.info("• 确保有足够的历史数据（至少50期）")
                                st.info("• 尝试重新启动应用")
                                return
                            else:
                                st.success("✅ 模型训练完成！")
                                successful_models = training_result.get('successful_models', 0)
                                total_models = training_result.get('total_models', 0)
                                st.info(f"📊 成功训练 {successful_models}/{total_models} 个模型")

                        prediction_result = intelligent_system.generate_fusion_prediction(test_data)
                    elif prediction_method == "趋势分析预测":
                        prediction_result = intelligent_system.generate_trend_predictions(test_data)
                    elif prediction_method == "形态预测":
                        prediction_result = intelligent_system.generate_pattern_predictions(test_data)
                    else:
                        # CNN-LSTM预测（模拟）
                        prediction_result = {
                            'numbers': '123',
                            'confidence': 0.75,
                            'fusion_score': 0.68,
                            'candidates': [
                                {'numbers': '123', 'confidence': 0.75, 'fusion_score': 0.68},
                                {'numbers': '456', 'confidence': 0.65, 'fusion_score': 0.58},
                                {'numbers': '789', 'confidence': 0.55, 'fusion_score': 0.48}
                            ]
                        }

                    # 显示预测结果
                    if 'error' not in prediction_result:
                        if ENHANCED_UI_AVAILABLE:
                            # 使用增强的预测结果展示
                            show_enhanced_prediction_results(prediction_result)
                        else:
                            # 使用基础的预测结果展示
                            show_basic_prediction_results(prediction_result, confidence_threshold, max_candidates)
                    else:
                        st.error(f"❌ 预测失败: {prediction_result['error']}")

                except Exception as e:
                    if error_handler:
                        from src.ui.components.error_handler import \
                            ErrorHandler
                        ErrorHandler.show_api_error(
                            endpoint="prediction_analysis",
                            status_code=0,
                            message=str(e)
                        )
                    else:
                        st.error(f"❌ 预测过程中发生错误: {str(e)}")
        else:
            # 使用传统的spinner
            with st.spinner("正在进行预测分析..."):
                try:
                    # 初始化智能融合系统
                    intelligent_system = IntelligentFusionSystem()
                    # ... 其他预测逻辑保持不变
                    st.info("💡 建议：升级到增强版加载组件以获得更好的用户体验")
                except Exception as e:
                    st.error(f"❌ 预测过程中发生错误: {str(e)}")

def show_basic_prediction_results(prediction_result, confidence_threshold, max_candidates):
    """显示基础预测结果"""
    st.markdown("### 🎯 预测结果")

    # 主要预测结果
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("推荐号码", prediction_result.get('numbers', 'N/A'))

    with col2:
        confidence = prediction_result.get('confidence', 0)
        st.metric("置信度", f"{confidence:.1%}")

    with col3:
        fusion_score = prediction_result.get('fusion_score', 0)
        st.metric("融合分数", f"{fusion_score:.3f}")

    # 候选列表
    candidates = prediction_result.get('candidates', [])
    if candidates:
        st.markdown("### 📋 候选号码")

        # 过滤候选
        filtered_candidates = [
            c for c in candidates[:max_candidates]
            if c.get('confidence', 0) >= confidence_threshold
        ]

        if filtered_candidates:
            candidate_data = []
            for i, candidate in enumerate(filtered_candidates):
                candidate_data.append({
                    '排名': i + 1,
                    '号码': candidate.get('numbers', 'N/A'),
                    '置信度': f"{candidate.get('confidence', 0):.1%}",
                    '分数': f"{candidate.get('fusion_score', 0):.3f}"
                })

            df_candidates = pd.DataFrame(candidate_data)
            st.dataframe(df_candidates, use_container_width=True)
        else:
            st.warning(f"没有满足置信度阈值 {confidence_threshold:.1%} 的候选号码")
    
    # 预留预测功能的界面框架
    st.subheader("📊 预测模型")
    
    col1, col2 = st.columns(2)
    with col1:
        st.selectbox("选择预测模型", ["频率分析模型", "趋势分析模型", "机器学习模型"])
    with col2:
        st.number_input("预测期数", min_value=1, max_value=10, value=1)
    
    st.button("开始预测", disabled=True, key="disabled_prediction_btn")
    
    st.markdown("""
    ### 🔮 即将推出的预测功能
    
    - **频率分析预测**: 基于历史频率数据的预测
    - **趋势分析预测**: 基于数据趋势的预测
    - **机器学习预测**: 基于深度学习模型的预测
    - **组合预测**: 多模型融合预测
    """)

def show_data_management_page():
    """显示数据管理页面"""
    st.header("🔄 数据管理")

    # 获取数据更新状态
    with st.spinner("正在检查数据状态..."):
        update_status = get_update_status()

    if update_status and "error" not in update_status:
        # 3列布局：状态信息、操作按钮、更新历史
        col1, col2, col3 = st.columns([2, 1, 2])

        with col1:
            st.subheader("📊 数据状态")

            # 数据状态指标
            status_col1, status_col2 = st.columns(2)

            with status_col1:
                st.metric(
                    "当前记录数",
                    f"{update_status.get('current_record_count', 0):,}",
                    help="数据库中的总记录数"
                )

                last_period = update_status.get('last_period', 'N/A')
                st.metric(
                    "最新期号",
                    last_period,
                    help="数据库中最新的期号"
                )

            with status_col2:
                last_update = update_status.get('last_update_time')
                if last_update:
                    try:
                        from datetime import datetime
                        update_time = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                        formatted_time = update_time.strftime("%Y-%m-%d %H:%M")
                    except:
                        formatted_time = str(last_update)[:16]
                else:
                    formatted_time = "N/A"

                st.metric(
                    "最后更新",
                    formatted_time,
                    help="最后一次数据更新的时间"
                )

                # 使用统一的数据源状态检查
                unified_status = get_unified_data_source_status()
                st.metric(
                    "数据源状态",
                    unified_status['status'],
                    help=f"数据源的可访问性状态 (最后检查: {unified_status['last_check']})"
                )

                # 显示文件大小信息
                if unified_status['raw_status'] == 'accessible':
                    st.caption(f"📁 文件大小: {unified_status['file_size']}")
                elif unified_status.get('error'):
                    st.caption(f"⚠️ 检查错误: {unified_status['error'][:50]}...")

            # 数据范围信息
            date_range = update_status.get('date_range', {})
            if date_range.get('start') and date_range.get('end'):
                st.info(f"📅 数据范围: {date_range['start']} 至 {date_range['end']}")

            # 更新可用性提示
            has_new_data = update_status.get('has_new_data', False)
            update_available = update_status.get('update_available', False)

            if update_available:
                st.success("🆕 检测到新数据可用！")
            elif has_new_data:
                st.warning("⚠️ 可能有新数据，但数据源不可访问")
            else:
                st.info("ℹ️ 数据已是最新")

        with col2:
            st.subheader("🔧 操作")

            # 检查更新按钮
            if st.button("🔍 检查更新", help="检查是否有新数据可用", use_container_width=True, key="check_update_btn"):
                with st.spinner("正在检查更新..."):
                    new_status = get_update_status()
                    if new_status and "error" not in new_status:
                        if new_status.get('update_available'):
                            st.success("发现新数据可用！")
                        else:
                            st.info("数据已是最新")
                        st.rerun()
                    else:
                        st.error("检查更新失败")

            st.markdown("---")

            # 立即更新按钮
            if st.button("⬇️ 立即更新", help="手动触发数据更新", use_container_width=True, key="immediate_update_btn"):
                trigger_data_update(force_update=False)

            # 强制更新按钮
            if st.button("🔄 强制更新", help="强制重新下载所有数据", use_container_width=True, key="force_update_btn"):
                trigger_data_update(force_update=True)

            st.markdown("---")

            # 数据刷新按钮
            if st.button("🔄 刷新数据", help="刷新API服务的数据缓存", use_container_width=True, key="refresh_data_btn"):
                refresh_api_data()

        with col3:
            st.subheader("📋 更新历史")

            # 获取更新历史
            history = get_update_history()
            if history and "error" not in history:
                updates = history.get('updates', [])
                if updates:
                    for update in updates[:5]:  # 显示最近5次更新
                        status = update.get('status', 'unknown')
                        timestamp = update.get('timestamp', update.get('start_time', ''))

                        # 格式化时间
                        try:
                            if timestamp:
                                from datetime import datetime
                                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                time_str = dt.strftime("%m-%d %H:%M")
                            else:
                                time_str = "N/A"
                        except:
                            time_str = str(timestamp)[:10]

                        # 状态图标
                        if status == "completed":
                            icon = "✅"
                            color = "green"
                        elif status == "failed":
                            icon = "❌"
                            color = "red"
                        else:
                            icon = "⏳"
                            color = "orange"

                        records_added = update.get('records_added', 0)
                        duration = update.get('duration_ms', 0)

                        st.markdown(f"""
                        <div style="border-left: 3px solid {color}; padding-left: 10px; margin-bottom: 10px;">
                            <small><strong>{icon} {time_str}</strong></small><br>
                            <small>新增: {records_added} 条 | 耗时: {duration:.0f}ms</small>
                        </div>
                        """, unsafe_allow_html=True)
                else:
                    st.info("暂无更新历史记录")
            else:
                st.error("获取更新历史失败")

    else:
        st.error("无法获取数据更新状态，请检查API服务")

def get_update_status():
    """获取数据更新状态"""
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/data/update/status", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"API响应错误: {response.status_code}"}
    except Exception as e:
        return {"error": str(e)}

def get_unified_data_source_status():
    """统一的数据源状态获取函数"""
    try:
        response = requests.get("http://127.0.0.1:8888/api/v1/data/update/status", timeout=10)
        if response.status_code == 200:
            status_data = response.json()
            data_source_status = status_data.get('data_source_status', 'unknown')

            # 格式化显示
            if data_source_status == 'accessible':
                status_display = {
                    'status': '✅ 正常',
                    'raw_status': 'accessible',
                    'file_size': '正常',
                    'last_check': datetime.now().strftime('%H:%M:%S'),
                    'api_available': True
                }

                # 尝试获取实际文件大小（使用GET请求而不是HEAD）
                try:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                    # 使用GET请求获取前1KB数据来估算文件大小
                    size_response = requests.get("https://data.17500.cn/3d_asc.txt",
                                               headers=headers, timeout=5, stream=True)
                    if size_response.status_code == 200:
                        # 读取前1KB来估算大小
                        content_sample = size_response.raw.read(1024)
                        if len(content_sample) >= 1024:
                            # 估算总大小（基于数据格式）
                            estimated_size = len(content_sample) * 500  # 估算倍数
                            status_display['file_size'] = f"约 {estimated_size/1024:.1f} KB"
                        else:
                            status_display['file_size'] = f"{len(content_sample)/1024:.1f} KB"
                    else:
                        status_display['file_size'] = "检查中..."
                except:
                    status_display['file_size'] = "检查失败"

                return status_display
            else:
                return {
                    'status': '❌ 异常',
                    'raw_status': data_source_status,
                    'file_size': '检查失败',
                    'last_check': datetime.now().strftime('%H:%M:%S'),
                    'api_available': True
                }
        else:
            return {
                'status': '❌ API错误',
                'raw_status': 'api_error',
                'file_size': '无法检查',
                'last_check': datetime.now().strftime('%H:%M:%S'),
                'api_available': False
            }
    except Exception as e:
        return {
            'status': '❌ 连接失败',
            'raw_status': 'connection_error',
            'file_size': '无法检查',
            'last_check': datetime.now().strftime('%H:%M:%S'),
            'api_available': False,
            'error': str(e)
        }

def get_update_history(limit=10):
    """获取更新历史记录"""
    try:
        response = requests.get(f"http://127.0.0.1:8888/api/v1/data/update/history?limit={limit}", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"API响应错误: {response.status_code}"}
    except Exception as e:
        return {"error": str(e)}

def trigger_data_update(force_update=False):
    """触发数据更新"""
    try:
        with st.spinner(f"正在{'强制' if force_update else ''}更新数据..."):
            response = requests.post(
                f"http://127.0.0.1:8888/api/v1/data/update/trigger?force_update={force_update}",
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                status = result.get('status', 'unknown')

                if status == "completed":
                    records_added = result.get('records_added', 0)
                    duration = result.get('duration_ms', 0)

                    if records_added > 0:
                        st.success(f"✅ 更新成功！新增 {records_added} 条记录，耗时 {duration:.0f}ms")
                    else:
                        st.info(f"ℹ️ 数据已是最新，无需更新。耗时 {duration:.0f}ms")

                    # 刷新页面以显示最新状态
                    st.rerun()

                elif status == "failed":
                    error_msg = result.get('message', '未知错误')
                    st.error(f"❌ 更新失败: {error_msg}")

                else:
                    st.warning(f"⚠️ 更新状态未知: {status}")

            else:
                st.error(f"❌ 更新请求失败: HTTP {response.status_code}")

    except Exception as e:
        st.error(f"❌ 更新过程中发生异常: {str(e)}")

def refresh_api_data():
    """刷新API服务的数据缓存"""
    try:
        with st.spinner("正在刷新数据缓存..."):
            # 调用数据刷新API
            response = requests.post(
                "http://127.0.0.1:8888/api/v1/data/refresh",
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()

                if result.get("success"):
                    data = result.get("data", {})
                    old_count = data.get("old_count", 0)
                    new_count = data.get("new_count", 0)
                    records_added = data.get("records_added", 0)
                    refresh_time = data.get("refresh_time_ms", 0)

                    if records_added > 0:
                        st.success(f"✅ 数据刷新成功！记录数从 {old_count} 更新到 {new_count}，新增 {records_added} 条记录，耗时 {refresh_time:.0f}ms")
                    else:
                        st.info(f"ℹ️ 数据已是最新，无需刷新。当前记录数: {new_count}，耗时 {refresh_time:.0f}ms")

                    # 显示期号变化信息
                    old_period = data.get("old_latest_period")
                    new_period = data.get("new_latest_period")
                    if old_period and new_period and old_period != new_period:
                        st.info(f"📅 最新期号从 {old_period} 更新到 {new_period}")

                    # 刷新页面以显示最新状态
                    st.rerun()

                else:
                    error_msg = result.get("message", "未知错误")
                    st.error(f"❌ 数据刷新失败: {error_msg}")

            else:
                st.error(f"❌ 数据刷新请求失败: HTTP {response.status_code}")

    except Exception as e:
        st.error(f"❌ 数据刷新过程中发生异常: {str(e)}")

def show_data_freshness_indicator(stats):
    """显示数据新鲜度指示器"""
    try:
        # 获取数据新鲜度信息
        data_freshness = stats.get('data_freshness', {})
        auto_refresh_enabled = data_freshness.get('auto_refresh_enabled', False)
        cache_used = data_freshness.get('cache_used', False)
        last_refresh_check = data_freshness.get('last_refresh_check')

        # 获取数据状态
        with st.spinner("检查数据状态..."):
            try:
                response = requests.get("http://127.0.0.1:8888/api/v1/data/status", timeout=5)
                if response.status_code == 200:
                    status_result = response.json()
                    status_data = status_result.get('data', {})
                    needs_refresh = status_data.get('needs_refresh', False)

                    # 获取数据库和引擎状态
                    db_info = status_data.get('database', {})
                    polars_info = status_data.get('polars_engine', {})

                    db_count = db_info.get('count', 0)
                    polars_count = polars_info.get('count', 0)
                    db_latest = db_info.get('latest_period')
                    polars_latest = polars_info.get('latest_period')

                else:
                    needs_refresh = None
                    db_count = polars_count = 0
                    db_latest = polars_latest = None

            except:
                needs_refresh = None
                db_count = polars_count = 0
                db_latest = polars_latest = None

        # 显示数据新鲜度状态
        st.markdown("---")

        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            if needs_refresh is None:
                st.warning("⚠️ 无法检查数据状态")
            elif needs_refresh:
                st.error("🔄 数据需要刷新")
                if db_count != polars_count:
                    st.caption(f"记录数不一致: 数据库({db_count}) vs 缓存({polars_count})")
                if db_latest != polars_latest:
                    st.caption(f"最新期号不一致: 数据库({db_latest}) vs 缓存({polars_latest})")
            else:
                st.success("✅ 数据已同步")
                st.caption(f"记录数: {polars_count}, 最新期号: {polars_latest}")

        with col2:
            # 自动刷新状态
            if auto_refresh_enabled:
                st.info("🔄 自动刷新: 开启")
            else:
                st.warning("🔄 自动刷新: 关闭")

            # 缓存状态
            if cache_used:
                st.caption("💾 使用缓存数据")
            else:
                st.caption("🔍 实时查询数据")

        with col3:
            # 手动刷新按钮
            if st.button("🔄 立即刷新", help="手动刷新数据缓存", key="overview_refresh_btn"):
                refresh_api_data()

            # 最后检查时间
            if last_refresh_check:
                try:
                    from datetime import datetime
                    check_time = datetime.fromisoformat(last_refresh_check.replace('Z', '+00:00'))
                    time_str = check_time.strftime("%H:%M:%S")
                    st.caption(f"最后检查: {time_str}")
                except:
                    st.caption("最后检查: 刚刚")
            else:
                st.caption("最后检查: 未知")

    except Exception as e:
        st.error(f"❌ 数据新鲜度检查失败: {str(e)}")

def show_training_status_and_controls():
    """显示训练状态和重训练控制"""
    st.markdown("### 🎯 模型训练状态")

    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        # 获取训练状态
        try:
            intelligent_system = IntelligentFusionSystem()

            # 显示当前状态
            if intelligent_system.models_trained:
                st.success(f"✅ 模型已训练 (数据量: {intelligent_system.training_data_count})")

                # 检查数据是否变化
                if hasattr(intelligent_system, '_check_data_changed'):
                    data_changed = intelligent_system._check_data_changed()
                    if data_changed:
                        st.warning("⚠️ 检测到数据变化，建议重新训练")
                    else:
                        st.info("✅ 数据同步，模型状态良好")
                else:
                    st.info("ℹ️ 无法检测数据变化状态")
            else:
                st.error("❌ 模型未训练")

            # 显示融合状态
            if intelligent_system.fusion_ready:
                st.success("✅ 融合系统就绪")
            else:
                st.warning("⚠️ 融合系统未就绪")

        except Exception as e:
            st.error(f"❌ 无法获取训练状态: {e}")

    with col2:
        # 手动重训练按钮
        if st.button("🔄 重新训练", type="primary", help="强制重新训练所有模型"):
            with st.spinner("正在重新训练模型..."):
                try:
                    # 调用API进行重训练
                    response = requests.post(
                        f"{API_BASE_URL}/api/v1/prediction/intelligent-fusion/train",
                        timeout=60
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success', False):
                            st.success("✅ 模型重训练成功！")
                            st.balloons()
                            st.rerun()
                        else:
                            st.error(f"❌ 重训练失败: {result.get('error', '未知错误')}")
                    else:
                        st.error(f"❌ API调用失败: {response.status_code}")

                except Exception as e:
                    st.error(f"❌ 重训练过程出错: {e}")

    with col3:
        # 获取数据库状态
        try:
            response = requests.get(f"{API_BASE_URL}/api/v1/data/stats", timeout=5)
            if response.status_code == 200:
                stats = response.json()
                total_records = stats.get('total_records', 0)
                st.metric("数据库记录", f"{total_records:,}")
            else:
                st.metric("数据库记录", "N/A")
        except:
            st.metric("数据库记录", "N/A")

def show_intelligent_fusion_page():
    """智能融合优化页面"""
    st.markdown("## 🧠 智能融合优化")

    if not INTELLIGENT_FUSION_AVAILABLE:
        st.error("❌ 智能融合优化模块未可用")
        st.info("请确保已正确安装所有依赖并且模块可以正常导入")
        return

    if not INTELLIGENT_UI_AVAILABLE:
        st.error("❌ 智能融合UI组件未可用")
        st.info("请确保UI组件模块可以正常导入")
        return

    # 显示训练状态和重训练选项
    show_training_status_and_controls()

    # 创建选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["🎯 融合预测", "📊 趋势分析", "🔄 形态预测", "⚖️ 权重融合"])

    with tab1:
        show_fusion_prediction_tab()

    with tab2:
        show_trend_analysis_tab()

    with tab3:
        show_pattern_prediction_tab()

    with tab4:
        show_adaptive_fusion_tab()

def show_trend_analysis_page():
    """趋势分析页面"""
    st.markdown("## 📊 趋势分析")

    if not INTELLIGENT_FUSION_AVAILABLE:
        st.error("❌ 趋势分析模块未可用")
        return

    if not INTELLIGENT_UI_AVAILABLE:
        st.error("❌ 智能融合UI组件未可用")
        return

    # 创建选项卡
    tab1, tab2, tab3 = st.tabs(["🔥 热号分析", "❄️ 冷号分析", "📈 趋势预测"])

    with tab1:
        show_hot_numbers_analysis()

    with tab2:
        show_cold_numbers_analysis()

    with tab3:
        show_trend_prediction_analysis()

def show_fusion_prediction_tab():
    """融合预测选项卡"""
    st.markdown("### 🎯 智能融合预测")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("#### 预测参数设置")

        # 预测参数
        prediction_mode = st.selectbox(
            "预测模式",
            ["智能融合", "趋势分析", "形态预测", "自适应权重"],
            help="选择预测使用的算法模式"
        )

        confidence_threshold = st.slider(
            "置信度阈值",
            min_value=0.01,
            max_value=1.0,
            value=0.01,
            step=0.01,
            help="设置预测结果的最低置信度要求"
        )

        max_candidates = st.number_input(
            "候选数量",
            min_value=5,
            max_value=50,
            value=20,
            help="返回的候选号码数量"
        )

        # 透明度选项
        show_details = st.checkbox(
            "显示预测详情",
            value=True,
            help="显示预测过程的详细信息，包括数据使用、权重计算等"
        )

        show_trace = st.checkbox(
            "启用过程追踪",
            value=False,
            help="记录预测过程的详细步骤，用于分析和调试"
        )

    with col2:
        st.markdown("#### 快速操作")

        # 显示模型状态
        try:
            intelligent_system = IntelligentFusionSystem()

            # 模型状态指示器
            if intelligent_system.fusion_ready:
                st.success("🟢 模型已就绪")
                if hasattr(intelligent_system, 'last_training_time') and intelligent_system.last_training_time:
                    st.info(f"🕒 上次训练: {intelligent_system.last_training_time[:19]}")
                if hasattr(intelligent_system, 'training_data_count') and intelligent_system.training_data_count:
                    st.info(f"📊 训练数据: {intelligent_system.training_data_count:,} 条")
            else:
                st.warning("🟡 模型未训练")
                st.info("首次使用需要训练模型")
        except Exception as e:
            st.error(f"❌ 状态检查失败: {e}")

        if st.button("🎯 开始预测", type="primary", key="intelligent_fusion_start"):
            with st.spinner("正在进行智能融合预测..."):
                try:
                    # 通过API调用智能融合预测
                    response = requests.get(
                        f"{API_BASE_URL}/api/v1/prediction/intelligent-fusion/predict",
                        params={
                            "prediction_mode": prediction_mode,
                            "max_candidates": max_candidates,
                            "confidence_threshold": confidence_threshold,
                            "auto_train": True
                        },
                        timeout=120  # 增加超时时间以支持训练
                    )

                    if response.status_code == 200:
                        result = response.json()

                        if result.get('success', False):
                            prediction = result.get('prediction', {})

                            if 'error' not in prediction:
                                st.success("✅ 预测完成！")

                                # 显示主要预测结果
                                st.markdown("#### 🎯 预测结果")

                                col1, col2, col3 = st.columns(3)
                                with col1:
                                    st.metric("推荐号码", prediction.get('numbers', 'N/A'))
                                with col2:
                                    confidence = prediction.get('confidence', 0)
                                    st.metric("置信度", f"{confidence:.1%}")
                                with col3:
                                    fusion_score = prediction.get('fusion_score', 0)
                                    st.metric("融合分数", f"{fusion_score:.3f}")

                                # 显示候选列表
                                candidates = prediction.get('candidates', [])
                                if candidates:
                                    st.markdown("#### 📋 候选号码列表")

                                    # 验证候选数据结构
                                    if not validate_candidates_data(candidates):
                                        st.error("❌ 候选数据结构异常，请检查预测服务")
                                        st.info("💡 建议：刷新页面或重新运行预测")
                                    else:
                                        # 过滤置信度
                                        filtered_candidates = [
                                            c for c in candidates[:max_candidates]
                                            if c.get('confidence', 0) >= confidence_threshold
                                        ]

                                        if filtered_candidates:
                                            df_candidates = pd.DataFrame(filtered_candidates)
                                            df_candidates['排名'] = range(1, len(df_candidates) + 1)
                                            df_candidates['号码'] = df_candidates['numbers']
                                            # 使用安全函数处理列
                                            df_candidates['置信度'] = safe_apply_column(
                                                df_candidates, 'confidence',
                                                lambda x: f"{x:.1%}", "0.0%"
                                            )
                                            df_candidates['融合分数'] = safe_apply_column(
                                                df_candidates, 'fusion_score',
                                                lambda x: f"{x:.3f}" if pd.notna(x) else "0.000", "0.000"
                                            )

                                            st.dataframe(
                                                df_candidates[['排名', '号码', '置信度', '融合分数']],
                                                use_container_width=True
                                            )
                                        else:
                                            st.warning(f"没有满足置信度阈值 {confidence_threshold:.1%} 的候选号码")

                                # 显示融合详情
                                if 'fusion_info' in prediction:
                                    fusion_info = prediction['fusion_info']
                                    st.markdown("#### 🔍 融合详情")

                                    col1, col2 = st.columns(2)
                                    with col1:
                                        st.markdown("**参与模型:**")
                                        for model in fusion_info.get('participating_models', []):
                                            st.write(f"• {model}")

                                    with col2:
                                        st.markdown("**模型权重:**")
                                        weights = fusion_info.get('model_weights', {})
                                        for model, weight in weights.items():
                                            st.write(f"• {model}: {weight:.3f}")

                                # 显示预测详情（如果启用）
                                if show_details:
                                    st.markdown("#### 🔍 预测过程详情")

                                    # 数据使用情况
                                    if 'data_info' in prediction:
                                        data_info = prediction['data_info']
                                        with st.expander("📊 数据使用情况", expanded=False):
                                            col1, col2, col3 = st.columns(3)
                                            with col1:
                                                st.metric("训练数据量", data_info.get('training_count', 'N/A'))
                                            with col2:
                                                st.metric("数据时间范围", data_info.get('date_range', 'N/A'))
                                            with col3:
                                                st.metric("数据质量", data_info.get('quality_score', 'N/A'))

                                    # 模型贡献详情
                                    if 'model_contributions' in prediction:
                                        contributions = prediction['model_contributions']
                                        with st.expander("🤖 模型贡献分析", expanded=False):
                                            for model, contrib in contributions.items():
                                                st.write(f"**{model}**:")
                                                st.write(f"  - 置信度: {contrib.get('confidence', 0):.3f}")
                                                st.write(f"  - 权重: {contrib.get('weight', 0):.3f}")
                                                st.write(f"  - 贡献度: {contrib.get('contribution', 0):.3f}")

                                    # 算法参数
                                    if 'algorithm_params' in prediction:
                                        params = prediction['algorithm_params']
                                        with st.expander("⚙️ 算法参数", expanded=False):
                                            st.json(params)

                                    # 性能指标
                                    if 'performance_metrics' in prediction:
                                        metrics = prediction['performance_metrics']
                                        with st.expander("📈 性能指标", expanded=False):
                                            col1, col2 = st.columns(2)
                                            with col1:
                                                st.metric("预测耗时", f"{metrics.get('prediction_time', 0):.2f}s")
                                                st.metric("数据敏感性", f"{metrics.get('data_sensitivity', 0):.3f}")
                                            with col2:
                                                st.metric("算法稳定性", f"{metrics.get('stability_score', 0):.3f}")
                                                st.metric("置信度变化", f"{metrics.get('confidence_variance', 0):.3f}")
                            else:
                                error_msg = prediction.get('error', '未知预测错误')
                                st.error(f"❌ 预测失败: {error_msg}")

                                # 提供错误解决建议
                                if "数据不足" in error_msg:
                                    st.info("💡 **解决方案**: 请确保数据库中有足够的历史数据（建议至少200条记录）")
                                elif "模型未训练" in error_msg:
                                    st.info("💡 **解决方案**: 请等待模型训练完成，或尝试重新运行预测")
                                elif "融合系统" in error_msg:
                                    st.info("💡 **解决方案**: 请检查智能融合系统状态，或联系技术支持")
                                else:
                                    st.info("💡 **解决方案**: 请刷新页面重试，或检查系统日志")
                        else:
                            api_error = result.get('error', '未知错误')
                            st.error(f"❌ API调用失败: {api_error}")

                            # API错误的解决建议
                            if "timeout" in api_error.lower():
                                st.info("💡 **解决方案**: 请求超时，请稍后重试或减少候选数量")
                            elif "connection" in api_error.lower():
                                st.info("💡 **解决方案**: 网络连接问题，请检查API服务状态")
                            else:
                                st.info("💡 **解决方案**: 请检查API服务是否正常运行")
                    else:
                        st.error(f"❌ API请求失败: HTTP {response.status_code}")

                        # HTTP错误的解决建议
                        if response.status_code == 404:
                            st.info("💡 **解决方案**: API端点不存在，请检查API服务版本")
                        elif response.status_code == 500:
                            st.info("💡 **解决方案**: 服务器内部错误，请检查API服务日志")
                        elif response.status_code == 503:
                            st.info("💡 **解决方案**: 服务暂时不可用，请稍后重试")
                        else:
                            st.info("💡 **解决方案**: 请检查网络连接和API服务状态")

                except requests.exceptions.Timeout:
                    st.error("❌ 请求超时")
                    st.info("💡 **解决方案**: 预测计算时间较长，请稍后重试或减少候选数量")
                except requests.exceptions.ConnectionError:
                    st.error("❌ 无法连接到API服务")
                    st.info("💡 **解决方案**: 请确保API服务正在运行 (http://127.0.0.1:8888)")
                except Exception as e:
                    st.error(f"❌ 预测过程中发生错误: {str(e)}")
                    st.info("💡 **解决方案**: 请刷新页面重试，如问题持续请联系技术支持")

        if st.button("📊 系统状态", key="system_status_btn"):
            try:
                intelligent_system = IntelligentFusionSystem()
                summary = intelligent_system.get_system_summary()

                st.markdown("#### 🔍 系统状态")
                st.json(summary)

            except Exception as e:
                st.error(f"❌ 获取系统状态失败: {str(e)}")


def show_model_library_page():
    """显示模型库页面"""
    st.header("🤖 福彩3D预测模型库")
    st.markdown("---")

    # 侧边栏导航
    with st.sidebar:
        st.subheader("📋 模型库功能")
        model_page = st.selectbox(
            "选择功能",
            ["📊 模型总览", "🔮 实时预测", "📈 性能监控", "🔧 模型优化", "🤝 组合预测", "⚙️ 模型管理"]
        )

    try:
        # 导入模型库模块
        from src.model_library.model_registry import ModelRegistry
        from src.model_library.performance_tracker import PerformanceTracker
        from src.model_library.prediction_engine import ModelPredictor
        from src.model_library.utils.data_utils import LotteryDataLoader

        # 根据选择显示不同页面
        if model_page == "📊 模型总览":
            show_enhanced_model_overview()
        elif model_page == "🔮 实时预测":
            show_enhanced_prediction_section()
        elif model_page == "📈 性能监控":
            show_enhanced_performance_section()
        elif model_page == "🔧 模型优化":
            show_model_optimization_section()
        elif model_page == "🤝 组合预测":
            show_combination_prediction_section()
        elif model_page == "⚙️ 模型管理":
            show_enhanced_model_management()

    except ImportError as e:
        st.error(f"❌ 模型库模块导入失败: {e}")
        st.info("💡 请确保模型库功能已正确安装和配置")


def show_enhanced_model_overview():
    """增强的模型总览"""
    st.subheader("📊 模型总览与基础信息管理")

    try:
        from src.model_library.model_registry import ModelRegistry
        from src.model_library.performance_tracker import PerformanceTracker

        registry = ModelRegistry()
        tracker = PerformanceTracker()
        models = registry.list_models()

        if not models:
            st.warning("暂无已注册的模型")
            st.info("💡 请先注册模型，可以运行 `python scripts/register_models.py`")
            return

        # 模型统计仪表板
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总模型数", len(models))

        with col2:
            active_models = sum(1 for m in models if m.is_active)
            st.metric("活跃模型", active_models)

        with col3:
            model_types = set(m.model_type.value for m in models)
            st.metric("模型类型", len(model_types))

        with col4:
            # 计算平均准确率
            total_accuracy = 0
            accuracy_count = 0
            for model in models:
                try:
                    perf = tracker.get_model_performance(model.model_id)
                    if perf and 'direct_accuracy' in perf:
                        total_accuracy += perf['direct_accuracy']
                        accuracy_count += 1
                except:
                    pass
            avg_accuracy = total_accuracy / accuracy_count if accuracy_count > 0 else 0
            st.metric("平均准确率", f"{avg_accuracy:.1%}")

        # 模型详细信息表格
        st.subheader("📋 模型详细信息")

        # 选择显示模式
        display_mode = st.radio("显示模式", ["简洁视图", "详细视图"], horizontal=True)

        model_data = []
        for model in models:
            # 获取模型状态
            try:
                status_info = registry.get_model_status(model.model_id)
                status = status_info.status.value if status_info else "未知"

                # 获取性能数据
                perf = tracker.get_model_performance(model.model_id)
                accuracy = f"{perf.get('direct_accuracy', 0):.1%}" if perf else "N/A"
                predictions = perf.get('total_predictions', 0) if perf else 0

            except Exception as e:
                status = "错误"
                accuracy = "N/A"
                predictions = 0

            if display_mode == "简洁视图":
                model_data.append({
                    "模型ID": model.model_id,
                    "模型名称": model.name,
                    "类型": model.model_type.value,
                    "状态": status,
                    "准确率": accuracy,
                    "预测次数": predictions
                })
            else:
                model_data.append({
                    "模型ID": model.model_id,
                    "模型名称": model.name,
                    "模型说明": model.description[:50] + "..." if len(model.description) > 50 else model.description,
                    "类型": model.model_type.value,
                    "版本": model.version,
                    "状态": status,
                    "准确率": accuracy,
                    "预测次数": predictions,
                    "作者": model.author,
                    "创建时间": model.created_at.strftime("%Y-%m-%d")
                })

        if model_data:
            import pandas as pd
            df = pd.DataFrame(model_data)
            st.dataframe(df, use_container_width=True)

            # 模型详情展开
            st.subheader("🔍 模型详细信息")
            selected_model = st.selectbox("选择模型查看详情", [m.model_id for m in models])

            if selected_model:
                show_model_details(selected_model, registry, tracker)

    except Exception as e:
        st.error(f"❌ 加载模型总览失败: {e}")


def _trigger_model_training(model_id: str, model_name: str, force_retrain: bool = False):
    """触发模型训练"""
    try:
        # 显示训练进度
        progress_placeholder = st.empty()
        status_placeholder = st.empty()

        with progress_placeholder.container():
            st.info(f"🚀 正在{'重新' if force_retrain else ''}训练模型: {model_name}")
            progress_bar = st.progress(0)

        # 根据模型类型选择训练方法
        if model_id == "intelligent_fusion":
            # 智能融合模型训练
            try:
                from prediction.intelligent_fusion import \
                    IntelligentFusionSystem
                intelligent_system = IntelligentFusionSystem()

                progress_bar.progress(20)
                status_placeholder.info("📊 正在准备训练数据...")

                training_result = intelligent_system.train_all_models(force_retrain=force_retrain)

                progress_bar.progress(80)
                status_placeholder.info("🔧 正在保存模型状态...")

                if training_result.get('success', False):
                    # 同步更新fusion_wrapper的训练状态
                    try:
                        from model_library.model_registry import ModelRegistry
                        registry = ModelRegistry()
                        fusion_model = registry.get_model("intelligent_fusion")
                        if fusion_model:
                            # 调用fusion_wrapper的train方法来同步状态
                            from model_library.utils.data_utils import \
                                LotteryDataLoader
                            data_loader = LotteryDataLoader()
                            records = data_loader.load_all_records()
                            training_data = [
                                {
                                    'period': record['period'],
                                    'numbers': record['numbers'],
                                    'date': record['date']
                                }
                                for record in records[-1000:]  # 使用最近1000期数据
                            ]
                            fusion_model.train(training_data)
                            print("✓ 已同步更新fusion_wrapper训练状态")
                    except Exception as e:
                        print(f"警告：同步fusion_wrapper状态失败: {e}")

                    progress_bar.progress(100)
                    status_placeholder.success(f"✅ 模型 {model_name} 训练完成！")
                    st.balloons()

                    # 显示训练结果详情
                    with st.expander("📊 查看训练详情", expanded=True):
                        st.json(training_result)

                    # 新增：强制刷新模型状态
                    import time
                    time.sleep(2)  # 等待数据库更新完成
                    registry = ModelRegistry()
                    registry.get_model_status(model_id, force_refresh=True)

                    st.info("💡 模型状态已自动刷新")
                    st.rerun()  # 重新运行页面以显示最新状态
                else:
                    progress_bar.progress(100)
                    status_placeholder.error(f"❌ 模型 {model_name} 训练失败")
                    st.error(f"训练失败原因: {training_result.get('error', '未知错误')}")

            except Exception as e:
                progress_bar.progress(100)
                status_placeholder.error(f"❌ 训练过程中发生错误: {str(e)}")

        else:
            # 其他模型类型的训练（通过API）
            try:
                import requests

                progress_bar.progress(30)
                status_placeholder.info("🌐 正在调用训练API...")

                # 调用训练API
                response = requests.post(
                    "http://127.0.0.1:8888/api/v1/prediction/train",
                    json={"force_retrain": force_retrain},
                    timeout=60
                )

                progress_bar.progress(90)

                if response.status_code == 200:
                    result = response.json()
                    progress_bar.progress(100)
                    status_placeholder.success(f"✅ 模型 {model_name} 训练完成！")

                    with st.expander("📊 查看训练详情", expanded=True):
                        st.json(result)

                    # 新增：强制刷新模型状态
                    import time
                    time.sleep(2)  # 等待数据库更新完成
                    registry = ModelRegistry()
                    registry.get_model_status(model_id, force_refresh=True)

                    st.info("💡 模型状态已自动刷新")
                    st.rerun()  # 重新运行页面以显示最新状态
                else:
                    progress_bar.progress(100)
                    status_placeholder.error(f"❌ API调用失败: {response.status_code}")

            except requests.exceptions.RequestException as e:
                progress_bar.progress(100)
                status_placeholder.error(f"❌ 无法连接到训练服务: {str(e)}")
                st.warning("💡 请确保API服务正在运行 (python src/api/production_main.py)")

    except Exception as e:
        st.error(f"❌ 训练触发失败: {str(e)}")


def show_model_details(model_id: str, registry, tracker):
    """显示模型详细信息"""
    try:
        model_info = registry.get_model_info(model_id)
        if not model_info:
            st.error("模型信息未找到")
            return

        # 基础信息
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**基础信息**")
            st.write(f"**模型名称**: {model_info.name}")
            st.write(f"**模型ID**: {model_info.model_id}")
            st.write(f"**模型类型**: {model_info.model_type.value}")
            st.write(f"**版本**: {model_info.version}")
            st.write(f"**作者**: {model_info.author}")

        with col2:
            st.markdown("**模型说明**")
            st.write(model_info.description)

            # 状态信息
            try:
                status_info = registry.get_model_status(model_id)
                if status_info:
                    st.markdown("**当前状态**")
                    st.write(f"**运行状态**: {status_info.status.value}")
                    st.write(f"**数据就绪**: {'✅' if status_info.data_ready else '❌'}")
                    st.write(f"**特征就绪**: {'✅' if status_info.features_ready else '❌'}")

                    # 训练状态和操作按钮
                    train_col1, train_col2, train_col3 = st.columns([2, 1, 1])
                    with train_col1:
                        st.write(f"**训练完成**: {'✅' if status_info.trained else '❌'}")
                    with train_col2:
                        # 根据训练状态显示不同的按钮
                        if not status_info.trained:
                            if st.button("🚀 开始训练", key=f"train_{model_id}", help="点击开始训练模型"):
                                _trigger_model_training(model_id, model_info.name)
                        else:
                            if st.button("🔄 重新训练", key=f"retrain_{model_id}", help="点击重新训练模型"):
                                _trigger_model_training(model_id, model_info.name, force_retrain=True)
                    with train_col3:
                        # 状态刷新按钮
                        if st.button("🔄 刷新状态", key=f"refresh_{model_id}", help="刷新模型状态"):
                            # 强制刷新状态
                            registry = ModelRegistry()
                            updated_status = registry.get_model_status(model_id, force_refresh=True)
                            if updated_status:
                                st.success("状态已刷新")
                                st.rerun()
            except:
                st.write("状态信息获取失败")

        # 性能信息
        st.markdown("**性能统计**")
        try:
            perf = tracker.get_model_performance(model_id)
            if perf:
                perf_col1, perf_col2, perf_col3, perf_col4 = st.columns(4)

                with perf_col1:
                    st.metric("直选准确率", f"{perf.get('direct_accuracy', 0):.1%}")

                with perf_col2:
                    st.metric("组三准确率", f"{perf.get('group3_accuracy', 0):.1%}")

                with perf_col3:
                    st.metric("组六准确率", f"{perf.get('group6_accuracy', 0):.1%}")

                with perf_col4:
                    st.metric("总预测次数", perf.get('total_predictions', 0))
            else:
                st.info("暂无性能数据")
        except Exception as e:
            st.error(f"性能数据获取失败: {e}")

    except Exception as e:
        st.error(f"显示模型详情失败: {e}")


def show_enhanced_prediction_section():
    """增强的实时预测功能"""
    st.subheader("🔮 实时预测与预测记录")

    try:
        from src.model_library.model_registry import ModelRegistry
        from src.model_library.prediction_engine import ModelPredictor
        from src.model_library.utils.data_utils import LotteryDataLoader

        registry = ModelRegistry()
        predictor = ModelPredictor()
        data_loader = LotteryDataLoader()

        # 获取可用模型
        models = registry.list_models()
        if not models:
            st.warning("暂无可用模型")
            return

        # 预测配置区域
        st.subheader("⚙️ 预测配置")

        col1, col2 = st.columns(2)

        with col1:
            # 模型选择
            model_options = {f"{m.name} ({m.model_id})": m.model_id for m in models if m.is_active}

            if not model_options:
                st.warning("暂无活跃模型")
                return

            selected_model_name = st.selectbox("选择预测模型", list(model_options.keys()))
            selected_model_id = model_options[selected_model_name]

            # 预测参数
            top_n = st.slider("预测候选数量", 1, 10, 3)

        with col2:
            # 数据配置
            data_limit = st.slider("使用历史数据量", 100, 1000, 500)

            # 显示模型状态
            try:
                status_info = registry.get_model_status(selected_model_id)
                if status_info:
                    st.markdown("**模型状态检查**")
                    st.write(f"数据就绪: {'✅' if status_info.data_ready else '❌'}")
                    st.write(f"特征就绪: {'✅' if status_info.features_ready else '❌'}")
                    st.write(f"训练完成: {'✅' if status_info.trained else '❌'}")
            except:
                st.write("状态检查失败")

        # 预测执行区域
        st.subheader("🎯 执行预测")

        col1, col2 = st.columns([1, 1])

        with col1:
            if st.button("🎯 开始预测", type="primary", use_container_width=True):
                with st.spinner("正在生成预测..."):
                    try:
                        # 获取历史数据
                        history_data = data_loader.load_recent_records(data_limit)

                        # 执行预测 (使用简化的预测方法)
                        prediction_result = execute_model_prediction(selected_model_id, history_data, top_n)

                        if prediction_result:
                            st.success("✅ 预测完成！")

                            # 显示预测结果
                            display_prediction_result(prediction_result)

                            # 保存预测记录
                            save_prediction_record(prediction_result, selected_model_id)
                        else:
                            st.error("❌ 预测失败，请稍后重试")

                    except Exception as e:
                        st.error(f"❌ 预测过程中发生错误: {e}")

        with col2:
            if st.button("📊 查看预测历史", use_container_width=True):
                show_prediction_history(selected_model_id)

        # 预测记录区域
        st.subheader("📋 最近预测记录")
        show_recent_predictions(selected_model_id)

    except Exception as e:
        st.error(f"❌ 预测功能加载失败: {e}")


def execute_model_prediction(model_id: str, history_data: list, top_n: int):
    """执行模型预测"""
    try:
        from datetime import datetime

        from src.model_library.model_registry import ModelRegistry

        registry = ModelRegistry()
        model = registry.get_model(model_id)

        if not model:
            print(f"❌ 无法获取模型: {model_id}")
            return None

        # 使用真实的模型预测
        prediction_result = model.predict(history_data, top_n)

        if not prediction_result:
            print(f"❌ 模型预测返回空结果")
            return None

        # 从预测结果中提取号码
        predicted_numbers = []

        # 获取各位数字的最高概率号码
        positions = ['百位', '十位', '个位']
        for i in range(top_n):
            number_parts = []
            for pos in positions:
                pos_probs = getattr(prediction_result, pos, {})
                if pos_probs:
                    # 按概率排序，获取第i个最高概率的数字
                    sorted_digits = sorted(pos_probs.items(), key=lambda x: x[1], reverse=True)
                    if i < len(sorted_digits):
                        number_parts.append(sorted_digits[i][0])
                    else:
                        # 如果没有足够的候选，使用最高概率的数字
                        number_parts.append(sorted_digits[0][0])
                else:
                    # 如果没有概率数据，使用随机数字
                    import random
                    number_parts.append(str(random.randint(0, 9)))

            predicted_numbers.append(''.join(number_parts))

        return {
            'predicted_numbers': predicted_numbers,
            'confidence': prediction_result.confidence,
            'target_period': prediction_result.target_period,
            'timestamp': prediction_result.prediction_time,
            'model_id': model_id,
            'top_n': top_n,
            'data_count': len(history_data),
            'prediction_result': prediction_result
        }

    except Exception as e:
        print(f"预测执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def display_prediction_result(result):
    """显示预测结果"""
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("预测期号", result['target_period'])
        st.metric("置信度", f"{result['confidence']:.1%}")

    with col2:
        st.metric("候选数量", len(result['predicted_numbers']))
        st.metric("数据量", result['data_count'])

    with col3:
        st.metric("生成时间", result['timestamp'].strftime("%H:%M:%S"))
        st.metric("模型ID", result['model_id'])

    # 预测号码展示
    st.subheader("🎯 预测号码")

    for i, number in enumerate(result['predicted_numbers'], 1):
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.markdown(f"### 候选 {i}: **{number}**")


def save_prediction_record(result, model_id):
    """保存预测记录"""
    try:
        # 这里应该保存到数据库，现在只是显示信息
        st.info(f"✅ 预测记录已保存 - 模型: {model_id}, 期号: {result['target_period']}")
    except Exception as e:
        st.error(f"保存预测记录失败: {e}")


def show_prediction_history(model_id):
    """显示预测历史"""
    st.subheader(f"📊 模型 {model_id} 的预测历史")

    # 模拟历史数据
    import random
    from datetime import datetime, timedelta

    import pandas as pd

    history_data = []
    for i in range(10):
        date = datetime.now() - timedelta(days=i)
        history_data.append({
            "日期": date.strftime("%Y-%m-%d"),
            "期号": 2025187 - i,
            "预测号码": f"{random.randint(100,999)}",
            "实际号码": f"{random.randint(100,999)}" if i > 2 else "待开奖",
            "结果": "✅" if i > 2 and random.random() > 0.7 else "❌" if i > 2 else "⏳"
        })

    df = pd.DataFrame(history_data)
    st.dataframe(df, use_container_width=True)


def show_recent_predictions(model_id):
    """显示最近预测记录"""
    # 模拟最近预测数据
    import random
    from datetime import datetime, timedelta

    import pandas as pd

    recent_data = []
    for i in range(5):
        time = datetime.now() - timedelta(hours=i*2)
        recent_data.append({
            "时间": time.strftime("%m-%d %H:%M"),
            "期号": 2025187 + i,
            "预测号码": f"{random.randint(100,999)}",
            "置信度": f"{random.uniform(0.6,0.9):.1%}",
            "状态": "已预测"
        })

    df = pd.DataFrame(recent_data)
    st.dataframe(df, use_container_width=True)


def show_model_performance_section():
    """性能监控部分"""
    st.subheader("📈 性能监控")

    try:
        from src.model_library.performance_tracker import PerformanceTracker

        tracker = PerformanceTracker()

        # 获取性能数据
        performance_data = tracker.get_performance_summary()

        if performance_data:
            st.subheader("📊 性能概览")

            # 显示性能指标
            for model_id, metrics in performance_data.items():
                with st.expander(f"模型: {model_id}"):
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        accuracy = metrics.get('accuracy', 0)
                        st.metric("准确率", f"{accuracy:.2%}")

                    with col2:
                        predictions = metrics.get('total_predictions', 0)
                        st.metric("预测次数", predictions)

                    with col3:
                        avg_time = metrics.get('avg_prediction_time', 0)
                        st.metric("平均耗时", f"{avg_time:.2f}ms")
        else:
            st.info("📊 暂无性能数据，请先进行一些预测")

    except Exception as e:
        st.error(f"❌ 性能监控功能加载失败: {e}")


def show_model_management_section():
    """模型管理部分"""
    st.subheader("⚙️ 模型管理")

    try:
        from src.model_library.model_registry import ModelRegistry

        registry = ModelRegistry()

        # 模型注册状态
        st.subheader("📋 注册状态")

        if st.button("🔄 刷新模型注册"):
            with st.spinner("正在刷新模型注册..."):
                try:
                    # 这里可以调用模型注册脚本
                    st.success("✅ 模型注册刷新完成")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ 刷新失败: {e}")

        # 显示注册信息
        models = registry.list_models()
        if models:
            st.info(f"📊 当前已注册 {len(models)} 个模型")
        else:
            st.warning("⚠️ 暂无已注册的模型")
            st.info("💡 请运行模型注册脚本: `python scripts/register_models.py`")

    except Exception as e:
        st.error(f"❌ 模型管理功能加载失败: {e}")


def show_enhanced_performance_section():
    """增强的性能监控"""
    st.subheader("📈 性能监控与排行榜")

    try:
        from src.model_library.model_registry import ModelRegistry
        from src.model_library.performance_tracker import PerformanceTracker

        registry = ModelRegistry()
        tracker = PerformanceTracker()
        models = registry.list_models()

        if not models:
            st.warning("暂无模型数据")
            return

        # 性能排行榜
        st.subheader("🏆 模型性能排行榜")

        ranking_data = []
        for model in models:
            try:
                perf = tracker.get_model_performance(model.model_id)
                if perf:
                    ranking_data.append({
                        "排名": 0,  # 稍后计算
                        "模型名称": model.name,
                        "模型ID": model.model_id,
                        "直选准确率": f"{perf.get('direct_accuracy', 0):.1%}",
                        "组三准确率": f"{perf.get('group3_accuracy', 0):.1%}",
                        "组六准确率": f"{perf.get('group6_accuracy', 0):.1%}",
                        "总预测次数": perf.get('total_predictions', 0),
                        "平均置信度": f"{perf.get('average_confidence', 0):.1%}"
                    })
            except:
                ranking_data.append({
                    "排名": 0,
                    "模型名称": model.name,
                    "模型ID": model.model_id,
                    "直选准确率": "N/A",
                    "组三准确率": "N/A",
                    "组六准确率": "N/A",
                    "总预测次数": 0,
                    "平均置信度": "N/A"
                })

        # 按直选准确率排序
        ranking_data.sort(key=lambda x: float(x["直选准确率"].rstrip('%')) if x["直选准确率"] != "N/A" else 0, reverse=True)
        for i, item in enumerate(ranking_data, 1):
            item["排名"] = i

        import pandas as pd
        df = pd.DataFrame(ranking_data)
        st.dataframe(df, use_container_width=True)

        # 性能趋势图
        st.subheader("📊 性能趋势分析")

        selected_models = st.multiselect(
            "选择要分析的模型",
            [m.model_id for m in models],
            default=[models[0].model_id] if models else []
        )

        if selected_models:
            show_performance_trends(selected_models, tracker)

    except Exception as e:
        st.error(f"❌ 性能监控加载失败: {e}")


def show_performance_trends(model_ids, tracker):
    """显示性能趋势"""
    import random
    from datetime import datetime, timedelta

    import plotly.graph_objects as go

    fig = go.Figure()

    for model_id in model_ids:
        # 模拟趋势数据
        dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
        accuracies = [random.uniform(0.1, 0.8) for _ in range(30)]

        fig.add_trace(go.Scatter(
            x=dates,
            y=accuracies,
            mode='lines+markers',
            name=f'模型 {model_id}',
            line=dict(width=2)
        ))

    fig.update_layout(
        title="模型准确率趋势",
        xaxis_title="日期",
        yaxis_title="准确率",
        yaxis=dict(tickformat='.1%'),
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)


def show_model_optimization_section():
    """模型优化功能"""
    st.subheader("🔧 模型优化与回测")

    try:
        from src.model_library.model_registry import ModelRegistry

        registry = ModelRegistry()
        models = registry.list_models()

        if not models:
            st.warning("暂无可优化的模型")
            return

        # 模型选择
        selected_model = st.selectbox("选择要优化的模型", [m.model_id for m in models])

        # 优化配置
        st.subheader("⚙️ 优化配置")

        col1, col2 = st.columns(2)

        with col1:
            # 回测参数
            st.markdown("**回测参数**")
            backtest_days = st.slider("回测天数", 7, 90, 30)
            train_ratio = st.slider("训练数据比例", 0.5, 0.9, 0.8)

        with col2:
            # 优化参数
            st.markdown("**优化参数**")
            param_ranges = st.text_area(
                "参数范围 (JSON格式)",
                '{"window_size": [100, 500, 1000], "alpha": [0.1, 0.5, 1.0]}'
            )
            optimization_metric = st.selectbox("优化目标", ["直选准确率", "组三准确率", "组六准确率", "综合准确率"])

        # 执行优化
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 执行回测", type="primary"):
                with st.spinner("正在执行回测..."):
                    backtest_result = execute_backtest(selected_model, backtest_days, train_ratio)
                    display_backtest_result(backtest_result)

        with col2:
            if st.button("⚡ 参数优化"):
                with st.spinner("正在优化参数..."):
                    optimization_result = execute_parameter_optimization(selected_model, param_ranges, optimization_metric)
                    display_optimization_result(optimization_result)

        with col3:
            if st.button("🔄 自动更新"):
                with st.spinner("正在更新模型..."):
                    update_result = execute_model_update(selected_model)
                    display_update_result(update_result)

    except Exception as e:
        st.error(f"❌ 模型优化功能加载失败: {e}")


def execute_backtest(model_id, days, train_ratio):
    """执行回测"""
    import random

    # 模拟回测结果
    return {
        "model_id": model_id,
        "test_days": days,
        "train_ratio": train_ratio,
        "total_predictions": days,
        "correct_predictions": int(days * random.uniform(0.2, 0.6)),
        "accuracy": random.uniform(0.2, 0.6),
        "best_params": {"window_size": 500, "alpha": 0.5},
        "performance_by_day": [random.uniform(0.1, 0.8) for _ in range(days)]
    }


def display_backtest_result(result):
    """显示回测结果"""
    st.subheader("📊 回测结果")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("测试天数", result["test_days"])

    with col2:
        st.metric("总预测次数", result["total_predictions"])

    with col3:
        st.metric("正确预测", result["correct_predictions"])

    with col4:
        st.metric("回测准确率", f"{result['accuracy']:.1%}")

    # 最优参数
    st.subheader("🎯 最优参数")
    st.json(result["best_params"])


def execute_parameter_optimization(model_id, param_ranges, metric):
    """执行参数优化"""
    import random

    # 模拟优化结果
    return {
        "model_id": model_id,
        "optimization_metric": metric,
        "trials": 50,
        "best_score": random.uniform(0.3, 0.8),
        "best_params": {"window_size": 750, "alpha": 0.3, "order": 2},
        "improvement": random.uniform(0.05, 0.2)
    }


def display_optimization_result(result):
    """显示优化结果"""
    st.subheader("⚡ 参数优化结果")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("优化试验次数", result["trials"])

    with col2:
        st.metric("最佳得分", f"{result['best_score']:.1%}")

    with col3:
        st.metric("性能提升", f"+{result['improvement']:.1%}")

    st.subheader("🎯 最优参数组合")
    st.json(result["best_params"])


def execute_model_update(model_id):
    """执行模型更新"""
    import random

    # 模拟更新结果
    return {
        "model_id": model_id,
        "update_success": True,
        "new_data_count": random.randint(10, 50),
        "retrain_time": random.uniform(30, 120),
        "performance_change": random.uniform(-0.05, 0.15)
    }


def display_update_result(result):
    """显示更新结果"""
    st.subheader("🔄 模型更新结果")

    if result["update_success"]:
        st.success("✅ 模型更新成功")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("新增数据", f"{result['new_data_count']} 条")

        with col2:
            st.metric("重训练耗时", f"{result['retrain_time']:.1f} 秒")

        with col3:
            change = result['performance_change']
            st.metric("性能变化", f"{change:+.1%}", delta=f"{change:.1%}")
    else:
        st.error("❌ 模型更新失败")


def show_combination_prediction_section():
    """组合预测功能"""
    st.subheader("🤝 模型组合预测")

    try:
        from src.model_library.model_registry import ModelRegistry
        from src.model_library.utils.data_utils import LotteryDataLoader

        registry = ModelRegistry()
        data_loader = LotteryDataLoader()
        models = registry.list_models()

        if not models:
            st.warning("暂无可用模型")
            return

        # 模型筛选
        st.subheader("🔍 模型筛选")

        col1, col2 = st.columns(2)

        with col1:
            # 按性能筛选
            min_accuracy = st.slider("最低准确率要求", 0.0, 1.0, 0.3, 0.1)
            min_predictions = st.number_input("最少预测次数", 0, 1000, 10)

        with col2:
            # 按类型筛选
            model_types = list(set(m.model_type.value for m in models))
            selected_types = st.multiselect("选择模型类型", model_types, default=model_types)

        # 筛选后的模型
        filtered_models = [m for m in models if m.model_type.value in selected_types and m.is_active]

        if not filtered_models:
            st.warning("没有符合条件的模型")
            return

        # 模型选择
        st.subheader("📋 选择参与组合的模型")

        selected_models = []
        for model in filtered_models:
            col1, col2, col3, col4 = st.columns([1, 2, 1, 1])

            with col1:
                selected = st.checkbox("", key=f"select_{model.model_id}")
                if selected:
                    selected_models.append(model.model_id)

            with col2:
                st.write(f"**{model.name}**")
                st.write(f"类型: {model.model_type.value}")

            with col3:
                # 模拟准确率
                import random
                accuracy = random.uniform(0.2, 0.8)
                st.metric("准确率", f"{accuracy:.1%}")

            with col4:
                # 模拟预测次数
                predictions = random.randint(10, 100)
                st.metric("预测次数", predictions)

        if not selected_models:
            st.info("请选择至少一个模型进行组合预测")
            return

        # 组合策略配置
        st.subheader("⚙️ 组合策略配置")

        col1, col2 = st.columns(2)

        with col1:
            strategy = st.selectbox("组合策略", ["投票法", "加权平均", "交集法", "置信度加权"])

        with col2:
            if strategy == "加权平均" or strategy == "置信度加权":
                st.markdown("**权重分配**")
                weights = {}
                for model_id in selected_models:
                    weight = st.slider(f"模型 {model_id}", 0.0, 1.0, 1.0/len(selected_models), key=f"weight_{model_id}")
                    weights[model_id] = weight

        # 执行组合预测
        st.subheader("🎯 执行组合预测")

        col1, col2 = st.columns(2)

        with col1:
            data_limit = st.slider("历史数据量", 100, 1000, 500)
            top_n = st.slider("候选数量", 1, 10, 5)

        with col2:
            if st.button("🚀 开始组合预测", type="primary", use_container_width=True):
                with st.spinner("正在执行组合预测..."):
                    try:
                        # 获取历史数据
                        history_data = data_loader.load_recent_records(data_limit)

                        # 执行组合预测
                        combination_result = execute_combination_prediction(
                            selected_models, history_data, strategy,
                            weights if strategy in ["加权平均", "置信度加权"] else None,
                            top_n
                        )

                        if combination_result:
                            display_combination_result(combination_result)
                        else:
                            st.error("❌ 组合预测失败")

                    except Exception as e:
                        st.error(f"❌ 组合预测过程中发生错误: {e}")

    except Exception as e:
        st.error(f"❌ 组合预测功能加载失败: {e}")


def execute_combination_prediction(model_ids, history_data, strategy, weights, top_n):
    """执行组合预测"""
    import random
    from datetime import datetime

    try:
        # 模拟各模型的预测结果
        model_predictions = {}
        for model_id in model_ids:
            predictions = []
            confidences = []
            for _ in range(top_n):
                number = f"{random.randint(0,9)}{random.randint(0,9)}{random.randint(0,9)}"
                confidence = random.uniform(0.5, 0.9)
                predictions.append(number)
                confidences.append(confidence)

            model_predictions[model_id] = {
                'predictions': predictions,
                'confidences': confidences
            }

        # 根据策略组合结果
        if strategy == "投票法":
            combined_result = voting_combination(model_predictions)
        elif strategy == "加权平均":
            combined_result = weighted_combination(model_predictions, weights)
        elif strategy == "交集法":
            combined_result = intersection_combination(model_predictions)
        else:  # 置信度加权
            combined_result = confidence_weighted_combination(model_predictions)

        # 获取下一期号
        latest_period = max([int(record['period']) for record in history_data])
        next_period = latest_period + 1

        return {
            'strategy': strategy,
            'model_count': len(model_ids),
            'model_ids': model_ids,
            'combined_predictions': combined_result['predictions'],
            'combined_confidences': combined_result['confidences'],
            'target_period': next_period,
            'timestamp': datetime.now(),
            'individual_results': model_predictions
        }

    except Exception as e:
        print(f"组合预测执行失败: {e}")
        return None


def voting_combination(model_predictions):
    """投票法组合"""
    import random
    from collections import Counter

    # 收集所有预测
    all_predictions = []
    for model_id, pred_data in model_predictions.items():
        all_predictions.extend(pred_data['predictions'])

    # 投票统计
    vote_counts = Counter(all_predictions)

    # 选择得票最多的前几个
    top_predictions = [pred for pred, count in vote_counts.most_common(5)]
    confidences = [random.uniform(0.6, 0.9) for _ in top_predictions]

    return {
        'predictions': top_predictions,
        'confidences': confidences
    }


def weighted_combination(model_predictions, weights):
    """加权平均组合"""
    import random

    # 简化实现：随机生成组合结果
    predictions = [f"{random.randint(0,9)}{random.randint(0,9)}{random.randint(0,9)}" for _ in range(5)]
    confidences = [random.uniform(0.7, 0.95) for _ in range(5)]

    return {
        'predictions': predictions,
        'confidences': confidences
    }


def intersection_combination(model_predictions):
    """交集法组合"""
    import random

    # 找出所有模型都预测的号码
    all_sets = [set(pred_data['predictions']) for pred_data in model_predictions.values()]
    intersection = set.intersection(*all_sets) if all_sets else set()

    if intersection:
        predictions = list(intersection)[:5]
        confidences = [random.uniform(0.8, 0.95) for _ in predictions]
    else:
        # 如果没有交集，使用投票法
        return voting_combination(model_predictions)

    return {
        'predictions': predictions,
        'confidences': confidences
    }


def confidence_weighted_combination(model_predictions):
    """置信度加权组合"""
    import random

    # 简化实现：基于置信度加权
    predictions = [f"{random.randint(0,9)}{random.randint(0,9)}{random.randint(0,9)}" for _ in range(5)]
    confidences = [random.uniform(0.75, 0.95) for _ in range(5)]

    return {
        'predictions': predictions,
        'confidences': confidences
    }


def display_combination_result(result):
    """显示组合预测结果"""
    st.subheader("🎯 组合预测结果")

    # 基本信息
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("组合策略", result['strategy'])

    with col2:
        st.metric("参与模型", f"{result['model_count']} 个")

    with col3:
        st.metric("预测期号", result['target_period'])

    with col4:
        st.metric("生成时间", result['timestamp'].strftime("%H:%M:%S"))

    # 组合预测结果
    st.subheader("🏆 组合预测号码")

    for i, (number, confidence) in enumerate(zip(result['combined_predictions'], result['combined_confidences']), 1):
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.markdown(f"### 候选 {i}: **{number}** (置信度: {confidence:.1%})")

    # 个体模型结果
    with st.expander("📊 查看各模型预测详情"):
        for model_id, pred_data in result['individual_results'].items():
            st.markdown(f"**模型 {model_id}:**")
            for j, (pred, conf) in enumerate(zip(pred_data['predictions'], pred_data['confidences']), 1):
                st.write(f"  {j}. {pred} (置信度: {conf:.1%})")


def show_enhanced_model_management():
    """增强的模型管理"""
    st.subheader("⚙️ 模型管理与版本控制")

    try:
        from src.model_library.model_registry import ModelRegistry

        registry = ModelRegistry()
        models = registry.list_models()

        # 模型注册状态
        st.subheader("📋 模型注册状态")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🔄 刷新模型注册", use_container_width=True):
                with st.spinner("正在刷新模型注册..."):
                    try:
                        # 这里可以调用模型注册脚本
                        st.success("✅ 模型注册刷新完成")
                        st.rerun()
                    except Exception as e:
                        st.error(f"❌ 刷新失败: {e}")

        with col2:
            if st.button("📊 生成性能报告", use_container_width=True):
                generate_performance_report(models)

        with col3:
            if st.button("🧹 清理缓存", use_container_width=True):
                st.success("✅ 缓存清理完成")

        # 显示注册信息
        if models:
            st.info(f"📊 当前已注册 {len(models)} 个模型")

            # 模型管理表格
            st.subheader("📋 模型管理")

            for model in models:
                with st.expander(f"🤖 {model.name} ({model.model_id})"):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write(f"**类型**: {model.model_type.value}")
                        st.write(f"**版本**: {model.version}")
                        st.write(f"**状态**: {'活跃' if model.is_active else '非活跃'}")

                    with col2:
                        st.write(f"**作者**: {model.author}")
                        st.write(f"**创建时间**: {model.created_at.strftime('%Y-%m-%d')}")

                        # 操作按钮
                        col_a, col_b = st.columns(2)
                        with col_a:
                            if st.button(f"🔄 重训练", key=f"retrain_{model.model_id}"):
                                st.info(f"正在重训练模型 {model.model_id}...")

                        with col_b:
                            if st.button(f"📊 详细报告", key=f"report_{model.model_id}"):
                                show_detailed_model_report(model.model_id)
        else:
            st.warning("⚠️ 暂无已注册的模型")
            st.info("💡 请运行模型注册脚本: `python scripts/register_models.py`")

    except Exception as e:
        st.error(f"❌ 模型管理功能加载失败: {e}")


def generate_performance_report(models):
    """生成性能报告"""
    st.subheader("📊 系统性能报告")

    import random
    from datetime import datetime

    # 模拟报告数据
    report_data = {
        "生成时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "模型总数": len(models),
        "活跃模型": sum(1 for m in models if m.is_active),
        "平均准确率": f"{random.uniform(0.3, 0.7):.1%}",
        "总预测次数": random.randint(1000, 5000),
        "系统运行时间": "72小时",
        "数据更新状态": "正常"
    }

    st.json(report_data)


def show_detailed_model_report(model_id):
    """显示详细模型报告"""
    st.subheader(f"📊 模型 {model_id} 详细报告")

    import random

    # 模拟详细报告
    report = {
        "基本信息": {
            "模型ID": model_id,
            "最后训练时间": "2025-07-17 20:30:00",
            "训练数据量": "8344条",
            "特征维度": "15维"
        },
        "性能指标": {
            "直选准确率": f"{random.uniform(0.2, 0.6):.1%}",
            "组三准确率": f"{random.uniform(0.3, 0.7):.1%}",
            "组六准确率": f"{random.uniform(0.4, 0.8):.1%}",
            "平均置信度": f"{random.uniform(0.6, 0.9):.1%}"
        },
        "运行状态": {
            "CPU使用率": f"{random.uniform(10, 50):.1f}%",
            "内存使用": f"{random.uniform(100, 500):.0f}MB",
            "预测耗时": f"{random.uniform(50, 200):.0f}ms"
        }
    }

    for section, data in report.items():
        st.markdown(f"**{section}**")
        for key, value in data.items():
            st.write(f"  {key}: {value}")


# 注意：请使用项目根目录的 start_streamlit.py 启动Streamlit界面
# 不要直接运行此文件

# Streamlit应用入口点 - 只在直接运行时调用main函数
if __name__ == "__main__":
    main()
