#!/usr/bin/env python3
"""
Phase 3阶段1.1和1.2评审测试
"""

def test_phase3_stage1_review():
    print("🔍 Phase 3阶段1.1和1.2评审测试")
    
    results = {
        'import_test': False,
        'compilation_test': False,
        'integration_test': False,
        'model_creation_test': False
    }
    
    try:
        # 1. 导入测试
        print("\n1. 模块导入测试...")
        from src.prediction.intelligent_fusion import IntelligentFusionSystem
        from src.prediction.models.transformer_predictor import TransformerPredictor, create_transformer_predictor
        from src.prediction.models.optimized_cnn_lstm import OptimizedCNNLSTM
        print("✅ 所有模块导入成功")
        results['import_test'] = True
        
        # 2. 编译测试
        print("\n2. 模型创建测试...")
        transformer_config = {
            'input_size': 3,
            'd_model': 32,  # 小模型用于测试
            'num_heads': 2,
            'num_layers': 1,
            'num_classes': 1000
        }
        transformer = create_transformer_predictor(transformer_config)
        print("✅ Transformer模型创建成功")
        print(f"   参数量: {transformer.get_model_info()['total_parameters']:,}")
        results['model_creation_test'] = True
        
        # 3. 集成测试
        print("\n3. 系统集成测试...")
        fusion = IntelligentFusionSystem()
        print("✅ 融合系统初始化成功")
        print(f"   优化模型状态: {fusion.optimized_models_ready}")
        print(f"   可用模型: {list(fusion.optimized_models.keys())}")
        results['integration_test'] = True
        
        # 4. 功能测试
        print("\n4. 预测功能测试...")
        test_data = ['123', '456', '789']
        
        # 测试单独的Transformer模型
        import torch
        test_input = torch.randn(1, 5, 3)
        with torch.no_grad():
            output = transformer(test_input)
        print(f"✅ Transformer前向传播成功: {test_input.shape} -> {output.shape}")
        
        # 如果优化模型就绪，测试融合预测
        if fusion.optimized_models_ready:
            result = fusion.generate_optimized_predictions(test_data)
            if 'error' not in result:
                print("✅ 优化模型预测成功")
                print(f"   使用模型: {result.get('model_types', [])}")
            else:
                print(f"⚠️ 预测失败: {result['error']}")
        else:
            print("⚠️ 优化模型未就绪，跳过预测测试")
        
        results['compilation_test'] = True
        
        # 总结
        print("\n📊 测试结果总结:")
        for test_name, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        success_rate = sum(results.values()) / len(results) * 100
        print(f"\n🎯 总体成功率: {success_rate:.1f}%")
        
        return success_rate >= 75  # 75%以上认为通过
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_phase3_stage1_review()
    if success:
        print("\n🎉 Phase 3阶段1.1和1.2评审测试通过！")
    else:
        print("\n⚠️ Phase 3阶段1.1和1.2评审测试需要改进")
