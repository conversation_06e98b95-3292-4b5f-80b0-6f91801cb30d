"""
导航样式管理组件
提供主题配置、CSS样式管理和视觉定制功能
"""

from enum import Enum
from typing import Any, Dict, Optional

import streamlit as st


class ThemeMode(Enum):
    """主题模式枚举"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


class ColorScheme(Enum):
    """色彩方案枚举"""
    BLUE_GRADIENT = "blue_gradient"
    PURPLE_GRADIENT = "purple_gradient"
    GREEN_GRADIENT = "green_gradient"
    ORANGE_GRADIENT = "orange_gradient"
    CUSTOM = "custom"


class NavigationStyleManager:
    """导航样式管理器"""
    
    def __init__(self):
        """初始化样式管理器"""
        self.current_theme = ThemeMode.DARK
        self.current_color_scheme = ColorScheme.BLUE_GRADIENT
        self.custom_colors = {}
        
    def get_color_scheme_config(self, scheme: ColorScheme) -> Dict[str, str]:
        """获取色彩方案配置"""
        schemes = {
            ColorScheme.BLUE_GRADIENT: {
                "primary_gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                "secondary_gradient": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                "accent_color": "#4ade80",
                "text_primary": "#ffffff",
                "text_secondary": "rgba(255,255,255,0.8)",
                "background_overlay": "rgba(255,255,255,0.1)",
                "border_color": "rgba(255,255,255,0.2)",
                "shadow_color": "rgba(0,0,0,0.1)"
            },
            ColorScheme.PURPLE_GRADIENT: {
                "primary_gradient": "linear-gradient(135deg, #a855f7 0%, #ec4899 100%)",
                "secondary_gradient": "linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%)",
                "accent_color": "#10b981",
                "text_primary": "#ffffff",
                "text_secondary": "rgba(255,255,255,0.8)",
                "background_overlay": "rgba(255,255,255,0.1)",
                "border_color": "rgba(255,255,255,0.2)",
                "shadow_color": "rgba(0,0,0,0.1)"
            },
            ColorScheme.GREEN_GRADIENT: {
                "primary_gradient": "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                "secondary_gradient": "linear-gradient(135deg, #34d399 0%, #6ee7b7 100%)",
                "accent_color": "#f59e0b",
                "text_primary": "#ffffff",
                "text_secondary": "rgba(255,255,255,0.8)",
                "background_overlay": "rgba(255,255,255,0.1)",
                "border_color": "rgba(255,255,255,0.2)",
                "shadow_color": "rgba(0,0,0,0.1)"
            },
            ColorScheme.ORANGE_GRADIENT: {
                "primary_gradient": "linear-gradient(135deg, #f97316 0%, #ea580c 100%)",
                "secondary_gradient": "linear-gradient(135deg, #fb923c 0%, #fdba74 100%)",
                "accent_color": "#3b82f6",
                "text_primary": "#ffffff",
                "text_secondary": "rgba(255,255,255,0.8)",
                "background_overlay": "rgba(255,255,255,0.1)",
                "border_color": "rgba(255,255,255,0.2)",
                "shadow_color": "rgba(0,0,0,0.1)"
            }
        }
        
        return schemes.get(scheme, schemes[ColorScheme.BLUE_GRADIENT])
    
    def generate_navigation_css(self, 
                               color_scheme: Optional[ColorScheme] = None,
                               custom_config: Optional[Dict[str, str]] = None) -> str:
        """生成导航CSS样式"""
        
        # 获取色彩配置
        if custom_config:
            colors = custom_config
        else:
            scheme = color_scheme or self.current_color_scheme
            colors = self.get_color_scheme_config(scheme)
        
        css = f"""
        <style>
        /* 现代化导航样式 - 动态主题 */
        .modern-nav-container {{
            background: {colors['primary_gradient']};
            border-radius: 20px;
            box-shadow: 0 10px 40px {colors['shadow_color']};
            padding: 28px;
            margin: 20px 0;
            backdrop-filter: blur(20px);
            border: 1px solid {colors['border_color']};
            position: relative;
            overflow: hidden;
        }}
        
        .modern-nav-container::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: {colors['secondary_gradient']};
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }}
        
        .modern-nav-container:hover::before {{
            opacity: 0.1;
        }}
        
        /* 分类标题增强 */
        .nav-category-header {{
            color: {colors['text_primary']};
            font-size: 20px;
            font-weight: 700;
            margin: 24px 0 16px 0;
            text-shadow: 0 2px 8px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }}
        
        .nav-category-header::after {{
            content: '';
            flex: 1;
            height: 2px;
            background: linear-gradient(90deg, {colors['accent_color']}, transparent);
            margin-left: 16px;
        }}
        
        /* 页面按钮增强 */
        .modern-nav-button {{
            background: {colors['background_overlay']};
            border: 1px solid {colors['border_color']};
            border-radius: 16px;
            padding: 16px 20px;
            margin: 10px 0;
            color: {colors['text_primary']};
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }}
        
        .modern-nav-button::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }}
        
        .modern-nav-button:hover::before {{
            left: 100%;
        }}
        
        .modern-nav-button:hover {{
            background: rgba(255,255,255,0.2);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
            border-color: {colors['accent_color']};
        }}
        
        .modern-nav-button:active {{
            transform: translateY(-1px) scale(1.01);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }}
        
        /* 活跃状态增强 */
        .modern-nav-button.active {{
            background: rgba(255,255,255,0.25);
            border-color: {colors['accent_color']};
            box-shadow: 0 8px 30px rgba(0,0,0,0.25);
            transform: translateY(-2px);
        }}
        
        .modern-nav-button.active::after {{
            content: '';
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: {colors['accent_color']};
            border-radius: 50%;
            box-shadow: 0 0 10px {colors['accent_color']};
        }}
        
        /* 按钮内容样式 */
        .nav-button-icon {{
            font-size: 22px;
            min-width: 28px;
            text-align: center;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }}
        
        .nav-button-text {{
            font-size: 15px;
            font-weight: 600;
            flex: 1;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }}
        
        .nav-button-badge {{
            background: {colors['accent_color']};
            color: white;
            border-radius: 12px;
            padding: 4px 10px;
            font-size: 11px;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }}
        
        /* 状态指示器增强 */
        .enhanced-status-panel {{
            background: rgba(255,255,255,0.08);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid {colors['border_color']};
            backdrop-filter: blur(15px);
        }}
        
        .status-title {{
            color: {colors['text_primary']};
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .status-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
        }}
        
        .status-item {{
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }}
        
        .status-item:hover {{
            background: rgba(255,255,255,0.1);
            transform: translateY(-2px);
        }}
        
        .status-label {{
            font-size: 12px;
            color: {colors['text_secondary']};
            margin-bottom: 4px;
        }}
        
        .status-value {{
            font-size: 14px;
            font-weight: 600;
            color: {colors['accent_color']};
        }}
        
        /* 搜索框增强 */
        .enhanced-search-box {{
            background: rgba(255,255,255,0.08);
            border: 2px solid {colors['border_color']};
            border-radius: 16px;
            padding: 14px 20px;
            color: {colors['text_primary']};
            width: 100%;
            margin-bottom: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }}
        
        .enhanced-search-box:focus {{
            outline: none;
            background: rgba(255,255,255,0.12);
            border-color: {colors['accent_color']};
            box-shadow: 0 0 0 4px rgba(74, 222, 128, 0.1);
            transform: translateY(-1px);
        }}
        
        .enhanced-search-box::placeholder {{
            color: {colors['text_secondary']};
        }}
        
        /* 响应式设计增强 */
        @media (max-width: 768px) {{
            .modern-nav-container {{
                padding: 20px;
                margin: 12px 0;
                border-radius: 16px;
            }}
            
            .modern-nav-button {{
                padding: 12px 16px;
                border-radius: 12px;
            }}
            
            .nav-button-text {{
                font-size: 14px;
            }}
            
            .status-grid {{
                grid-template-columns: 1fr 1fr;
            }}
        }}
        
        /* 动画效果 */
        @keyframes fadeInUp {{
            from {{
                opacity: 0;
                transform: translateY(20px);
            }}
            to {{
                opacity: 1;
                transform: translateY(0);
            }}
        }}
        
        .nav-fade-in {{
            animation: fadeInUp 0.6s ease-out;
        }}
        
        @keyframes pulse {{
            0%, 100% {{ opacity: 1; }}
            50% {{ opacity: 0.8; }}
        }}
        
        .nav-pulse {{
            animation: pulse 2s ease-in-out infinite;
        }}
        
        /* 加载状态 */
        .nav-loading-spinner {{
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: {colors['accent_color']};
            animation: spin 1s linear infinite;
        }}
        
        @keyframes spin {{
            to {{ transform: rotate(360deg); }}
        }}

        /* 高级动画效果 */
        @keyframes slideInLeft {{
            from {{
                opacity: 0;
                transform: translateX(-30px);
            }}
            to {{
                opacity: 1;
                transform: translateX(0);
            }}
        }}

        @keyframes bounceIn {{
            0% {{
                opacity: 0;
                transform: scale(0.3);
            }}
            50% {{
                opacity: 1;
                transform: scale(1.05);
            }}
            70% {{
                transform: scale(0.9);
            }}
            100% {{
                opacity: 1;
                transform: scale(1);
            }}
        }}

        @keyframes shimmer {{
            0% {{
                background-position: -200px 0;
            }}
            100% {{
                background-position: calc(200px + 100%) 0;
            }}
        }}

        .nav-slide-in {{
            animation: slideInLeft 0.5s ease-out;
        }}

        .nav-bounce-in {{
            animation: bounceIn 0.8s ease-out;
        }}

        .nav-shimmer {{
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }}

        @keyframes glow {{
            0%, 100% {{
                box-shadow: 0 0 5px {colors['accent_color']};
            }}
            50% {{
                box-shadow: 0 0 20px {colors['accent_color']}, 0 0 30px {colors['accent_color']};
            }}
        }}

        .nav-glow {{
            animation: glow 2s ease-in-out infinite;
        }}

        /* 悬停波纹效果 */
        .nav-ripple {{
            position: relative;
            overflow: hidden;
        }}

        .nav-ripple::after {{
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }}

        .nav-ripple:hover::after {{
            width: 300px;
            height: 300px;
        }}
        </style>
        """
        
        return css
    
    def inject_theme_styles(self, 
                           color_scheme: Optional[ColorScheme] = None,
                           custom_config: Optional[Dict[str, str]] = None) -> None:
        """注入主题样式到页面"""
        css = self.generate_navigation_css(color_scheme, custom_config)
        st.markdown(css, unsafe_allow_html=True)
    
    def create_theme_selector(self) -> ColorScheme:
        """创建主题选择器"""
        st.sidebar.markdown("### 🎨 主题设置")
        
        theme_options = {
            "蓝色渐变": ColorScheme.BLUE_GRADIENT,
            "紫色渐变": ColorScheme.PURPLE_GRADIENT,
            "绿色渐变": ColorScheme.GREEN_GRADIENT,
            "橙色渐变": ColorScheme.ORANGE_GRADIENT
        }
        
        selected_theme = st.sidebar.selectbox(
            "选择色彩主题",
            options=list(theme_options.keys()),
            index=0,
            key="theme_selector"
        )
        
        return theme_options[selected_theme]
    
    def get_responsive_config(self) -> Dict[str, Any]:
        """获取响应式配置"""
        return {
            "mobile_breakpoint": 768,
            "tablet_breakpoint": 1024,
            "desktop_breakpoint": 1200,
            "container_padding": {
                "mobile": "16px",
                "tablet": "24px", 
                "desktop": "28px"
            },
            "button_padding": {
                "mobile": "12px 16px",
                "tablet": "14px 18px",
                "desktop": "16px 20px"
            }
        }
