"""
Polars引擎性能对比测试

对比原版和优化版的性能差异。
"""

import time
import logging
from typing import Dict, Any, List
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.polars_engine import PolarsEngine
from core.polars_engine_optimized import OptimizedPolarsEngine

logger = logging.getLogger(__name__)


class PolarsPerformanceTester:
    """Polars性能测试器"""
    
    def __init__(self):
        self.original_engine = PolarsEngine()
        self.optimized_engine = OptimizedPolarsEngine()
        self.test_data = self._generate_test_data()
    
    def _generate_test_data(self, count: int = 1000) -> List[Any]:
        """生成测试数据"""
        class MockRecord:
            def __init__(self, period, numbers):
                self.period = period
                self.date = f"2025-01-{(period % 31) + 1:02d}"
                self.numbers = numbers
                self.trial_numbers = f"{(int(numbers) + 100) % 1000:03d}"
                self.draw_machine = f"机器{period % 5 + 1}"
                self.trial_machine = f"试机{period % 3 + 1}"
                self.sales_amount = 1000000 + (period * 1000)
                self.direct_prize = 1040
                self.group3_prize = 346
                self.group6_prize = 173
        
        test_records = []
        for i in range(count):
            # 生成随机3位数
            numbers = f"{(i * 7 + 123) % 1000:03d}"
            record = MockRecord(f"2025{i:03d}", numbers)
            test_records.append(record)
        
        return test_records
    
    def run_performance_comparison(self) -> Dict[str, Any]:
        """运行性能对比测试"""
        results = {
            "test_data_count": len(self.test_data),
            "original_engine": {},
            "optimized_engine": {},
            "performance_improvement": {}
        }
        
        print("🚀 开始Polars引擎性能对比测试...")
        print(f"📊 测试数据量: {len(self.test_data)}条记录")
        
        # 测试原版引擎
        print("\n📈 测试原版引擎...")
        original_results = self._test_original_engine()
        results["original_engine"] = original_results
        
        # 测试优化版引擎
        print("\n⚡ 测试优化版引擎...")
        optimized_results = self._test_optimized_engine()
        results["optimized_engine"] = optimized_results
        
        # 计算性能提升
        improvement = self._calculate_improvement(original_results, optimized_results)
        results["performance_improvement"] = improvement
        
        # 输出结果
        self._print_results(results)
        
        return results
    
    def _test_original_engine(self) -> Dict[str, Any]:
        """测试原版引擎"""
        results = {}
        
        # 数据加载测试
        start_time = time.time()
        load_success = self.original_engine.load_from_records(self.test_data)
        load_time = time.time() - start_time
        
        results["load_time"] = load_time
        results["load_success"] = load_success
        
        if not load_success:
            return results
        
        # 频率分析测试
        start_time = time.time()
        freq_analysis = self.original_engine.get_frequency_analysis()
        freq_time = time.time() - start_time
        
        results["frequency_analysis_time"] = freq_time
        results["frequency_analysis_success"] = bool(freq_analysis)
        
        # 基础统计测试
        start_time = time.time()
        basic_stats = self.original_engine.get_basic_stats()
        stats_time = time.time() - start_time
        
        results["basic_stats_time"] = stats_time
        results["basic_stats_success"] = bool(basic_stats)
        
        # 总时间
        results["total_time"] = load_time + freq_time + stats_time
        
        return results
    
    def _test_optimized_engine(self) -> Dict[str, Any]:
        """测试优化版引擎"""
        results = {}
        
        # 数据加载测试
        start_time = time.time()
        load_success = self.optimized_engine.load_from_records(self.test_data, enable_streaming=True)
        load_time = time.time() - start_time
        
        results["load_time"] = load_time
        results["load_success"] = load_success
        
        if not load_success:
            return results
        
        # 频率分析测试（首次查询）
        start_time = time.time()
        freq_analysis = self.optimized_engine.get_frequency_analysis()
        freq_time_first = time.time() - start_time
        
        # 频率分析测试（缓存查询）
        start_time = time.time()
        freq_analysis_cached = self.optimized_engine.get_frequency_analysis()
        freq_time_cached = time.time() - start_time
        
        results["frequency_analysis_time_first"] = freq_time_first
        results["frequency_analysis_time_cached"] = freq_time_cached
        results["frequency_analysis_success"] = bool(freq_analysis)
        
        # 优化基础统计测试
        start_time = time.time()
        basic_stats = self.optimized_engine.get_optimized_basic_stats()
        stats_time = time.time() - start_time
        
        results["basic_stats_time"] = stats_time
        results["basic_stats_success"] = bool(basic_stats)
        
        # 获取性能指标
        performance_metrics = self.optimized_engine.get_performance_metrics()
        results["performance_metrics"] = performance_metrics
        
        # 总时间（使用缓存后的时间）
        results["total_time"] = load_time + freq_time_cached + stats_time
        
        return results
    
    def _calculate_improvement(self, original: Dict[str, Any], optimized: Dict[str, Any]) -> Dict[str, Any]:
        """计算性能提升"""
        improvement = {}
        
        # 计算各项性能提升百分比
        metrics = ["load_time", "basic_stats_time", "total_time"]
        
        for metric in metrics:
            if metric in original and metric in optimized:
                original_value = original[metric]
                optimized_value = optimized[metric]
                
                if original_value > 0:
                    improvement_pct = ((original_value - optimized_value) / original_value) * 100
                    improvement[f"{metric}_improvement_pct"] = round(improvement_pct, 2)
        
        # 频率分析特殊处理（对比缓存效果）
        if "frequency_analysis_time" in original and "frequency_analysis_time_cached" in optimized:
            original_freq = original["frequency_analysis_time"]
            optimized_freq = optimized["frequency_analysis_time_cached"]
            
            if original_freq > 0:
                freq_improvement = ((original_freq - optimized_freq) / original_freq) * 100
                improvement["frequency_analysis_improvement_pct"] = round(freq_improvement, 2)
        
        return improvement
    
    def _print_results(self, results: Dict[str, Any]):
        """打印测试结果"""
        print("\n" + "="*60)
        print("📊 Polars引擎性能对比测试结果")
        print("="*60)
        
        original = results["original_engine"]
        optimized = results["optimized_engine"]
        improvement = results["performance_improvement"]
        
        print(f"\n📈 原版引擎性能:")
        print(f"  数据加载时间: {original.get('load_time', 0):.3f}秒")
        print(f"  频率分析时间: {original.get('frequency_analysis_time', 0):.3f}秒")
        print(f"  基础统计时间: {original.get('basic_stats_time', 0):.3f}秒")
        print(f"  总执行时间: {original.get('total_time', 0):.3f}秒")
        
        print(f"\n⚡ 优化版引擎性能:")
        print(f"  数据加载时间: {optimized.get('load_time', 0):.3f}秒")
        print(f"  频率分析时间(首次): {optimized.get('frequency_analysis_time_first', 0):.3f}秒")
        print(f"  频率分析时间(缓存): {optimized.get('frequency_analysis_time_cached', 0):.3f}秒")
        print(f"  基础统计时间: {optimized.get('basic_stats_time', 0):.3f}秒")
        print(f"  总执行时间: {optimized.get('total_time', 0):.3f}秒")
        
        print(f"\n🚀 性能提升:")
        for key, value in improvement.items():
            metric_name = key.replace("_improvement_pct", "").replace("_", " ").title()
            print(f"  {metric_name}: {value:+.2f}%")
        
        # 显示缓存性能
        if "performance_metrics" in optimized:
            metrics = optimized["performance_metrics"]
            print(f"\n💾 缓存性能:")
            print(f"  缓存命中率: {metrics.get('cache_hit_rate', 0):.2f}%")
            print(f"  平均查询时间: {metrics.get('avg_query_time_ms', 0):.2f}ms")
            print(f"  流式处理: {'启用' if metrics.get('streaming_enabled') else '禁用'}")


def main():
    """主测试函数"""
    tester = PolarsPerformanceTester()
    results = tester.run_performance_comparison()
    
    # 保存结果到文件
    import json
    with open("polars_performance_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试结果已保存到: polars_performance_results.json")


if __name__ == "__main__":
    main()
