"""
Polars引擎优化版本

针对性能瓶颈进行优化，提升查询效率和内存使用。
"""

import logging
from typing import Any, Dict, List, Optional
import polars as pl
from functools import lru_cache
import time

logger = logging.getLogger(__name__)


class OptimizedPolarsEngine:
    """优化版Polars数据分析引擎"""
    
    def __init__(self):
        """初始化优化引擎"""
        self.df = None
        self.lazy_df = None
        self.logger = logger
        self._cache_enabled = True
        self._streaming_enabled = True
        
        # 性能监控
        self._query_times = []
        self._cache_hits = 0
        self._cache_misses = 0
    
    def load_from_records(self, records: List[Any], enable_streaming: bool = True) -> bool:
        """
        从记录加载数据，启用流式处理优化
        
        Args:
            records: 数据记录列表
            enable_streaming: 是否启用流式处理
            
        Returns:
            是否加载成功
        """
        try:
            start_time = time.time()
            
            if not records:
                self.logger.warning("没有数据记录")
                return False
            
            # 构建数据字典
            data_dict = {
                "period": [],
                "date": [],
                "numbers": [],
                "number_list": [],
                "sum_value": [],
                "span_value": [],
                "trial_numbers": [],
                "draw_machine": [],
                "trial_machine": [],
                "sales_amount": [],
                "direct_prize": [],
                "group3_prize": [],
                "group6_prize": []
            }
            
            # 批量处理记录
            for record in records:
                try:
                    numbers_str = str(record.numbers) if hasattr(record, 'numbers') else "000"
                    number_list = [int(d) for d in numbers_str]
                    
                    data_dict["period"].append(getattr(record, 'period', ''))
                    data_dict["date"].append(getattr(record, 'date', ''))
                    data_dict["numbers"].append(numbers_str)
                    data_dict["number_list"].append(number_list)
                    data_dict["sum_value"].append(sum(number_list))
                    data_dict["span_value"].append(max(number_list) - min(number_list))
                    data_dict["trial_numbers"].append(getattr(record, 'trial_numbers', ''))
                    data_dict["draw_machine"].append(getattr(record, 'draw_machine', ''))
                    data_dict["trial_machine"].append(getattr(record, 'trial_machine', ''))
                    data_dict["sales_amount"].append(getattr(record, 'sales_amount', 0))
                    data_dict["direct_prize"].append(getattr(record, 'direct_prize', 1040))
                    data_dict["group3_prize"].append(getattr(record, 'group3_prize', 346))
                    data_dict["group6_prize"].append(getattr(record, 'group6_prize', 173))
                    
                except Exception as e:
                    self.logger.warning(f"处理记录失败: {e}")
                    continue
            
            # 创建DataFrame
            self.df = pl.DataFrame(data_dict)
            
            # 创建lazy DataFrame用于优化查询
            if enable_streaming:
                self.lazy_df = self.df.lazy()
                self._streaming_enabled = True
            
            load_time = time.time() - start_time
            self.logger.info(f"数据加载完成: {len(records)}条记录, 耗时: {load_time:.3f}秒")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            return False
    
    @lru_cache(maxsize=128)
    def get_frequency_analysis_cached(self, digit_position: str = "all") -> str:
        """
        缓存版频率分析
        
        Args:
            digit_position: 分析位置
            
        Returns:
            序列化的分析结果
        """
        if self.lazy_df is None:
            return "{}"
        
        start_time = time.time()
        result = {}
        
        try:
            if digit_position in ["all", "hundreds"]:
                # 使用lazy evaluation优化百位数字频率查询
                hundreds_freq = (
                    self.lazy_df
                    .with_columns(
                        pl.col("number_list").list.get(0).alias("hundreds")
                    )
                    .group_by("hundreds")
                    .agg(pl.count().alias("count"))
                    .sort("count", descending=True)
                    .collect(streaming=self._streaming_enabled)
                )
                result["hundreds"] = hundreds_freq.to_dicts()
                
            if digit_position in ["all", "tens"]:
                # 优化十位数字频率查询
                tens_freq = (
                    self.lazy_df
                    .with_columns(
                        pl.col("number_list").list.get(1).alias("tens")
                    )
                    .group_by("tens")
                    .agg(pl.count().alias("count"))
                    .sort("count", descending=True)
                    .collect(streaming=self._streaming_enabled)
                )
                result["tens"] = tens_freq.to_dicts()
                
            if digit_position in ["all", "units"]:
                # 优化个位数字频率查询
                units_freq = (
                    self.lazy_df
                    .with_columns(
                        pl.col("number_list").list.get(2).alias("units")
                    )
                    .group_by("units")
                    .agg(pl.count().alias("count"))
                    .sort("count", descending=True)
                    .collect(streaming=self._streaming_enabled)
                )
                result["units"] = units_freq.to_dicts()
            
            query_time = time.time() - start_time
            self._query_times.append(query_time)
            self._cache_misses += 1
            
            self.logger.debug(f"频率分析查询耗时: {query_time:.3f}秒")
            
            # 序列化结果用于缓存
            import json
            return json.dumps(result)
            
        except Exception as e:
            self.logger.error(f"频率分析失败: {e}")
            return "{}"
    
    def get_frequency_analysis(self, digit_position: str = "all") -> Dict[str, Any]:
        """
        获取频率分析（带缓存优化）
        
        Args:
            digit_position: 分析位置
            
        Returns:
            频率分析结果
        """
        if not self._cache_enabled:
            return self._get_frequency_analysis_direct(digit_position)
        
        try:
            # 使用缓存版本
            cached_result = self.get_frequency_analysis_cached(digit_position)
            self._cache_hits += 1
            
            import json
            return json.loads(cached_result)
            
        except Exception as e:
            self.logger.warning(f"缓存查询失败，使用直接查询: {e}")
            return self._get_frequency_analysis_direct(digit_position)
    
    def _get_frequency_analysis_direct(self, digit_position: str = "all") -> Dict[str, Any]:
        """直接频率分析（无缓存）"""
        if self.df is None:
            return {}
        
        result = {}
        
        if digit_position in ["all", "hundreds"]:
            hundreds_freq = (
                self.df.with_columns(
                    pl.col("number_list").list.get(0).alias("hundreds")
                )
                .group_by("hundreds")
                .agg(pl.count().alias("count"))
                .sort("count", descending=True)
            )
            result["hundreds"] = hundreds_freq.to_dicts()
            
        if digit_position in ["all", "tens"]:
            tens_freq = (
                self.df.with_columns(
                    pl.col("number_list").list.get(1).alias("tens")
                )
                .group_by("tens")
                .agg(pl.count().alias("count"))
                .sort("count", descending=True)
            )
            result["tens"] = tens_freq.to_dicts()
            
        if digit_position in ["all", "units"]:
            units_freq = (
                self.df.with_columns(
                    pl.col("number_list").list.get(2).alias("units")
                )
                .group_by("units")
                .agg(pl.count().alias("count"))
                .sort("count", descending=True)
            )
            result["units"] = units_freq.to_dicts()
            
        return result
    
    def get_optimized_basic_stats(self) -> Dict[str, Any]:
        """
        获取优化的基础统计信息
        
        Returns:
            统计信息
        """
        if self.lazy_df is None:
            return {}
        
        start_time = time.time()
        
        try:
            # 使用单次查询获取多个统计信息
            stats = (
                self.lazy_df
                .select([
                    pl.count().alias("total_count"),
                    pl.col("sum_value").mean().alias("avg_sum"),
                    pl.col("sum_value").std().alias("std_sum"),
                    pl.col("sum_value").min().alias("min_sum"),
                    pl.col("sum_value").max().alias("max_sum"),
                    pl.col("span_value").mean().alias("avg_span"),
                    pl.col("span_value").std().alias("std_span"),
                    pl.col("sales_amount").sum().alias("total_sales"),
                    pl.col("sales_amount").mean().alias("avg_sales")
                ])
                .collect(streaming=self._streaming_enabled)
            )
            
            query_time = time.time() - start_time
            self._query_times.append(query_time)
            
            result = stats.to_dicts()[0] if len(stats) > 0 else {}
            
            self.logger.debug(f"基础统计查询耗时: {query_time:.3f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"基础统计查询失败: {e}")
            return {}
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            性能指标
        """
        if not self._query_times:
            return {"message": "暂无查询记录"}
        
        avg_query_time = sum(self._query_times) / len(self._query_times)
        max_query_time = max(self._query_times)
        min_query_time = min(self._query_times)
        
        cache_hit_rate = self._cache_hits / (self._cache_hits + self._cache_misses) if (self._cache_hits + self._cache_misses) > 0 else 0
        
        return {
            "total_queries": len(self._query_times),
            "avg_query_time_ms": round(avg_query_time * 1000, 2),
            "max_query_time_ms": round(max_query_time * 1000, 2),
            "min_query_time_ms": round(min_query_time * 1000, 2),
            "cache_hits": self._cache_hits,
            "cache_misses": self._cache_misses,
            "cache_hit_rate": round(cache_hit_rate * 100, 2),
            "streaming_enabled": self._streaming_enabled,
            "cache_enabled": self._cache_enabled
        }
    
    def clear_cache(self):
        """清理缓存"""
        self.get_frequency_analysis_cached.cache_clear()
        self._cache_hits = 0
        self._cache_misses = 0
        self.logger.info("缓存已清理")
    
    def enable_cache(self, enabled: bool = True):
        """启用/禁用缓存"""
        self._cache_enabled = enabled
        self.logger.info(f"缓存{'启用' if enabled else '禁用'}")
    
    def enable_streaming(self, enabled: bool = True):
        """启用/禁用流式处理"""
        self._streaming_enabled = enabled
        self.logger.info(f"流式处理{'启用' if enabled else '禁用'}")
