#!/usr/bin/env python3
"""
Phase 3 阶段1测试：优化模型集成测试
"""

def test_optimized_model_integration():
    print("🧪 Phase 3 阶段1测试：优化模型集成")
    
    try:
        # 测试导入
        print("\n1. 测试模块导入...")
        from src.prediction.intelligent_fusion import IntelligentFusionSystem
        print("✅ IntelligentFusionSystem导入成功")
        
        # 测试系统初始化
        print("\n2. 测试系统初始化...")
        fusion = IntelligentFusionSystem()
        print("✅ 系统初始化成功")
        
        # 检查优化模型状态
        print("\n3. 检查优化模型集成状态...")
        print(f"   优化模型就绪: {fusion.optimized_models_ready}")
        print(f"   可用优化模型: {list(fusion.optimized_models.keys())}")
        print(f"   推理引擎: {list(fusion.inference_engines.keys())}")
        
        if fusion.optimized_models_ready:
            print("✅ Phase 2.5优化模型集成成功")
            
            # 获取模型信息
            if 'cnn_lstm' in fusion.optimized_models:
                model = fusion.optimized_models['cnn_lstm']
                model_info = model.get_model_info()
                print(f"   模型参数量: {model_info['total_parameters']:,}")
                print(f"   模型大小: {model_info['model_size_mb']:.2f} MB")
        else:
            print("⚠️ 优化模型未就绪，可能是导入问题")
        
        # 测试预测功能
        print("\n4. 测试融合预测功能...")
        test_data = ['123', '456', '789', '012', '345', '678', '901', '234', '567', '890']
        
        result = fusion.generate_fusion_prediction(
            data=test_data, 
            max_candidates=5,
            confidence_threshold=0.3
        )
        
        if 'error' not in result:
            print("✅ 融合预测测试成功")
            print(f"   候选数量: {len(result.get('candidates', []))}")
            print(f"   包含模型: {list(result.get('model_results', {}).keys())}")
            
            # 检查是否包含优化模型结果
            model_results = result.get('model_results', {})
            if 'optimized_cnn_lstm' in model_results:
                print("✅ 优化模型预测已集成到融合结果中")
                opt_result = model_results['optimized_cnn_lstm']
                print(f"   优化模型候选: {opt_result.get('candidates', [])[:3]}")
            else:
                print("⚠️ 优化模型预测未在融合结果中找到")
                
        else:
            print(f"❌ 融合预测失败: {result['error']}")
        
        # 测试单独的优化模型预测
        if fusion.optimized_models_ready:
            print("\n5. 测试单独优化模型预测...")
            opt_result = fusion.generate_optimized_predictions(test_data)
            
            if 'error' not in opt_result:
                print("✅ 优化模型单独预测成功")
                print(f"   候选数量: {len(opt_result.get('candidates', []))}")
                print(f"   前3个候选: {opt_result.get('candidates', [])[:3]}")
                print(f"   置信度: {opt_result.get('confidence_scores', [])[:3]}")
            else:
                print(f"❌ 优化模型预测失败: {opt_result['error']}")
        
        print("\n🎉 Phase 3 阶段1.1测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_optimized_model_integration()
